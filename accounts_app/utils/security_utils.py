# --- Django Imports ---
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
import re
import logging
from datetime import timed<PERSON>ta
from ipaddress import ip_address, ip_network

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """
    Get the real client IP address from request.
    
    Handles various proxy configurations and headers.
    """
    # Check for forwarded headers (common in load balancers/proxies)
    forwarded_headers = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP',
        'HTTP_CF_CONNECTING_IP',  # Cloudflare
        'HTTP_X_CLUSTER_CLIENT_IP',
    ]
    
    for header in forwarded_headers:
        ip_list = request.META.get(header)
        if ip_list:
            # Take the first IP in the list (original client)
            ip = ip_list.split(',')[0].strip()
            if is_valid_ip(ip):
                return ip
    
    # Fallback to REMOTE_ADDR
    return request.META.get('REMOTE_ADDR', '127.0.0.1')


def is_valid_ip(ip_string):
    """Check if string is a valid IP address."""
    try:
        ip_address(ip_string)
        return True
    except ValueError:
        return False


def is_private_ip(ip_string):
    """Check if IP address is private/internal."""
    try:
        ip = ip_address(ip_string)
        return ip.is_private or ip.is_loopback or ip.is_link_local
    except ValueError:
        return False


def is_suspicious_request(request):
    """
    Determine if a request appears suspicious.
    
    Factors considered:
    - Unusual user agent patterns
    - Suspicious headers
    - Request patterns
    - Geographic anomalies
    """
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    
    # Check for suspicious user agent patterns
    suspicious_ua_patterns = [
        r'bot',
        r'crawler',
        r'spider',
        r'scraper',
        r'curl',
        r'wget',
        r'python',
        r'requests',
        r'urllib',
        r'scanner',
        r'test',
    ]
    
    for pattern in suspicious_ua_patterns:
        if re.search(pattern, user_agent):
            return True
    
    # Check for missing or unusual headers
    if not user_agent:
        return True
    
    # Check for suspicious header combinations
    accept_header = request.META.get('HTTP_ACCEPT', '')
    if not accept_header or 'text/html' not in accept_header:
        # Non-browser request to authentication pages
        if any(path in request.path for path in ['/login/', '/signup/', '/password/']):
            return True
    
    # Check for rapid requests from same IP
    client_ip = get_client_ip(request)
    request_count_key = f"request_count:{client_ip}"
    request_count = cache.get(request_count_key, 0)
    
    if request_count > 50:  # More than 50 requests per minute
        return True
    
    cache.set(request_count_key, request_count + 1, 60)  # 1 minute window
    
    return False


def is_bot_request(request):
    """
    Determine if request is from a bot or automated system.
    
    More specific than is_suspicious_request, focuses on legitimate bots.
    """
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    
    # Known bot patterns
    bot_patterns = [
        r'googlebot',
        r'bingbot',
        r'slurp',  # Yahoo
        r'duckduckbot',
        r'baiduspider',
        r'yandexbot',
        r'facebookexternalhit',
        r'twitterbot',
        r'linkedinbot',
        r'whatsapp',
        r'telegrambot',
        r'applebot',
        r'amazonbot',
    ]
    
    for pattern in bot_patterns:
        if re.search(pattern, user_agent):
            return True
    
    return False


def check_rate_limit(request):
    """
    Check rate limits for the request with comprehensive protection.

    Features:
    - Multiple rate limit tiers (IP, user, global)
    - Progressive penalties for repeat offenders
    - Different limits for authenticated vs anonymous users
    - Whitelist support for trusted IPs

    Returns dict with 'blocked' status and additional info.
    """
    client_ip = get_client_ip(request)
    path = request.path
    method = request.method
    user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None

    # Check if IP is whitelisted
    if is_whitelisted_ip(client_ip):
        return {'blocked': False, 'whitelisted': True}

    # Get rate limit configuration
    rate_limit_config = get_rate_limit_config(path, user)
    if not rate_limit_config:
        return {'blocked': False}

    # Check multiple rate limit tiers
    rate_limit_results = []

    # 1. IP-based rate limiting
    ip_result = check_ip_rate_limit(client_ip, path, method, rate_limit_config)
    rate_limit_results.append(ip_result)

    # 2. User-based rate limiting (if authenticated)
    if user:
        user_result = check_user_rate_limit(user, path, method, rate_limit_config)
        rate_limit_results.append(user_result)

    # 3. Global rate limiting for sensitive operations
    if is_sensitive_operation(path):
        global_result = check_global_rate_limit(path, method, rate_limit_config)
        rate_limit_results.append(global_result)

    # 4. Progressive penalty check
    penalty_result = check_progressive_penalties(client_ip, user)
    if penalty_result['blocked']:
        rate_limit_results.append(penalty_result)

    # Return the most restrictive result
    for result in rate_limit_results:
        if result['blocked']:
            # Log rate limit violation
            log_security_event(
                'rate_limit_exceeded',
                user=user,
                request=request,
                details={
                    'limit_type': result.get('limit_type', 'unknown'),
                    'path': path,
                    'method': method,
                    'limit': result.get('limit'),
                    'current_count': result.get('current_count'),
                    'window': result.get('window'),
                }
            )
            return result

    # No rate limits exceeded
    return {'blocked': False, 'checks_passed': len(rate_limit_results)}


def get_rate_limit_config(path, user=None):
    """
    Get rate limit configuration for a path.

    Different limits for authenticated vs anonymous users.
    """
    # Base rate limits for anonymous users
    anonymous_limits = {
        '/accounts/login/': {'limit': 5, 'window': 300, 'progressive': True},
        '/accounts/signup/': {'limit': 3, 'window': 3600, 'progressive': True},
        '/accounts/password/reset/': {'limit': 3, 'window': 3600, 'progressive': True},
        '/accounts/email/verification/resend/': {'limit': 3, 'window': 300, 'progressive': False},
        '/accounts/customer/signup/': {'limit': 2, 'window': 3600, 'progressive': True},
        '/accounts/provider/signup/': {'limit': 2, 'window': 3600, 'progressive': True},
        '/accounts/password/change/': {'limit': 5, 'window': 900, 'progressive': True},
        '/accounts/email/change/': {'limit': 3, 'window': 1800, 'progressive': True},
    }

    # Higher limits for authenticated users
    authenticated_limits = {
        '/accounts/login/': {'limit': 10, 'window': 300, 'progressive': True},
        '/accounts/password/reset/': {'limit': 5, 'window': 3600, 'progressive': True},
        '/accounts/email/verification/resend/': {'limit': 5, 'window': 300, 'progressive': False},
        '/accounts/password/change/': {'limit': 10, 'window': 900, 'progressive': True},
        '/accounts/email/change/': {'limit': 5, 'window': 1800, 'progressive': True},
    }

    # Choose appropriate limits
    limits = authenticated_limits if user else anonymous_limits

    # Find matching path
    for limited_path, config in limits.items():
        if path.startswith(limited_path):
            return config

    return None


def check_ip_rate_limit(client_ip, path, method, config):
    """Check IP-based rate limiting."""
    cache_key = f"rate_limit:ip:{client_ip}:{path}:{method}"
    current_count = cache.get(cache_key, 0)

    if current_count >= config['limit']:
        ttl = cache.ttl(cache_key)
        return {
            'blocked': True,
            'limit_type': 'ip',
            'limit': config['limit'],
            'window': config['window'],
            'retry_after': ttl if ttl > 0 else config['window'],
            'current_count': current_count,
        }

    # Increment counter
    cache.set(cache_key, current_count + 1, config['window'])

    return {
        'blocked': False,
        'limit_type': 'ip',
        'current_count': current_count + 1,
    }


def check_user_rate_limit(user, path, method, config):
    """Check user-based rate limiting."""
    cache_key = f"rate_limit:user:{user.id}:{path}:{method}"
    current_count = cache.get(cache_key, 0)

    # Users get slightly higher limits
    user_limit = int(config['limit'] * 1.5)

    if current_count >= user_limit:
        ttl = cache.ttl(cache_key)
        return {
            'blocked': True,
            'limit_type': 'user',
            'limit': user_limit,
            'window': config['window'],
            'retry_after': ttl if ttl > 0 else config['window'],
            'current_count': current_count,
        }

    # Increment counter
    cache.set(cache_key, current_count + 1, config['window'])

    return {
        'blocked': False,
        'limit_type': 'user',
        'current_count': current_count + 1,
    }


def check_global_rate_limit(path, method, config):
    """Check global rate limiting for sensitive operations."""
    cache_key = f"rate_limit:global:{path}:{method}"
    current_count = cache.get(cache_key, 0)

    # Global limits are much higher
    global_limit = config['limit'] * 100  # 100x the individual limit

    if current_count >= global_limit:
        ttl = cache.ttl(cache_key)
        return {
            'blocked': True,
            'limit_type': 'global',
            'limit': global_limit,
            'window': config['window'],
            'retry_after': ttl if ttl > 0 else config['window'],
            'current_count': current_count,
        }

    # Increment counter
    cache.set(cache_key, current_count + 1, config['window'])

    return {
        'blocked': False,
        'limit_type': 'global',
        'current_count': current_count + 1,
    }


def check_progressive_penalties(client_ip, user=None):
    """
    Check for progressive penalties based on violation history.

    Progressive penalties increase restrictions for repeat offenders.
    """
    # Check violation history
    violation_key = f"violations:{client_ip}"
    violations = cache.get(violation_key, [])

    # Count recent violations (last 24 hours)
    from datetime import datetime, timedelta
    now = datetime.now()
    recent_violations = [
        v for v in violations
        if datetime.fromisoformat(v['timestamp']) > now - timedelta(hours=24)
    ]

    violation_count = len(recent_violations)

    # Apply progressive penalties
    if violation_count >= 10:  # 10+ violations in 24 hours
        return {
            'blocked': True,
            'limit_type': 'progressive_penalty',
            'penalty_level': 'severe',
            'violation_count': violation_count,
            'retry_after': 3600,  # 1 hour penalty
            'message': 'Account temporarily restricted due to repeated violations'
        }
    elif violation_count >= 5:  # 5+ violations in 24 hours
        return {
            'blocked': True,
            'limit_type': 'progressive_penalty',
            'penalty_level': 'moderate',
            'violation_count': violation_count,
            'retry_after': 1800,  # 30 minute penalty
            'message': 'Temporary restriction due to multiple violations'
        }
    elif violation_count >= 3:  # 3+ violations in 24 hours
        return {
            'blocked': True,
            'limit_type': 'progressive_penalty',
            'penalty_level': 'mild',
            'violation_count': violation_count,
            'retry_after': 600,  # 10 minute penalty
            'message': 'Brief restriction due to recent violations'
        }

    return {'blocked': False, 'violation_count': violation_count}


def record_rate_limit_violation(client_ip, user=None, violation_type='general', details=None):
    """Record a rate limit violation for progressive penalties."""
    violation_key = f"violations:{client_ip}"
    violations = cache.get(violation_key, [])

    # Add new violation
    violation = {
        'timestamp': datetime.now().isoformat(),
        'type': violation_type,
        'user_id': user.id if user else None,
        'details': details or {}
    }

    violations.append(violation)

    # Keep only last 50 violations
    violations = violations[-50:]

    # Store for 7 days
    cache.set(violation_key, violations, 7 * 24 * 3600)


def is_whitelisted_ip(client_ip):
    """Check if IP is whitelisted."""
    # Get whitelisted IPs from settings or cache
    whitelisted_ips = getattr(settings, 'RATE_LIMIT_WHITELIST', [])

    # Add common development IPs
    if settings.DEBUG:
        whitelisted_ips.extend(['127.0.0.1', '::1', 'localhost'])

    return client_ip in whitelisted_ips


def is_sensitive_operation(path):
    """Check if path represents a sensitive operation."""
    sensitive_paths = [
        '/accounts/login/',
        '/accounts/signup/',
        '/accounts/password/',
        '/accounts/email/',
        '/accounts/delete/',
        '/accounts/social/unlink/',
    ]

    return any(path.startswith(sensitive_path) for sensitive_path in sensitive_paths)


def get_rate_limit_status(request):
    """
    Get current rate limit status for debugging/monitoring.

    Returns detailed information about current rate limits.
    """
    client_ip = get_client_ip(request)
    user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None

    status = {
        'client_ip': client_ip,
        'user_id': user.id if user else None,
        'whitelisted': is_whitelisted_ip(client_ip),
        'limits': {},
        'violations': [],
    }

    # Check current limits for common paths
    common_paths = [
        '/accounts/login/',
        '/accounts/signup/',
        '/accounts/password/reset/',
        '/accounts/email/verification/resend/',
    ]

    for path in common_paths:
        config = get_rate_limit_config(path, user)
        if config:
            # Check IP limit
            ip_key = f"rate_limit:ip:{client_ip}:{path}:POST"
            ip_count = cache.get(ip_key, 0)

            status['limits'][path] = {
                'ip_count': ip_count,
                'ip_limit': config['limit'],
                'window': config['window'],
                'remaining': max(0, config['limit'] - ip_count),
            }

            # Check user limit if authenticated
            if user:
                user_key = f"rate_limit:user:{user.id}:{path}:POST"
                user_count = cache.get(user_key, 0)
                user_limit = int(config['limit'] * 1.5)

                status['limits'][path].update({
                    'user_count': user_count,
                    'user_limit': user_limit,
                    'user_remaining': max(0, user_limit - user_count),
                })

    # Get violation history
    violation_key = f"violations:{client_ip}"
    violations = cache.get(violation_key, [])
    status['violations'] = violations[-10:]  # Last 10 violations

    return status


def reset_rate_limits(client_ip=None, user=None, path=None):
    """
    Reset rate limits for debugging or administrative purposes.

    Use with caution - only for legitimate administrative needs.
    """
    if client_ip:
        # Reset IP-based limits
        if path:
            cache.delete(f"rate_limit:ip:{client_ip}:{path}:POST")
            cache.delete(f"rate_limit:ip:{client_ip}:{path}:GET")
        else:
            # Reset all IP limits (dangerous - use carefully)
            cache_keys = cache.keys(f"rate_limit:ip:{client_ip}:*")
            for key in cache_keys:
                cache.delete(key)

        # Reset violations
        cache.delete(f"violations:{client_ip}")

    if user:
        # Reset user-based limits
        if path:
            cache.delete(f"rate_limit:user:{user.id}:{path}:POST")
            cache.delete(f"rate_limit:user:{user.id}:{path}:GET")
        else:
            # Reset all user limits
            cache_keys = cache.keys(f"rate_limit:user:{user.id}:*")
            for key in cache_keys:
                cache.delete(key)

    # Log the reset for audit purposes
    log_security_event(
        'rate_limit_reset',
        user=user,
        details={
            'client_ip': client_ip,
            'path': path,
            'reset_type': 'administrative',
        }
    )


def log_security_event(event_type, user=None, request=None, details=None, is_suspicious=False):
    """
    Log a security event.
    
    Args:
        event_type: Type of security event
        user: User object (if applicable)
        request: HTTP request object (if applicable)
        details: Additional event details
        is_suspicious: Whether event is flagged as suspicious
    """
    try:
        from ..models.session_management import SessionSecurityEvent
        
        event_data = {
            'event_type': event_type,
            'user': user,
            'is_suspicious': is_suspicious,
        }
        
        if request:
            event_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        if details:
            event_data['details'] = details
        
        # Create the event
        event = SessionSecurityEvent.objects.create(**event_data)
        
        # Log to Django logger as well
        log_level = logging.WARNING if is_suspicious else logging.INFO
        logger.log(
            log_level,
            f"Security event: {event_type}",
            extra={
                'event_id': event.id,
                'user_id': user.id if user else None,
                'ip_address': event_data.get('ip_address'),
                'is_suspicious': is_suspicious,
                'details': details,
            }
        )
        
        return event
        
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")
        return None


def check_password_strength(password):
    """
    Check password strength and return score and feedback.
    
    Returns:
        dict: {
            'score': int (0-100),
            'feedback': list of strings,
            'is_strong': bool
        }
    """
    score = 0
    feedback = []
    
    # Length check
    if len(password) >= 8:
        score += 20
    else:
        feedback.append("Password should be at least 8 characters long")
    
    if len(password) >= 12:
        score += 10
    
    # Character variety checks
    if re.search(r'[a-z]', password):
        score += 15
    else:
        feedback.append("Include lowercase letters")
    
    if re.search(r'[A-Z]', password):
        score += 15
    else:
        feedback.append("Include uppercase letters")
    
    if re.search(r'\d', password):
        score += 15
    else:
        feedback.append("Include numbers")
    
    if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        score += 15
    else:
        feedback.append("Include special characters")
    
    # Pattern checks
    if not re.search(r'(.)\1{2,}', password):  # No 3+ repeated characters
        score += 5
    else:
        feedback.append("Avoid repeating characters")
    
    if not re.search(r'(012|123|234|345|456|567|678|789|890)', password):  # No sequential numbers
        score += 5
    else:
        feedback.append("Avoid sequential numbers")
    
    # Common password check (basic)
    common_passwords = [
        'password', '123456', 'qwerty', 'abc123', 'password123',
        'admin', 'letmein', 'welcome', 'monkey', 'dragon'
    ]
    
    if password.lower() not in common_passwords:
        score += 10
    else:
        feedback.append("Avoid common passwords")
    
    return {
        'score': min(score, 100),
        'feedback': feedback,
        'is_strong': score >= 70
    }


def validate_email_security(email):
    """
    Validate email for security concerns.
    
    Returns:
        dict: {
            'is_valid': bool,
            'warnings': list of strings,
            'is_disposable': bool
        }
    """
    warnings = []
    is_disposable = False
    
    # Basic email format validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return {
            'is_valid': False,
            'warnings': ['Invalid email format'],
            'is_disposable': False
        }
    
    domain = email.split('@')[1].lower()
    
    # Check for disposable email domains (basic list)
    disposable_domains = [
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
        'mailinator.com', 'yopmail.com', 'temp-mail.org',
        'throwaway.email', 'getnada.com', 'maildrop.cc'
    ]
    
    if domain in disposable_domains:
        is_disposable = True
        warnings.append("Disposable email addresses are not recommended")
    
    # Check for suspicious patterns
    if re.search(r'\d{4,}', email):  # Many consecutive numbers
        warnings.append("Email contains suspicious number patterns")
    
    if len(email.split('@')[0]) < 3:  # Very short local part
        warnings.append("Email local part is very short")
    
    return {
        'is_valid': True,
        'warnings': warnings,
        'is_disposable': is_disposable
    }


def get_security_recommendations(user):
    """
    Get security recommendations for a user.
    
    Returns:
        list: List of recommendation dictionaries
    """
    recommendations = []
    
    # Check if user has password
    if not user.has_usable_password():
        recommendations.append({
            'type': 'critical',
            'title': 'Set a Password',
            'description': 'You only have social login. Set a password as backup.',
            'action_url': '/accounts/password/set/',
            'action_text': 'Set Password'
        })
    
    # Check for social accounts
    from allauth.socialaccount.models import SocialAccount
    social_accounts = SocialAccount.objects.filter(user=user)
    
    if not social_accounts.exists():
        recommendations.append({
            'type': 'medium',
            'title': 'Connect Social Accounts',
            'description': 'Link social accounts for easier sign-in.',
            'action_url': '/accounts/social/',
            'action_text': 'Manage Social Accounts'
        })
    
    # Check last password change (if we track this)
    if hasattr(user, 'last_password_change'):
        if user.last_password_change:
            days_since_change = (timezone.now() - user.last_password_change).days
            if days_since_change > 90:
                recommendations.append({
                    'type': 'medium',
                    'title': 'Update Password',
                    'description': f'Password last changed {days_since_change} days ago.',
                    'action_url': '/accounts/password/change/',
                    'action_text': 'Change Password'
                })
    
    # Check for multiple active sessions
    from ..models.session_management import UserSession
    active_sessions = UserSession.objects.filter(user=user, is_active=True).count()
    
    if active_sessions > 3:
        recommendations.append({
            'type': 'low',
            'title': 'Review Active Sessions',
            'description': f'You have {active_sessions} active sessions.',
            'action_url': '/accounts/sessions/',
            'action_text': 'Manage Sessions'
        })
    
    return recommendations
