# --- Django Imports ---
from django.conf import settings
from django.core.cache import cache
from django.http import JsonResponse
from django.middleware.csrf import get_token
from django.template.context_processors import csrf
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect, ensure_csrf_cookie
from django.contrib import messages
import logging

# --- Local App Imports ---
from .security_utils import get_client_ip, log_security_event

logger = logging.getLogger(__name__)


def get_csrf_token(request):
    """
    Get CSRF token for the request.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        str: CSRF token
    """
    return get_token(request)


def validate_csrf_token(request, token):
    """
    Validate CSRF token against request.
    
    Args:
        request: Django HttpRequest object
        token: CSRF token to validate
        
    Returns:
        bool: True if token is valid
    """
    expected_token = get_token(request)
    return token == expected_token


def add_csrf_to_context(request):
    """
    Add CSRF token to template context.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        dict: Context with CSRF token
    """
    return csrf(request)


def csrf_failure_handler(request, reason=""):
    """
    Custom CSRF failure handler with enhanced logging.
    
    Args:
        request: Django HttpRequest object
        reason: Reason for CSRF failure
        
    Returns:
        HttpResponse: Error response
    """
    client_ip = get_client_ip(request)
    
    # Log CSRF failure
    log_security_event(
        'csrf_failure',
        request=request,
        details={
            'reason': reason,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    # Track failures for rate limiting
    failure_key = f"csrf_failures:{client_ip}"
    failure_count = cache.get(failure_key, 0) + 1
    cache.set(failure_key, failure_count, 3600)  # 1 hour
    
    # Check if this is an AJAX request
    is_ajax = (
        request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
        'application/json' in request.headers.get('Accept', '')
    )
    
    if is_ajax:
        return JsonResponse({
            'error': 'csrf_failed',
            'message': 'Security validation failed. Please refresh the page and try again.',
            'code': 'csrf_token_invalid',
        }, status=403)
    else:
        # Add error message for regular requests
        messages.error(
            request,
            'Security validation failed. Please refresh the page and try again.'
        )
        from django.shortcuts import render
        return render(request, 'accounts/csrf_failure.html', {
            'reason': reason,
            'failure_count': failure_count,
        }, status=403)


class CSRFProtectionMixin:
    """
    Mixin to add CSRF protection to class-based views.
    """
    
    @method_decorator(csrf_protect)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class EnsureCSRFCookieMixin:
    """
    Mixin to ensure CSRF cookie is set for class-based views.
    """
    
    @method_decorator(ensure_csrf_cookie)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


def csrf_exempt_ajax(view_func):
    """
    Decorator to exempt AJAX requests from CSRF protection.
    Use with caution and only for specific endpoints.
    """
    def wrapped_view(request, *args, **kwargs):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            from django.views.decorators.csrf import csrf_exempt
            return csrf_exempt(view_func)(request, *args, **kwargs)
        else:
            return csrf_protect(view_func)(request, *args, **kwargs)
    
    return wrapped_view


def validate_double_submit_csrf(request):
    """
    Validate double-submit CSRF pattern.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        bool: True if validation passes
    """
    # Get CSRF token from header
    csrf_header = (
        request.META.get('HTTP_X_CSRFTOKEN') or 
        request.META.get('HTTP_X_CSRF_TOKEN')
    )
    
    # Get CSRF token from cookie
    csrf_cookie = request.COOKIES.get(settings.CSRF_COOKIE_NAME)
    
    # Both must be present and match
    if not csrf_header or not csrf_cookie:
        return False
    
    return csrf_header == csrf_cookie


def get_csrf_failure_count(request):
    """
    Get CSRF failure count for client IP.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        int: Number of CSRF failures
    """
    client_ip = get_client_ip(request)
    return cache.get(f"csrf_failures:{client_ip}", 0)


def reset_csrf_failure_count(request):
    """
    Reset CSRF failure count for client IP.
    
    Args:
        request: Django HttpRequest object
    """
    client_ip = get_client_ip(request)
    cache.delete(f"csrf_failures:{client_ip}")


def is_csrf_rate_limited(request):
    """
    Check if client is rate limited due to CSRF failures.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        bool: True if rate limited
    """
    failure_count = get_csrf_failure_count(request)
    return failure_count >= 10  # Rate limit after 10 failures


def generate_csrf_meta_tag(request):
    """
    Generate CSRF meta tag for HTML head.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        str: HTML meta tag
    """
    token = get_token(request)
    return f'<meta name="csrf-token" content="{token}">'


def generate_csrf_input(request):
    """
    Generate CSRF input field for forms.
    
    Args:
        request: Django HttpRequest object
        
    Returns:
        str: HTML input field
    """
    token = get_token(request)
    return f'<input type="hidden" name="csrfmiddlewaretoken" value="{token}">'


# CSRF configuration for settings
CSRF_SETTINGS = {
    'CSRF_COOKIE_SECURE': not settings.DEBUG,
    'CSRF_COOKIE_HTTPONLY': True,
    'CSRF_COOKIE_SAMESITE': 'Lax',
    'CSRF_COOKIE_AGE': ********,  # 1 year
    'CSRF_USE_SESSIONS': False,
    'CSRF_TRUSTED_ORIGINS': [],  # Add your trusted origins
    'CSRF_FAILURE_VIEW': 'accounts_app.utils.csrf_utils.csrf_failure_handler',
}
