# --- Standard Library Imports ---
import re
import hashlib
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# --- Django Imports ---
from django.conf import settings
from django.core.cache import cache
from django.contrib.auth.hashers import check_password, make_password
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models.security import PasswordHistory

logger = logging.getLogger(__name__)


class PasswordSecurityManager:
    """
    Comprehensive password security management.
    
    Features:
    - Password strength validation
    - Password history tracking
    - Breach checking
    - Common password detection
    - Password policy enforcement
    """
    
    def __init__(self):
        self.min_length = getattr(settings, 'PASSWORD_MIN_LENGTH', 8)
        self.max_length = getattr(settings, 'PASSWORD_MAX_LENGTH', 128)
        self.require_uppercase = getattr(settings, 'PASSWORD_REQUIRE_UPPERCASE', True)
        self.require_lowercase = getattr(settings, 'PASSWORD_REQUIRE_LOWERCASE', True)
        self.require_numbers = getattr(settings, 'PASSWORD_REQUIRE_NUMBERS', True)
        self.require_symbols = getattr(settings, 'PASSWORD_REQUIRE_SYMBOLS', True)
        self.history_count = getattr(settings, 'PASSWORD_HISTORY_COUNT', 5)
        self.enable_breach_check = getattr(settings, 'PASSWORD_ENABLE_BREACH_CHECK', True)
        
        # Common passwords list (top 100 most common)
        self.common_passwords = self._load_common_passwords()
        
        # Breach check API settings
        self.breach_api_url = "https://api.pwnedpasswords.com/range/"
        self.breach_check_timeout = 5  # seconds
    
    def validate_password_strength(self, password: str, user=None) -> Dict:
        """
        Comprehensive password strength validation.
        
        Args:
            password: Password to validate
            user: User object for personalized validation
            
        Returns:
            Dict with validation results
        """
        result = {
            'is_valid': True,
            'score': 0,
            'strength_level': 'weak',
            'feedback': [],
            'errors': [],
            'warnings': [],
        }
        
        if not password:
            result['is_valid'] = False
            result['errors'].append(_('Password is required'))
            return result
        
        # Length validation
        length_result = self._validate_length(password)
        result['score'] += length_result['score']
        result['feedback'].extend(length_result['feedback'])
        if length_result['errors']:
            result['is_valid'] = False
            result['errors'].extend(length_result['errors'])
        
        # Character variety validation
        variety_result = self._validate_character_variety(password)
        result['score'] += variety_result['score']
        result['feedback'].extend(variety_result['feedback'])
        if variety_result['errors']:
            result['is_valid'] = False
            result['errors'].extend(variety_result['errors'])
        
        # Pattern validation
        pattern_result = self._validate_patterns(password)
        result['score'] += pattern_result['score']
        result['feedback'].extend(pattern_result['feedback'])
        result['warnings'].extend(pattern_result['warnings'])
        
        # Common password check
        common_result = self._check_common_password(password)
        result['score'] += common_result['score']
        if common_result['is_common']:
            result['is_valid'] = False
            result['errors'].append(_('This password is too common'))
        
        # Personal information check
        if user:
            personal_result = self._check_personal_information(password, user)
            result['score'] += personal_result['score']
            result['warnings'].extend(personal_result['warnings'])
        
        # Breach check
        if self.enable_breach_check:
            breach_result = self._check_password_breach(password)
            if breach_result['is_breached']:
                result['is_valid'] = False
                result['errors'].append(
                    _('This password has been found in data breaches and is not secure')
                )
                result['warnings'].append(
                    f"Found in {breach_result['breach_count']} breaches"
                )
        
        # Calculate strength level
        result['strength_level'] = self._calculate_strength_level(result['score'])
        
        return result
    
    def check_password_history(self, user, new_password: str) -> bool:
        """
        Check if password was used recently.
        
        Args:
            user: User object
            new_password: New password to check
            
        Returns:
            True if password is in history (should be rejected)
        """
        if not user or not user.id:
            return False
        
        # Get recent password history
        recent_passwords = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[:self.history_count]
        
        # Check against each historical password
        for password_entry in recent_passwords:
            if check_password(new_password, password_entry.password_hash):
                return True
        
        return False
    
    def save_password_to_history(self, user, password: str):
        """
        Save password to history.
        
        Args:
            user: User object
            password: Password to save
        """
        if not user or not user.id:
            return
        
        # Create password hash
        password_hash = make_password(password)
        
        # Save to history
        PasswordHistory.objects.create(
            user=user,
            password_hash=password_hash
        )
        
        # Clean up old history entries
        old_entries = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[self.history_count:]
        
        for entry in old_entries:
            entry.delete()
    
    def _validate_length(self, password: str) -> Dict:
        """Validate password length."""
        result = {'score': 0, 'feedback': [], 'errors': []}
        
        length = len(password)
        
        if length < self.min_length:
            result['errors'].append(
                _(f'Password must be at least {self.min_length} characters long')
            )
        elif length >= self.min_length:
            result['score'] += 20
            if length >= 12:
                result['score'] += 10
                result['feedback'].append('Good length')
            if length >= 16:
                result['score'] += 5
                result['feedback'].append('Excellent length')
        
        if length > self.max_length:
            result['errors'].append(
                _(f'Password cannot be longer than {self.max_length} characters')
            )
        
        return result
    
    def _validate_character_variety(self, password: str) -> Dict:
        """Validate character variety in password."""
        result = {'score': 0, 'feedback': [], 'errors': []}
        
        has_lowercase = bool(re.search(r'[a-z]', password))
        has_uppercase = bool(re.search(r'[A-Z]', password))
        has_numbers = bool(re.search(r'\d', password))
        has_symbols = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
        
        # Check requirements
        if self.require_lowercase and not has_lowercase:
            result['errors'].append(_('Password must contain lowercase letters'))
        elif has_lowercase:
            result['score'] += 15
        
        if self.require_uppercase and not has_uppercase:
            result['errors'].append(_('Password must contain uppercase letters'))
        elif has_uppercase:
            result['score'] += 15
        
        if self.require_numbers and not has_numbers:
            result['errors'].append(_('Password must contain numbers'))
        elif has_numbers:
            result['score'] += 15
        
        if self.require_symbols and not has_symbols:
            result['errors'].append(_('Password must contain special characters'))
        elif has_symbols:
            result['score'] += 15
        
        # Bonus for variety
        variety_count = sum([has_lowercase, has_uppercase, has_numbers, has_symbols])
        if variety_count >= 3:
            result['score'] += 10
            result['feedback'].append('Good character variety')
        
        return result
    
    def _validate_patterns(self, password: str) -> Dict:
        """Validate password patterns."""
        result = {'score': 0, 'feedback': [], 'warnings': []}
        
        # Check for repeated characters
        if not re.search(r'(.)\1{2,}', password):
            result['score'] += 5
        else:
            result['warnings'].append('Avoid repeating characters')
        
        # Check for sequential patterns
        sequential_patterns = [
            r'(012|123|234|345|456|567|678|789|890)',
            r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',
            r'(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)',
        ]
        
        has_sequential = False
        for pattern in sequential_patterns:
            if re.search(pattern, password.lower()):
                has_sequential = True
                break
        
        if not has_sequential:
            result['score'] += 5
        else:
            result['warnings'].append('Avoid sequential patterns')
        
        # Check for keyboard patterns
        keyboard_patterns = ['qwerty', 'asdf', 'zxcv', '1234', 'abcd']
        has_keyboard_pattern = any(pattern in password.lower() for pattern in keyboard_patterns)
        
        if not has_keyboard_pattern:
            result['score'] += 5
        else:
            result['warnings'].append('Avoid keyboard patterns')
        
        return result
    
    def _check_common_password(self, password: str) -> Dict:
        """Check if password is in common passwords list."""
        result = {'score': 0, 'is_common': False}
        
        if password.lower() in self.common_passwords:
            result['is_common'] = True
        else:
            result['score'] += 10
        
        return result
    
    def _check_personal_information(self, password: str, user) -> Dict:
        """Check if password contains personal information."""
        result = {'score': 0, 'warnings': []}
        
        # Get user information
        personal_info = []
        if hasattr(user, 'email') and user.email:
            personal_info.append(user.email.split('@')[0].lower())
        if hasattr(user, 'first_name') and user.first_name:
            personal_info.append(user.first_name.lower())
        if hasattr(user, 'last_name') and user.last_name:
            personal_info.append(user.last_name.lower())
        if hasattr(user, 'username') and user.username:
            personal_info.append(user.username.lower())
        
        # Check if password contains personal information
        password_lower = password.lower()
        contains_personal = False
        
        for info in personal_info:
            if len(info) >= 3 and info in password_lower:
                contains_personal = True
                break
        
        if not contains_personal:
            result['score'] += 10
        else:
            result['warnings'].append('Avoid using personal information in passwords')

        return result

    def _check_password_breach(self, password: str) -> Dict:
        """
        Check if password has been found in data breaches using HaveIBeenPwned API.

        Args:
            password: Password to check

        Returns:
            Dict with breach information
        """
        result = {'is_breached': False, 'breach_count': 0}

        try:
            # Create SHA-1 hash of password
            sha1_hash = hashlib.sha1(password.encode('utf-8')).hexdigest().upper()

            # Use k-anonymity: send only first 5 characters
            prefix = sha1_hash[:5]
            suffix = sha1_hash[5:]

            # Check cache first
            cache_key = f"breach_check:{prefix}"
            cached_result = cache.get(cache_key)

            if cached_result is None:
                # Make API request
                response = requests.get(
                    f"{self.breach_api_url}{prefix}",
                    timeout=self.breach_check_timeout
                )

                if response.status_code == 200:
                    cached_result = response.text
                    # Cache for 24 hours
                    cache.set(cache_key, cached_result, 24 * 3600)
                else:
                    logger.warning(f"Breach check API returned status {response.status_code}")
                    return result

            # Parse response
            for line in cached_result.split('\n'):
                if line.strip():
                    hash_suffix, count = line.strip().split(':')
                    if hash_suffix == suffix:
                        result['is_breached'] = True
                        result['breach_count'] = int(count)
                        break

        except requests.RequestException as e:
            logger.warning(f"Breach check API request failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error during breach check: {str(e)}")

        return result

    def _calculate_strength_level(self, score: int) -> str:
        """Calculate password strength level based on score."""
        if score >= 80:
            return 'very_strong'
        elif score >= 60:
            return 'strong'
        elif score >= 40:
            return 'moderate'
        elif score >= 20:
            return 'weak'
        else:
            return 'very_weak'

    def _load_common_passwords(self) -> set:
        """Load common passwords list."""
        # Top 100 most common passwords
        common_passwords = {
            'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', 'dragon', 'master', 'hello',
            'freedom', 'whatever', 'qazwsx', 'trustno1', 'jordan', 'harley',
            'robert', 'matthew', 'jordan23', 'daniel', 'andrew', 'joshua',
            'michelle', 'jessica', 'amanda', 'kimberly', 'ashley', 'jennifer',
            'nicole', 'melissa', 'heather', 'stephanie', 'joseph', 'charles',
            'thomas', 'christopher', 'anthony', 'william', 'donald', 'kenneth',
            'paul', 'mark', 'steven', 'andrew', 'joshua', 'kenneth', 'paul',
            'mark', 'steven', 'edward', 'brian', 'ronald', 'anthony', 'kevin',
            'jason', 'matthew', 'gary', 'timothy', 'jose', 'larry', 'jeffrey',
            'frank', 'scott', 'eric', 'stephen', 'andrew', 'raymond', 'gregory',
            'joshua', 'jerry', 'dennis', 'walter', 'patrick', 'peter', 'harold',
            'douglas', 'henry', 'carl', 'arthur', 'ryan', 'roger', 'joe',
            'juan', 'jack', 'albert', 'jonathan', 'justin', 'terry', 'gerald',
            'keith', 'samuel', 'willie', 'ralph', 'lawrence', 'nicholas',
            'roy', 'benjamin', 'bruce', 'brandon', 'adam', 'harry', 'fred',
            'wayne', 'billy', 'steve', 'louis', 'jeremy', 'aaron', 'mike',
            'johnny', 'howard', 'eugene', 'carlos', 'russell', 'bobby',
            'victor', 'martin', 'ernest', 'phillip', 'todd', 'jesse', 'craig',
            'alan', 'shawn', 'clarence', 'sean', 'philip', 'chris', 'johnny',
        }

        return common_passwords

    def generate_password_policy_text(self) -> str:
        """Generate human-readable password policy text."""
        policy_parts = []

        policy_parts.append(f"Password must be {self.min_length}-{self.max_length} characters long")

        requirements = []
        if self.require_lowercase:
            requirements.append("lowercase letters")
        if self.require_uppercase:
            requirements.append("uppercase letters")
        if self.require_numbers:
            requirements.append("numbers")
        if self.require_symbols:
            requirements.append("special characters")

        if requirements:
            policy_parts.append(f"Must contain: {', '.join(requirements)}")

        policy_parts.append("Cannot be a common password")
        policy_parts.append(f"Cannot reuse last {self.history_count} passwords")

        if self.enable_breach_check:
            policy_parts.append("Cannot be found in data breaches")

        return ". ".join(policy_parts) + "."


# Global password security manager instance
password_security = PasswordSecurityManager()


def validate_password(password: str, user=None) -> Dict:
    """
    Convenience function for password validation.

    Args:
        password: Password to validate
        user: User object for personalized validation

    Returns:
        Dict with validation results
    """
    return password_security.validate_password_strength(password, user)


def check_password_history(user, password: str) -> bool:
    """
    Convenience function for password history check.

    Args:
        user: User object
        password: Password to check

    Returns:
        True if password is in history
    """
    return password_security.check_password_history(user, password)


def save_password_to_history(user, password: str):
    """
    Convenience function to save password to history.

    Args:
        user: User object
        password: Password to save
    """
    password_security.save_password_to_history(user, password)


def get_password_policy_text() -> str:
    """
    Get human-readable password policy text.

    Returns:
        Password policy description
    """
    return password_security.generate_password_policy_text()
