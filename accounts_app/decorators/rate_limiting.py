# --- Standard Library Imports ---
from functools import wraps
from datetime import datetime, timedelta

# --- Django Imports ---
from django.core.cache import cache
from django.http import JsonResponse, HttpResponseForbidden
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import reverse
from django.conf import settings

# --- Local App Imports ---
from ..utils.security_utils import (
    get_client_ip,
    log_security_event,
    check_rate_limit,
    record_rate_limit_violation,
)


def rate_limit(limit=5, window=300, key_func=None, methods=None, skip_if=None):
    """
    Comprehensive rate limiting decorator.
    
    Args:
        limit (int): Maximum number of requests allowed
        window (int): Time window in seconds
        key_func (callable): Function to generate cache key
        methods (list): HTTP methods to apply rate limiting to
        skip_if (callable): Function to determine if rate limiting should be skipped
    
    Usage:
        @rate_limit(limit=5, window=300)
        def my_view(request):
            pass
        
        @rate_limit(limit=3, window=3600, methods=['POST'])
        def signup_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Check if rate limiting should be skipped
            if skip_if and skip_if(request):
                return view_func(request, *args, **kwargs)
            
            # Check if method should be rate limited
            if methods and request.method not in methods:
                return view_func(request, *args, **kwargs)
            
            # Generate cache key
            if key_func:
                cache_key = key_func(request)
            else:
                client_ip = get_client_ip(request)
                view_name = view_func.__name__
                cache_key = f"rate_limit:decorator:{client_ip}:{view_name}:{request.method}"
            
            # Check current count
            current_count = cache.get(cache_key, 0)
            
            if current_count >= limit:
                # Rate limit exceeded
                record_rate_limit_violation(
                    get_client_ip(request),
                    user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                    violation_type='decorator_rate_limit',
                    details={
                        'view': view_func.__name__,
                        'limit': limit,
                        'window': window,
                        'count': current_count,
                    }
                )
                
                # Log the violation
                log_security_event(
                    'rate_limit_exceeded',
                    user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                    request=request,
                    details={
                        'view': view_func.__name__,
                        'limit': limit,
                        'window': window,
                        'current_count': current_count,
                        'cache_key': cache_key,
                    }
                )
                
                # Return appropriate response
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': 'rate_limit_exceeded',
                        'message': f'Too many requests. Please try again in {window} seconds.',
                        'retry_after': window,
                        'limit': limit,
                        'current_count': current_count,
                    }, status=429)
                else:
                    messages.error(
                        request,
                        f'Too many requests. Please wait {window // 60} minutes before trying again.'
                    )
                    return redirect('accounts_app:rate_limit_exceeded')
            
            # Increment counter
            cache.set(cache_key, current_count + 1, window)
            
            # Call the original view
            return view_func(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


def login_rate_limit(limit=5, window=300):
    """Specialized rate limiting for login views."""
    def key_func(request):
        client_ip = get_client_ip(request)
        # Include email in key if provided for more granular limiting
        email = request.POST.get('email', request.POST.get('login', ''))
        if email:
            return f"login_rate_limit:{client_ip}:{email}"
        return f"login_rate_limit:{client_ip}"
    
    def skip_if(request):
        # Skip for whitelisted IPs
        from ..utils.security_utils import is_whitelisted_ip
        return is_whitelisted_ip(get_client_ip(request))
    
    return rate_limit(
        limit=limit,
        window=window,
        key_func=key_func,
        methods=['POST'],
        skip_if=skip_if
    )


def signup_rate_limit(limit=3, window=3600):
    """Specialized rate limiting for signup views."""
    def key_func(request):
        client_ip = get_client_ip(request)
        email = request.POST.get('email', '')
        if email:
            return f"signup_rate_limit:{client_ip}:{email}"
        return f"signup_rate_limit:{client_ip}"
    
    return rate_limit(
        limit=limit,
        window=window,
        key_func=key_func,
        methods=['POST']
    )


def password_reset_rate_limit(limit=3, window=3600):
    """Specialized rate limiting for password reset views."""
    def key_func(request):
        client_ip = get_client_ip(request)
        email = request.POST.get('email', '')
        if email:
            return f"password_reset_rate_limit:{client_ip}:{email}"
        return f"password_reset_rate_limit:{client_ip}"
    
    return rate_limit(
        limit=limit,
        window=window,
        key_func=key_func,
        methods=['POST']
    )


def email_verification_rate_limit(limit=3, window=300):
    """Specialized rate limiting for email verification resend."""
    def key_func(request):
        client_ip = get_client_ip(request)
        user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else 'anonymous'
        return f"email_verification_rate_limit:{client_ip}:{user_id}"
    
    return rate_limit(
        limit=limit,
        window=window,
        key_func=key_func,
        methods=['POST']
    )


def api_rate_limit(limit=60, window=60):
    """Rate limiting for API endpoints."""
    def key_func(request):
        client_ip = get_client_ip(request)
        user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else 'anonymous'
        return f"api_rate_limit:{client_ip}:{user_id}"
    
    def skip_if(request):
        # Skip for authenticated users with API keys (if implemented)
        return hasattr(request, 'api_key') and request.api_key
    
    return rate_limit(
        limit=limit,
        window=window,
        key_func=key_func,
        skip_if=skip_if
    )


def progressive_rate_limit(base_limit=5, base_window=300, max_multiplier=8):
    """
    Progressive rate limiting that increases restrictions for repeat offenders.
    
    The limit becomes more restrictive based on violation history.
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            client_ip = get_client_ip(request)
            
            # Get violation history
            violation_key = f"violations:{client_ip}"
            violations = cache.get(violation_key, [])
            
            # Count recent violations (last hour)
            now = datetime.now()
            recent_violations = [
                v for v in violations 
                if datetime.fromisoformat(v['timestamp']) > now - timedelta(hours=1)
            ]
            
            # Calculate progressive multiplier
            violation_count = len(recent_violations)
            multiplier = min(2 ** (violation_count // 3), max_multiplier)
            
            # Apply progressive limits
            current_limit = max(1, base_limit // multiplier)
            current_window = base_window * multiplier
            
            # Use the standard rate limit with calculated values
            rate_limit_decorator = rate_limit(
                limit=current_limit,
                window=current_window,
                methods=['POST']
            )
            
            return rate_limit_decorator(view_func)(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


class RateLimitMixin:
    """
    Mixin for class-based views to add rate limiting.
    
    Usage:
        class MyView(RateLimitMixin, View):
            rate_limit_config = {
                'limit': 5,
                'window': 300,
                'methods': ['POST'],
            }
    """
    rate_limit_config = {
        'limit': 10,
        'window': 300,
        'methods': ['POST', 'PUT', 'PATCH', 'DELETE'],
    }
    
    def dispatch(self, request, *args, **kwargs):
        # Apply rate limiting
        config = self.rate_limit_config
        
        if request.method in config.get('methods', ['POST']):
            # Check rate limit
            result = check_rate_limit(request)
            if result['blocked']:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': 'rate_limit_exceeded',
                        'message': 'Too many requests. Please try again later.',
                        'retry_after': result.get('retry_after', 300),
                    }, status=429)
                else:
                    messages.error(request, 'Too many requests. Please try again later.')
                    return redirect('accounts_app:rate_limit_exceeded')
        
        return super().dispatch(request, *args, **kwargs)
