# --- Standard Library Imports ---
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# --- Django Imports ---
from django.utils import timezone
from django.urls import reverse
from django.conf import settings

# --- Local App Imports ---
from ..models import CustomerProfile, ServiceProviderProfile, ProfileCompletionMilestone


class ProfileOnboardingService:
    """
    Service for managing progressive profile completion workflows.
    
    Features:
    - Guided onboarding flows
    - Smart field suggestions
    - Completion incentives
    - Progressive disclosure
    """
    
    ONBOARDING_STEPS = {
        'customer': [
            {
                'id': 'welcome',
                'title': 'Welcome to CozyWish!',
                'description': 'Let\'s get your profile set up so you can start booking amazing services.',
                'fields': [],
                'completion_weight': 0,
                'is_required': True,
            },
            {
                'id': 'basic_info',
                'title': 'Basic Information',
                'description': 'Tell us a bit about yourself.',
                'fields': ['first_name', 'last_name'],
                'completion_weight': 30,
                'is_required': True,
            },
            {
                'id': 'contact_info',
                'title': 'Contact Information',
                'description': 'How can service providers reach you?',
                'fields': ['phone_number'],
                'completion_weight': 25,
                'is_required': True,
            },
            {
                'id': 'location',
                'title': 'Your Location',
                'description': 'Help us find services near you.',
                'fields': ['address', 'city', 'zip_code'],
                'completion_weight': 20,
                'is_required': False,
            },
            {
                'id': 'profile_picture',
                'title': 'Profile Picture',
                'description': 'Add a photo to personalize your profile.',
                'fields': ['profile_picture'],
                'completion_weight': 15,
                'is_required': False,
            },
            {
                'id': 'preferences',
                'title': 'Your Preferences',
                'description': 'Tell us about your preferences for better recommendations.',
                'fields': ['birth_month', 'birth_year'],
                'completion_weight': 10,
                'is_required': False,
            },
        ],
        'service_provider': [
            {
                'id': 'welcome',
                'title': 'Welcome to CozyWish Business!',
                'description': 'Let\'s set up your business profile to start attracting customers.',
                'fields': [],
                'completion_weight': 0,
                'is_required': True,
            },
            {
                'id': 'business_basics',
                'title': 'Business Information',
                'description': 'Tell us about your business.',
                'fields': ['legal_name', 'display_name'],
                'completion_weight': 25,
                'is_required': True,
            },
            {
                'id': 'contact_details',
                'title': 'Contact Details',
                'description': 'How can customers reach your business?',
                'fields': ['phone', 'contact_name'],
                'completion_weight': 20,
                'is_required': True,
            },
            {
                'id': 'business_location',
                'title': 'Business Location',
                'description': 'Where is your business located?',
                'fields': ['address', 'city', 'state', 'zip_code'],
                'completion_weight': 20,
                'is_required': True,
            },
            {
                'id': 'business_description',
                'title': 'About Your Business',
                'description': 'Describe your business and services.',
                'fields': ['description'],
                'completion_weight': 15,
                'is_required': False,
            },
            {
                'id': 'branding',
                'title': 'Business Branding',
                'description': 'Upload your logo and add your website.',
                'fields': ['logo', 'website'],
                'completion_weight': 10,
                'is_required': False,
            },
            {
                'id': 'social_media',
                'title': 'Social Media',
                'description': 'Connect your social media accounts.',
                'fields': ['instagram', 'facebook'],
                'completion_weight': 10,
                'is_required': False,
            },
        ]
    }
    
    def __init__(self, user):
        self.user = user
        self.profile = self._get_profile()
        self.user_type = 'customer' if user.is_customer else 'service_provider'
        self.steps = self.ONBOARDING_STEPS[self.user_type]
    
    def _get_profile(self):
        """Get the user's profile."""
        if self.user.is_customer:
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.user.is_service_provider:
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def get_onboarding_status(self) -> Dict[str, Any]:
        """
        Get current onboarding status and progress.
        
        Returns:
            dict: Onboarding status information
        """
        if not self.profile:
            return {
                'is_complete': False,
                'current_step': 0,
                'total_steps': len(self.steps),
                'completion_percentage': 0,
                'steps': [],
            }
        
        # Check completion status for each step
        completed_steps = 0
        current_step_index = 0
        step_statuses = []
        
        for i, step in enumerate(self.steps):
            step_completion = self._check_step_completion(step)
            step_statuses.append({
                'id': step['id'],
                'title': step['title'],
                'description': step['description'],
                'is_completed': step_completion['is_completed'],
                'completion_percentage': step_completion['completion_percentage'],
                'missing_fields': step_completion['missing_fields'],
                'is_required': step['is_required'],
                'is_current': False,
            })
            
            if step_completion['is_completed']:
                completed_steps += 1
            elif current_step_index == 0:  # First incomplete step
                current_step_index = i
                step_statuses[i]['is_current'] = True
        
        # If all steps are complete, mark as complete
        if completed_steps == len(self.steps):
            current_step_index = len(self.steps)
        
        # Calculate overall completion percentage
        total_weight = sum(step['completion_weight'] for step in self.steps)
        completed_weight = sum(
            step['completion_weight'] for i, step in enumerate(self.steps)
            if step_statuses[i]['is_completed']
        )
        completion_percentage = int((completed_weight / total_weight) * 100) if total_weight > 0 else 100
        
        return {
            'is_complete': completed_steps == len(self.steps),
            'current_step': current_step_index,
            'total_steps': len(self.steps),
            'completed_steps': completed_steps,
            'completion_percentage': completion_percentage,
            'steps': step_statuses,
            'next_step': self._get_next_step_info(current_step_index),
        }
    
    def _check_step_completion(self, step: Dict) -> Dict[str, Any]:
        """
        Check if a specific onboarding step is completed.
        
        Args:
            step: Step configuration
            
        Returns:
            dict: Step completion status
        """
        if not step['fields']:  # Welcome step or similar
            return {
                'is_completed': True,
                'completion_percentage': 100,
                'missing_fields': [],
            }
        
        completed_fields = 0
        missing_fields = []
        
        for field_name in step['fields']:
            value = getattr(self.profile, field_name, None)
            if value:
                completed_fields += 1
            else:
                missing_fields.append(field_name)
        
        completion_percentage = int((completed_fields / len(step['fields'])) * 100)
        is_completed = completion_percentage == 100
        
        return {
            'is_completed': is_completed,
            'completion_percentage': completion_percentage,
            'missing_fields': missing_fields,
        }
    
    def _get_next_step_info(self, current_step_index: int) -> Optional[Dict[str, Any]]:
        """
        Get information about the next step to complete.
        
        Args:
            current_step_index: Index of current step
            
        Returns:
            dict or None: Next step information
        """
        if current_step_index >= len(self.steps):
            return None
        
        step = self.steps[current_step_index]
        step_completion = self._check_step_completion(step)
        
        return {
            'id': step['id'],
            'title': step['title'],
            'description': step['description'],
            'fields': step['fields'],
            'missing_fields': step_completion['missing_fields'],
            'is_required': step['is_required'],
            'completion_percentage': step_completion['completion_percentage'],
            'action_url': self._get_step_action_url(step['id']),
            'estimated_time': self._estimate_step_time(step),
        }
    
    def _get_step_action_url(self, step_id: str) -> str:
        """
        Get the URL for completing a specific step.
        
        Args:
            step_id: Step identifier
            
        Returns:
            str: URL for step completion
        """
        url_mapping = {
            'welcome': reverse('dashboard_app:customer_dashboard'),
            'basic_info': reverse('accounts_app:customer_profile'),
            'contact_info': reverse('accounts_app:customer_profile'),
            'location': reverse('accounts_app:customer_profile'),
            'profile_picture': reverse('accounts_app:customer_profile'),
            'preferences': reverse('accounts_app:customer_profile'),
            'business_basics': reverse('accounts_app:service_provider_profile'),
            'contact_details': reverse('accounts_app:service_provider_profile'),
            'business_location': reverse('accounts_app:service_provider_profile'),
            'business_description': reverse('accounts_app:service_provider_profile'),
            'branding': reverse('accounts_app:service_provider_profile'),
            'social_media': reverse('accounts_app:service_provider_profile'),
        }
        
        return url_mapping.get(step_id, '#')
    
    def _estimate_step_time(self, step: Dict) -> str:
        """
        Estimate time to complete a step.
        
        Args:
            step: Step configuration
            
        Returns:
            str: Estimated time
        """
        field_count = len(step['fields'])
        
        if field_count == 0:
            return '1 minute'
        elif field_count <= 2:
            return '2-3 minutes'
        elif field_count <= 4:
            return '3-5 minutes'
        else:
            return '5-10 minutes'
    
    def get_smart_suggestions(self) -> List[Dict[str, Any]]:
        """
        Get smart suggestions for profile completion.
        
        Returns:
            list: Smart suggestions
        """
        suggestions = []
        
        if not self.profile:
            return suggestions
        
        # Get profile completion suggestions
        profile_suggestions = self.profile.get_completion_suggestions()
        
        # Enhance with onboarding context
        onboarding_status = self.get_onboarding_status()
        
        for suggestion in profile_suggestions:
            # Add onboarding context
            suggestion['onboarding_step'] = self._find_step_for_fields(suggestion['fields'])
            suggestion['estimated_time'] = self._estimate_suggestion_time(suggestion)
            suggestion['completion_boost'] = self._calculate_completion_boost(suggestion['fields'])
            
            suggestions.append(suggestion)
        
        # Add step-specific suggestions
        if not onboarding_status['is_complete']:
            next_step = onboarding_status['next_step']
            if next_step and next_step['missing_fields']:
                suggestions.insert(0, {
                    'priority': 'high',
                    'message': f"Complete '{next_step['title']}' to continue your onboarding",
                    'fields': next_step['missing_fields'],
                    'action': 'complete_onboarding_step',
                    'onboarding_step': next_step['id'],
                    'estimated_time': next_step['estimated_time'],
                    'completion_boost': self._calculate_completion_boost(next_step['missing_fields']),
                })
        
        return suggestions
    
    def _find_step_for_fields(self, fields: List[str]) -> Optional[str]:
        """
        Find which onboarding step contains the given fields.
        
        Args:
            fields: List of field names
            
        Returns:
            str or None: Step ID
        """
        for step in self.steps:
            if any(field in step['fields'] for field in fields):
                return step['id']
        return None
    
    def _estimate_suggestion_time(self, suggestion: Dict) -> str:
        """
        Estimate time to complete a suggestion.
        
        Args:
            suggestion: Suggestion dictionary
            
        Returns:
            str: Estimated time
        """
        field_count = len(suggestion.get('fields', []))
        
        if suggestion['action'] == 'upload_picture':
            return '2-3 minutes'
        elif suggestion['action'] == 'complete_name':
            return '1 minute'
        elif suggestion['action'] == 'add_phone':
            return '1-2 minutes'
        elif suggestion['action'] == 'complete_address':
            return '3-5 minutes'
        else:
            return f'{field_count * 2} minutes'
    
    def _calculate_completion_boost(self, fields: List[str]) -> int:
        """
        Calculate how much completing these fields would boost completion percentage.
        
        Args:
            fields: List of field names
            
        Returns:
            int: Percentage boost
        """
        # This is a simplified calculation
        # In practice, you'd use the actual completion calculation logic
        return len(fields) * 5  # Rough estimate
    
    def mark_step_completed(self, step_id: str) -> bool:
        """
        Mark a specific onboarding step as completed.
        
        Args:
            step_id: Step identifier
            
        Returns:
            bool: Whether step was marked as completed
        """
        try:
            # Check if step is actually completed
            step = next((s for s in self.steps if s['id'] == step_id), None)
            if not step:
                return False
            
            step_completion = self._check_step_completion(step)
            if step_completion['is_completed']:
                # Award milestone if applicable
                milestone_mapping = {
                    'basic_info': 'basic_info',
                    'contact_info': 'contact_info',
                    'profile_picture': 'profile_picture',
                    'branding': 'profile_picture',  # Business logo
                }
                
                milestone_type = milestone_mapping.get(step_id)
                if milestone_type:
                    ProfileCompletionMilestone.check_and_award_milestones(
                        self.user, self.profile
                    )
                
                return True
            
            return False
            
        except Exception:
            return False
    
    def get_completion_incentives(self) -> List[Dict[str, Any]]:
        """
        Get completion incentives and rewards.
        
        Returns:
            list: Available incentives
        """
        incentives = []
        onboarding_status = self.get_onboarding_status()
        
        # Completion percentage incentives
        if onboarding_status['completion_percentage'] >= 50 and onboarding_status['completion_percentage'] < 75:
            incentives.append({
                'type': 'progress',
                'title': 'Halfway There!',
                'description': 'You\'re 50% complete. Finish your profile to unlock all features.',
                'icon': 'fas fa-star-half-alt',
                'color': 'warning',
            })
        
        elif onboarding_status['completion_percentage'] >= 75 and onboarding_status['completion_percentage'] < 100:
            incentives.append({
                'type': 'progress',
                'title': 'Almost Done!',
                'description': 'You\'re 75% complete. Just a few more steps to go!',
                'icon': 'fas fa-star',
                'color': 'info',
            })
        
        elif onboarding_status['completion_percentage'] == 100:
            incentives.append({
                'type': 'completion',
                'title': 'Profile Complete!',
                'description': 'Congratulations! Your profile is 100% complete.',
                'icon': 'fas fa-trophy',
                'color': 'success',
            })
        
        # Feature unlock incentives
        if self.user_type == 'customer':
            if onboarding_status['completion_percentage'] >= 60:
                incentives.append({
                    'type': 'feature',
                    'title': 'Booking Unlocked',
                    'description': 'You can now book services with verified providers.',
                    'icon': 'fas fa-calendar-check',
                    'color': 'primary',
                })
        
        elif self.user_type == 'service_provider':
            if onboarding_status['completion_percentage'] >= 70:
                incentives.append({
                    'type': 'feature',
                    'title': 'Venue Creation Unlocked',
                    'description': 'You can now create venues and list your services.',
                    'icon': 'fas fa-store',
                    'color': 'primary',
                })
        
        return incentives
