# --- Standard Library Imports ---
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# --- Django Imports ---
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.urls import reverse

# --- Local App Imports ---
from ..models import CustomerProfile, ServiceProviderProfile, ProfileChangeHistory


class ProfileVerificationService:
    """
    Service for managing profile verification processes.
    
    Features:
    - Automated verification checks
    - Manual verification workflows
    - Verification badges and status tracking
    - Verification history and audit trail
    """
    
    VERIFICATION_CRITERIA = {
        'customer': {
            'email_verified': {'weight': 30, 'required': True},
            'phone_verified': {'weight': 25, 'required': False},
            'profile_complete': {'weight': 20, 'required': False},
            'identity_documents': {'weight': 15, 'required': False},
            'address_verified': {'weight': 10, 'required': False},
        },
        'service_provider': {
            'email_verified': {'weight': 20, 'required': True},
            'phone_verified': {'weight': 20, 'required': True},
            'business_documents': {'weight': 25, 'required': True},
            'profile_complete': {'weight': 15, 'required': False},
            'address_verified': {'weight': 10, 'required': False},
            'insurance_verified': {'weight': 10, 'required': False},
        }
    }
    
    def __init__(self, user):
        self.user = user
        self.profile = self._get_profile()
        self.user_type = 'customer' if user.is_customer else 'service_provider'
    
    def _get_profile(self):
        """Get the user's profile."""
        if self.user.is_customer:
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.user.is_service_provider:
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def check_verification_status(self) -> Dict[str, Any]:
        """
        Check current verification status and calculate verification score.
        
        Returns:
            dict: Verification status information
        """
        if not self.profile:
            return {
                'is_verified': False,
                'verification_score': 0,
                'missing_criteria': ['Profile not found'],
                'next_steps': ['Create profile'],
            }
        
        criteria = self.VERIFICATION_CRITERIA[self.user_type]
        verification_score = 0
        max_score = sum(criterion['weight'] for criterion in criteria.values())
        missing_criteria = []
        completed_criteria = []
        
        # Check each verification criterion
        for criterion_name, criterion_info in criteria.items():
            is_met = self._check_criterion(criterion_name)
            
            if is_met:
                verification_score += criterion_info['weight']
                completed_criteria.append(criterion_name)
            else:
                missing_criteria.append(criterion_name)
        
        # Calculate percentage
        verification_percentage = int((verification_score / max_score) * 100)
        
        # Determine if profile is verified
        required_criteria_met = all(
            self._check_criterion(name) 
            for name, info in criteria.items() 
            if info['required']
        )
        
        # Profile is verified if all required criteria are met and score is above threshold
        is_verified = required_criteria_met and verification_percentage >= 80
        
        return {
            'is_verified': is_verified,
            'verification_score': verification_score,
            'verification_percentage': verification_percentage,
            'max_score': max_score,
            'completed_criteria': completed_criteria,
            'missing_criteria': missing_criteria,
            'required_criteria_met': required_criteria_met,
            'next_steps': self._get_next_steps(missing_criteria),
            'verification_badges': self._get_verification_badges(completed_criteria),
        }
    
    def _check_criterion(self, criterion_name: str) -> bool:
        """
        Check if a specific verification criterion is met.
        
        Args:
            criterion_name: Name of the criterion to check
            
        Returns:
            bool: Whether the criterion is met
        """
        if criterion_name == 'email_verified':
            # Check if email is verified through django-allauth
            try:
                from allauth.account.models import EmailAddress
                return EmailAddress.objects.filter(
                    user=self.user,
                    verified=True,
                    primary=True
                ).exists()
            except ImportError:
                # Fallback if allauth not available
                return self.user.is_active
        
        elif criterion_name == 'phone_verified':
            # Check if phone number is verified
            if self.user_type == 'customer':
                return bool(self.profile.phone_number)  # Simplified check
            else:
                return bool(self.profile.phone)  # Simplified check
        
        elif criterion_name == 'profile_complete':
            # Check if profile is sufficiently complete
            completion_percentage = self.profile.profile_completion_percentage
            return completion_percentage >= 80
        
        elif criterion_name == 'identity_documents':
            # Check if identity documents are uploaded (placeholder)
            # This would integrate with a document verification system
            return False  # Not implemented yet
        
        elif criterion_name == 'business_documents':
            # Check if business documents are uploaded (placeholder)
            # This would integrate with a document verification system
            return False  # Not implemented yet
        
        elif criterion_name == 'address_verified':
            # Check if address is verified
            if self.user_type == 'customer':
                return bool(self.profile.address and self.profile.city and self.profile.zip_code)
            else:
                return bool(self.profile.address and self.profile.city and self.profile.zip_code)
        
        elif criterion_name == 'insurance_verified':
            # Check if insurance is verified (placeholder)
            return False  # Not implemented yet
        
        return False
    
    def _get_next_steps(self, missing_criteria: List[str]) -> List[Dict[str, str]]:
        """
        Get next steps for completing verification.
        
        Args:
            missing_criteria: List of missing verification criteria
            
        Returns:
            list: List of next step dictionaries
        """
        next_steps = []
        
        step_mapping = {
            'email_verified': {
                'title': 'Verify Email Address',
                'description': 'Check your email and click the verification link',
                'action': 'verify_email',
                'priority': 'high'
            },
            'phone_verified': {
                'title': 'Verify Phone Number',
                'description': 'Add and verify your phone number',
                'action': 'verify_phone',
                'priority': 'high'
            },
            'profile_complete': {
                'title': 'Complete Profile',
                'description': 'Fill in all required profile information',
                'action': 'complete_profile',
                'priority': 'medium'
            },
            'identity_documents': {
                'title': 'Upload Identity Documents',
                'description': 'Upload government-issued ID for verification',
                'action': 'upload_documents',
                'priority': 'medium'
            },
            'business_documents': {
                'title': 'Upload Business Documents',
                'description': 'Upload business license and registration documents',
                'action': 'upload_business_docs',
                'priority': 'high'
            },
            'address_verified': {
                'title': 'Verify Address',
                'description': 'Complete your address information',
                'action': 'verify_address',
                'priority': 'low'
            },
            'insurance_verified': {
                'title': 'Verify Insurance',
                'description': 'Upload proof of business insurance',
                'action': 'verify_insurance',
                'priority': 'medium'
            },
        }
        
        for criterion in missing_criteria:
            if criterion in step_mapping:
                next_steps.append(step_mapping[criterion])
        
        # Sort by priority
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        next_steps.sort(key=lambda x: priority_order.get(x['priority'], 3))
        
        return next_steps
    
    def _get_verification_badges(self, completed_criteria: List[str]) -> List[Dict[str, str]]:
        """
        Get verification badges based on completed criteria.
        
        Args:
            completed_criteria: List of completed verification criteria
            
        Returns:
            list: List of badge dictionaries
        """
        badges = []
        
        badge_mapping = {
            'email_verified': {
                'name': 'Email Verified',
                'icon': 'fas fa-envelope-check',
                'color': 'success',
                'description': 'Email address has been verified'
            },
            'phone_verified': {
                'name': 'Phone Verified',
                'icon': 'fas fa-phone-check',
                'color': 'success',
                'description': 'Phone number has been verified'
            },
            'profile_complete': {
                'name': 'Profile Complete',
                'icon': 'fas fa-user-check',
                'color': 'info',
                'description': 'Profile is fully completed'
            },
            'identity_documents': {
                'name': 'Identity Verified',
                'icon': 'fas fa-id-card',
                'color': 'primary',
                'description': 'Identity documents verified'
            },
            'business_documents': {
                'name': 'Business Verified',
                'icon': 'fas fa-building-check',
                'color': 'primary',
                'description': 'Business documents verified'
            },
            'address_verified': {
                'name': 'Address Verified',
                'icon': 'fas fa-map-marker-check',
                'color': 'info',
                'description': 'Address has been verified'
            },
            'insurance_verified': {
                'name': 'Insured',
                'icon': 'fas fa-shield-check',
                'color': 'warning',
                'description': 'Business insurance verified'
            },
        }
        
        for criterion in completed_criteria:
            if criterion in badge_mapping:
                badges.append(badge_mapping[criterion])
        
        return badges
    
    def request_manual_verification(self, notes: str = '', documents: List = None) -> bool:
        """
        Request manual verification by admin.
        
        Args:
            notes: Additional notes for verification request
            documents: List of uploaded documents
            
        Returns:
            bool: Whether request was submitted successfully
        """
        try:
            # Log verification request
            ProfileChangeHistory.log_change(
                profile=self.profile,
                change_type='verify',
                change_summary=f'Manual verification requested: {notes}',
                changed_fields={'verification_requested': {'old': False, 'new': True}}
            )
            
            # Send notification to admins
            self._notify_admins_verification_request(notes, documents)
            
            return True
            
        except Exception as e:
            # Log error
            return False
    
    def approve_verification(self, admin_user, notes: str = '') -> bool:
        """
        Approve profile verification (admin action).
        
        Args:
            admin_user: Admin user approving verification
            notes: Verification notes
            
        Returns:
            bool: Whether verification was approved successfully
        """
        try:
            # Mark profile as verified
            self.profile.mark_verified(notes=notes, admin_user=admin_user)
            
            # Log verification approval
            ProfileChangeHistory.log_change(
                profile=self.profile,
                change_type='verify',
                change_summary=f'Profile verified by admin: {notes}',
                changed_by=admin_user,
                changed_fields={'is_verified': {'old': False, 'new': True}}
            )
            
            # Send confirmation email to user
            self._send_verification_confirmation_email()
            
            return True
            
        except Exception as e:
            return False
    
    def _notify_admins_verification_request(self, notes: str, documents: List):
        """Send notification to admins about verification request."""
        try:
            subject = f'Verification Request - {self.user.email}'
            message = f'''
            A user has requested manual verification:
            
            User: {self.user.email}
            User Type: {self.user_type.title()}
            Notes: {notes}
            
            Please review and approve/reject this verification request.
            '''
            
            # Send to admin emails
            admin_emails = getattr(settings, 'VERIFICATION_ADMIN_EMAILS', [])
            if admin_emails:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=admin_emails,
                    fail_silently=True,
                )
        except Exception:
            pass  # Fail silently for notifications
    
    def _send_verification_confirmation_email(self):
        """Send verification confirmation email to user."""
        try:
            subject = f'Profile Verified - {settings.SITE_NAME}'
            message = f'''
            Congratulations! Your profile has been verified.
            
            You now have access to all verified user features and your profile
            will display verification badges to other users.
            
            Thank you for completing the verification process.
            
            Best regards,
            The {settings.SITE_NAME} Team
            '''
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[self.user.email],
                fail_silently=True,
            )
        except Exception:
            pass  # Fail silently for notifications
