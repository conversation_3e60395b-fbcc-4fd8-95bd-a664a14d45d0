"""
Enhanced Data Export Service.

This module provides comprehensive data export functionality with multiple formats,
scheduled exports, export history tracking, and GDPR-compliant data portability.
"""

import json
import csv
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.files.base import ContentFile
from django.core.mail import EmailMessage
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
import io

from ..models import DataExportRequest, CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_info, log_error
from .data_export import DataExportService


class EnhancedDataExportService(DataExportService):
    """
    Enhanced data export service with additional features.
    
    New Features:
    - XML export format
    - Scheduled exports
    - Export history tracking
    - Advanced export options
    - Compression support
    - Export templates
    """
    
    SUPPORTED_FORMATS = ['json', 'csv', 'pdf', 'xml']
    COMPRESSION_FORMATS = ['zip', 'gzip']
    
    def __init__(self, user):
        super().__init__(user)
        self.export_request = None
    
    def create_export_request(self, export_format: str, data_types: List[str], 
                            options: Dict[str, Any] = None) -> 'DataExportRequest':
        """
        Create a new export request with tracking.
        
        Args:
            export_format: Format for export (json, csv, pdf, xml)
            data_types: Types of data to export
            options: Additional export options
            
        Returns:
            DataExportRequest: Created export request
        """
        self.export_request = DataExportRequest.objects.create(
            user=self.user,
            export_format=export_format,
            data_types=data_types,
            export_options=options or {},
            status='pending',
            requested_at=timezone.now(),
        )
        
        log_info(
            'Data export request created',
            user=self.user,
            extra_data={
                'export_id': self.export_request.id,
                'format': export_format,
                'data_types': data_types,
            }
        )
        
        return self.export_request
    
    def export_as_xml(self, data_types: List[str] = None) -> str:
        """
        Export data as XML string.
        
        Args:
            data_types: List of data types to include
            
        Returns:
            str: XML formatted data
        """
        data = self.collect_user_data(data_types)
        
        # Create root element
        root = ET.Element('user_data_export')
        root.set('version', '1.0')
        root.set('export_date', self.export_timestamp.isoformat())
        root.set('user_id', str(self.user.id))
        root.set('user_email', self.user.email)
        
        # Convert data to XML
        self._dict_to_xml(data, root)
        
        # Create XML string with declaration
        xml_str = ET.tostring(root, encoding='unicode', method='xml')
        return f'<?xml version="1.0" encoding="UTF-8"?>\n{xml_str}'
    
    def _dict_to_xml(self, data: Any, parent: ET.Element, key: str = None):
        """Convert dictionary to XML elements recursively."""
        if isinstance(data, dict):
            for k, v in data.items():
                # Create element with safe name
                safe_key = self._make_xml_safe(k)
                element = ET.SubElement(parent, safe_key)
                self._dict_to_xml(v, element, k)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                item_element = ET.SubElement(parent, 'item')
                item_element.set('index', str(i))
                self._dict_to_xml(item, item_element)
        else:
            parent.text = str(data) if data is not None else ''
    
    def _make_xml_safe(self, name: str) -> str:
        """Make string safe for XML element names."""
        # Replace invalid characters with underscores
        safe_name = ''.join(c if c.isalnum() or c == '_' else '_' for c in str(name))
        
        # Ensure it starts with a letter or underscore
        if safe_name and not (safe_name[0].isalpha() or safe_name[0] == '_'):
            safe_name = 'field_' + safe_name
        
        return safe_name or 'unnamed_field'
    
    def create_enhanced_export_file(self, format_type: str, data_types: List[str] = None,
                                  options: Dict[str, Any] = None) -> ContentFile:
        """
        Create export file with enhanced options.
        
        Args:
            format_type: Export format
            data_types: Data types to include
            options: Export options (compression, template, etc.)
            
        Returns:
            ContentFile: Enhanced export file
        """
        options = options or {}
        
        # Update export request status
        if self.export_request:
            self.export_request.status = 'processing'
            self.export_request.save()
        
        try:
            # Generate base filename
            timestamp = self.export_timestamp.strftime('%Y%m%d_%H%M%S')
            filename = f"data_export_{self.user.id}_{timestamp}"
            
            # Create content based on format
            if format_type == 'xml':
                content = self.export_as_xml(data_types)
                file_content = ContentFile(content.encode('utf-8'))
                file_content.name = f"{filename}.xml"
            else:
                # Use parent class for other formats
                file_content = super().create_export_file(format_type, data_types)
            
            # Apply compression if requested
            compression = options.get('compression')
            if compression in self.COMPRESSION_FORMATS:
                file_content = self._compress_file(file_content, compression)
            
            # Apply template if specified
            template = options.get('template')
            if template:
                file_content = self._apply_template(file_content, template, format_type)
            
            # Update export request with file info
            if self.export_request:
                self.export_request.file_size = len(file_content.read())
                self.export_request.file_name = file_content.name
                self.export_request.status = 'completed'
                self.export_request.completed_at = timezone.now()
                self.export_request.save()
                
                # Reset file pointer
                file_content.seek(0)
            
            return file_content
            
        except Exception as e:
            if self.export_request:
                self.export_request.status = 'failed'
                self.export_request.error_message = str(e)
                self.export_request.save()
            raise
    
    def _compress_file(self, file_content: ContentFile, compression: str) -> ContentFile:
        """Compress file content."""
        import gzip
        import zipfile
        
        file_content.seek(0)
        original_content = file_content.read()
        original_name = file_content.name
        
        if compression == 'gzip':
            compressed_content = gzip.compress(original_content)
            compressed_file = ContentFile(compressed_content)
            compressed_file.name = f"{original_name}.gz"
        elif compression == 'zip':
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                zip_file.writestr(original_name, original_content)
            
            compressed_file = ContentFile(zip_buffer.getvalue())
            compressed_file.name = f"{original_name.rsplit('.', 1)[0]}.zip"
        else:
            return file_content
        
        return compressed_file
    
    def _apply_template(self, file_content: ContentFile, template: str, 
                       format_type: str) -> ContentFile:
        """Apply export template to customize output."""
        # This would apply custom templates for different export formats
        # For now, return the original file
        return file_content
    
    def schedule_export(self, export_format: str, data_types: List[str],
                       schedule_type: str, schedule_options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Schedule a recurring data export.
        
        Args:
            export_format: Format for export
            data_types: Data types to export
            schedule_type: Type of schedule (daily, weekly, monthly)
            schedule_options: Schedule configuration
            
        Returns:
            dict: Scheduled export information
        """
        from ..models import ScheduledDataExport
        
        scheduled_export = ScheduledDataExport.objects.create(
            user=self.user,
            export_format=export_format,
            data_types=data_types,
            schedule_type=schedule_type,
            schedule_options=schedule_options,
            is_active=True,
            created_at=timezone.now(),
        )
        
        # Calculate next run time
        next_run = self._calculate_next_run(schedule_type, schedule_options)
        scheduled_export.next_run_at = next_run
        scheduled_export.save()
        
        log_info(
            'Data export scheduled',
            user=self.user,
            extra_data={
                'schedule_id': scheduled_export.id,
                'schedule_type': schedule_type,
                'next_run': next_run.isoformat(),
            }
        )
        
        return {
            'schedule_id': scheduled_export.id,
            'next_run_at': next_run,
            'schedule_type': schedule_type,
        }
    
    def _calculate_next_run(self, schedule_type: str, options: Dict[str, Any]) -> datetime:
        """Calculate next run time for scheduled export."""
        now = timezone.now()
        
        if schedule_type == 'daily':
            return now + timedelta(days=1)
        elif schedule_type == 'weekly':
            days_ahead = 7
            return now + timedelta(days=days_ahead)
        elif schedule_type == 'monthly':
            # Add one month (approximate)
            return now + timedelta(days=30)
        else:
            return now + timedelta(days=1)  # Default to daily
    
    def get_export_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get export history for the user.
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            list: Export history records
        """
        exports = DataExportRequest.objects.filter(
            user=self.user
        ).order_by('-requested_at')[:limit]
        
        history = []
        for export in exports:
            history.append({
                'id': export.id,
                'export_format': export.export_format,
                'data_types': export.data_types,
                'status': export.status,
                'requested_at': export.requested_at.isoformat(),
                'completed_at': export.completed_at.isoformat() if export.completed_at else None,
                'file_size': export.file_size,
                'file_name': export.file_name,
                'download_count': export.download_count,
                'expires_at': export.expires_at.isoformat() if export.expires_at else None,
                'error_message': export.error_message,
            })
        
        return history
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """Get export statistics for the user."""
        exports = DataExportRequest.objects.filter(user=self.user)
        
        total_exports = exports.count()
        completed_exports = exports.filter(status='completed').count()
        failed_exports = exports.filter(status='failed').count()
        
        # Calculate total data exported
        total_size = sum(
            export.file_size for export in exports.filter(status='completed')
            if export.file_size
        )
        
        # Format breakdown
        format_breakdown = {}
        for export in exports:
            format_type = export.export_format
            format_breakdown[format_type] = format_breakdown.get(format_type, 0) + 1
        
        return {
            'total_exports': total_exports,
            'completed_exports': completed_exports,
            'failed_exports': failed_exports,
            'success_rate': (completed_exports / total_exports * 100) if total_exports > 0 else 0,
            'total_data_size': total_size,
            'format_breakdown': format_breakdown,
            'last_export': exports.order_by('-requested_at').first().requested_at.isoformat() if exports.exists() else None,
        }
    
    def cleanup_expired_exports(self) -> int:
        """
        Clean up expired export files.
        
        Returns:
            int: Number of exports cleaned up
        """
        expired_exports = DataExportRequest.objects.filter(
            user=self.user,
            expires_at__lt=timezone.now(),
            status='completed'
        )
        
        count = expired_exports.count()
        
        # Mark as expired (don't delete the records for audit purposes)
        expired_exports.update(status='expired')
        
        log_info(
            f'Cleaned up {count} expired data exports',
            user=self.user,
            extra_data={'cleaned_count': count}
        )
        
        return count
    
    def download_export(self, export_id: int) -> HttpResponse:
        """
        Download a completed export file.
        
        Args:
            export_id: ID of the export to download
            
        Returns:
            HttpResponse: File download response
        """
        try:
            export = DataExportRequest.objects.get(
                id=export_id,
                user=self.user,
                status='completed'
            )
        except DataExportRequest.DoesNotExist:
            raise ValueError("Export not found or not available for download")
        
        # Check if export has expired
        if export.expires_at and export.expires_at < timezone.now():
            raise ValueError("Export has expired")
        
        # Increment download count
        export.download_count += 1
        export.last_downloaded_at = timezone.now()
        export.save()
        
        # Create response with file content
        # Note: In a real implementation, you'd retrieve the actual file content
        # from storage (filesystem, S3, etc.)
        response = HttpResponse(
            content_type='application/octet-stream'
        )
        response['Content-Disposition'] = f'attachment; filename="{export.file_name}"'
        
        log_info(
            'Data export downloaded',
            user=self.user,
            extra_data={
                'export_id': export_id,
                'download_count': export.download_count,
            }
        )
        
        return response


@login_required
@require_http_methods(["POST"])
def create_data_export_ajax(request):
    """
    AJAX endpoint for creating data export requests.
    """
    try:
        data = json.loads(request.body)
        export_format = data.get('export_format')
        data_types = data.get('data_types', [])
        options = data.get('options', {})

        if not export_format:
            return JsonResponse({
                'success': False,
                'error': 'Export format is required',
            }, status=400)

        export_service = EnhancedDataExportService(request.user)

        # Create export request
        export_request = export_service.create_export_request(
            export_format, data_types, options
        )

        # Process export asynchronously (in a real app, use Celery)
        try:
            export_file = export_service.create_enhanced_export_file(
                export_format, data_types, options
            )

            return JsonResponse({
                'success': True,
                'export_id': export_request.id,
                'status': export_request.status,
                'file_name': export_file.name,
                'file_size': len(export_file.read()),
            })

        except Exception as e:
            export_request.mark_as_failed(str(e))
            return JsonResponse({
                'success': False,
                'error': f'Export failed: {str(e)}',
                'export_id': export_request.id,
            }, status=500)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='data_export_ajax_error',
            error_message='Error creating data export via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to create data export',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def data_export_history_ajax(request):
    """
    AJAX endpoint for getting data export history.
    """
    try:
        limit = int(request.GET.get('limit', 50))
        export_service = EnhancedDataExportService(request.user)

        history = export_service.get_export_history(limit)
        statistics = export_service.get_export_statistics()

        return JsonResponse({
            'success': True,
            'history': history,
            'statistics': statistics,
        })

    except Exception as e:
        log_error(
            error_type='data_export_history_error',
            error_message='Error getting data export history',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to load export history',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def schedule_data_export_ajax(request):
    """
    AJAX endpoint for scheduling data exports.
    """
    try:
        data = json.loads(request.body)
        export_format = data.get('export_format')
        data_types = data.get('data_types', [])
        schedule_type = data.get('schedule_type')
        schedule_options = data.get('schedule_options', {})

        if not export_format or not schedule_type:
            return JsonResponse({
                'success': False,
                'error': 'Export format and schedule type are required',
            }, status=400)

        export_service = EnhancedDataExportService(request.user)

        schedule_info = export_service.schedule_export(
            export_format, data_types, schedule_type, schedule_options
        )

        return JsonResponse({
            'success': True,
            'schedule': schedule_info,
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='schedule_export_ajax_error',
            error_message='Error scheduling data export via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to schedule data export',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def download_data_export_ajax(request, export_id):
    """
    AJAX endpoint for downloading data exports.
    """
    try:
        export_service = EnhancedDataExportService(request.user)
        response = export_service.download_export(int(export_id))

        return response

    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
        }, status=404)
    except Exception as e:
        log_error(
            error_type='download_export_ajax_error',
            error_message='Error downloading data export via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to download export',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def cleanup_expired_exports_ajax(request):
    """
    AJAX endpoint for cleaning up expired exports.
    """
    try:
        export_service = EnhancedDataExportService(request.user)
        cleaned_count = export_service.cleanup_expired_exports()

        return JsonResponse({
            'success': True,
            'cleaned_count': cleaned_count,
        })

    except Exception as e:
        log_error(
            error_type='cleanup_exports_ajax_error',
            error_message='Error cleaning up expired exports via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to cleanup expired exports',
        }, status=500)
