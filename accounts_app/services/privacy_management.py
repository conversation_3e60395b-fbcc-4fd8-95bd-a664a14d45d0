"""
Enhanced Privacy Settings Management Service.

This module provides comprehensive privacy settings management with granular controls,
bulk updates, privacy impact analysis, and user-friendly privacy dashboard.
"""

from typing import Dict, List, Any, Optional, Tuple
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
import json

from ..models import ProfilePrivacySettings, PrivacyConsent, CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_info, log_error


class PrivacySettingsManager:
    """
    Advanced privacy settings management service.
    
    Features:
    - Granular privacy controls
    - Privacy impact analysis
    - Bulk privacy updates
    - Privacy recommendations
    - GDPR compliance tools
    """
    
    # Privacy impact levels
    PRIVACY_IMPACT_LEVELS = {
        'high': {
            'color': 'danger',
            'icon': 'fas fa-shield-alt',
            'description': 'High privacy protection - Limited visibility and data sharing'
        },
        'medium': {
            'color': 'warning',
            'icon': 'fas fa-balance-scale',
            'description': 'Balanced privacy - Good protection with reasonable functionality'
        },
        'low': {
            'color': 'info',
            'icon': 'fas fa-eye',
            'description': 'Open privacy - Maximum visibility and features'
        }
    }
    
    # Privacy presets for quick configuration
    PRIVACY_PRESETS = {
        'maximum_privacy': {
            'name': 'Maximum Privacy',
            'description': 'Highest privacy protection with minimal data sharing',
            'icon': 'fas fa-user-secret',
            'settings': {
                'profile_visibility': 'private',
                'show_name': False,
                'show_profile_picture': False,
                'show_email': False,
                'show_phone': False,
                'show_address': False,
                'contact_permission': 'none',
                'allow_marketing_contact': False,
                'data_sharing_level': 'none',
                'allow_usage_analytics': False,
                'searchable_profile': False,
                'show_in_recommendations': False,
            }
        },
        'balanced': {
            'name': 'Balanced Privacy',
            'description': 'Good privacy protection while maintaining functionality',
            'icon': 'fas fa-balance-scale',
            'settings': {
                'profile_visibility': 'limited',
                'show_name': True,
                'show_profile_picture': True,
                'show_email': False,
                'show_phone': False,
                'show_address': False,
                'contact_permission': 'verified',
                'allow_marketing_contact': False,
                'data_sharing_level': 'limited',
                'allow_usage_analytics': True,
                'searchable_profile': True,
                'show_in_recommendations': True,
            }
        },
        'open': {
            'name': 'Open Profile',
            'description': 'Maximum visibility and features for best experience',
            'icon': 'fas fa-globe',
            'settings': {
                'profile_visibility': 'public',
                'show_name': True,
                'show_profile_picture': True,
                'show_email': True,
                'show_phone': True,
                'show_address': True,
                'contact_permission': 'anyone',
                'allow_marketing_contact': True,
                'data_sharing_level': 'full',
                'allow_usage_analytics': True,
                'searchable_profile': True,
                'show_in_recommendations': True,
            }
        }
    }
    
    def __init__(self, user):
        """
        Initialize privacy settings manager.
        
        Args:
            user: User instance
        """
        self.user = user
        self.privacy_settings = self._get_or_create_privacy_settings()
    
    def _get_or_create_privacy_settings(self):
        """Get or create privacy settings for user."""
        settings, created = ProfilePrivacySettings.objects.get_or_create(
            user=self.user,
            defaults={
                'profile_visibility': 'public',
                'contact_permission': 'verified',
                'data_sharing_level': 'limited',
            }
        )
        return settings
    
    def get_privacy_dashboard_data(self) -> Dict[str, Any]:
        """
        Get comprehensive privacy dashboard data.
        
        Returns:
            dict: Privacy dashboard data
        """
        return {
            'current_settings': self._serialize_privacy_settings(),
            'privacy_score': self._calculate_privacy_score(),
            'privacy_impact': self._analyze_privacy_impact(),
            'recommendations': self._get_privacy_recommendations(),
            'presets': self.PRIVACY_PRESETS,
            'data_usage_summary': self._get_data_usage_summary(),
            'consent_status': self._get_consent_status(),
            'recent_changes': self._get_recent_privacy_changes(),
        }
    
    def _serialize_privacy_settings(self) -> Dict[str, Any]:
        """Serialize current privacy settings."""
        settings = {}
        
        # Get all privacy setting fields
        for field in self.privacy_settings._meta.fields:
            if field.name != 'user':
                value = getattr(self.privacy_settings, field.name)
                settings[field.name] = value
        
        return settings
    
    def _calculate_privacy_score(self) -> Dict[str, Any]:
        """Calculate privacy protection score (0-100)."""
        score = 0
        max_score = 0
        
        # Privacy scoring weights
        scoring_rules = {
            'profile_visibility': {'private': 20, 'limited': 15, 'public': 5, 'custom': 10},
            'show_name': {False: 10, True: 0},
            'show_email': {False: 15, True: 0},
            'show_phone': {False: 15, True: 0},
            'show_address': {False: 10, True: 0},
            'contact_permission': {'none': 15, 'bookings': 10, 'verified': 5, 'anyone': 0},
            'allow_marketing_contact': {False: 10, True: 0},
            'data_sharing_level': {'none': 15, 'minimal': 10, 'limited': 5, 'full': 0},
            'allow_usage_analytics': {False: 5, True: 0},
            'searchable_profile': {False: 5, True: 0},
        }
        
        for field, weights in scoring_rules.items():
            max_score += max(weights.values())
            current_value = getattr(self.privacy_settings, field, None)
            score += weights.get(current_value, 0)
        
        percentage = int((score / max_score) * 100) if max_score > 0 else 0
        
        # Determine privacy level
        if percentage >= 80:
            level = 'high'
        elif percentage >= 50:
            level = 'medium'
        else:
            level = 'low'
        
        return {
            'score': percentage,
            'level': level,
            'level_info': self.PRIVACY_IMPACT_LEVELS[level],
            'raw_score': score,
            'max_score': max_score,
        }
    
    def _analyze_privacy_impact(self) -> Dict[str, Any]:
        """Analyze the impact of current privacy settings."""
        impacts = []
        
        # Analyze visibility impact
        if self.privacy_settings.profile_visibility == 'private':
            impacts.append({
                'type': 'visibility',
                'level': 'high',
                'title': 'Profile Hidden',
                'description': 'Your profile is completely private and won\'t appear in searches.',
                'trade_off': 'You may miss out on service opportunities.'
            })
        elif self.privacy_settings.profile_visibility == 'public':
            impacts.append({
                'type': 'visibility',
                'level': 'low',
                'title': 'Public Profile',
                'description': 'Your profile is visible to everyone.',
                'trade_off': 'Maximum discoverability but less privacy.'
            })
        
        # Analyze contact impact
        if self.privacy_settings.contact_permission == 'none':
            impacts.append({
                'type': 'contact',
                'level': 'high',
                'title': 'No Direct Contact',
                'description': 'No one can contact you directly through the platform.',
                'trade_off': 'You may miss important communications.'
            })
        
        # Analyze data sharing impact
        if self.privacy_settings.data_sharing_level == 'none':
            impacts.append({
                'type': 'features',
                'level': 'medium',
                'title': 'Limited Recommendations',
                'description': 'You won\'t receive personalized recommendations.',
                'trade_off': 'Less relevant service suggestions.'
            })
        
        return {
            'impacts': impacts,
            'overall_impact': self._get_overall_impact_level(impacts),
        }
    
    def _get_overall_impact_level(self, impacts: List[Dict]) -> str:
        """Determine overall privacy impact level."""
        high_count = sum(1 for impact in impacts if impact['level'] == 'high')
        medium_count = sum(1 for impact in impacts if impact['level'] == 'medium')
        
        if high_count >= 2:
            return 'high'
        elif high_count >= 1 or medium_count >= 2:
            return 'medium'
        else:
            return 'low'
    
    def _get_privacy_recommendations(self) -> List[Dict[str, Any]]:
        """Get personalized privacy recommendations."""
        recommendations = []
        
        # Check for overly restrictive settings
        if (self.privacy_settings.profile_visibility == 'private' and 
            self.privacy_settings.contact_permission == 'none'):
            recommendations.append({
                'type': 'warning',
                'title': 'Very Restrictive Settings',
                'description': 'Your privacy settings are very restrictive and may limit platform functionality.',
                'suggestion': 'Consider using "Limited" visibility to maintain privacy while allowing some interaction.',
                'action': 'adjust_visibility'
            })
        
        # Check for overly open settings
        if (self.privacy_settings.profile_visibility == 'public' and 
            self.privacy_settings.show_email and 
            self.privacy_settings.show_phone):
            recommendations.append({
                'type': 'caution',
                'title': 'Very Open Settings',
                'description': 'Your contact information is publicly visible.',
                'suggestion': 'Consider hiding your email and phone for better privacy protection.',
                'action': 'hide_contact_info'
            })
        
        # Check for inconsistent settings
        if (self.privacy_settings.profile_visibility == 'private' and 
            self.privacy_settings.searchable_profile):
            recommendations.append({
                'type': 'inconsistency',
                'title': 'Inconsistent Settings',
                'description': 'Your profile is private but still searchable.',
                'suggestion': 'Disable search visibility for consistent privacy.',
                'action': 'fix_inconsistency'
            })
        
        return recommendations
    
    def _get_data_usage_summary(self) -> Dict[str, Any]:
        """Get summary of how user data is being used."""
        usage = {
            'analytics': self.privacy_settings.allow_usage_analytics,
            'marketing': self.privacy_settings.allow_marketing_contact,
            'recommendations': self.privacy_settings.data_sharing_level != 'none',
            'search_visibility': self.privacy_settings.searchable_profile,
            'third_party_sharing': self.privacy_settings.share_with_partners,
        }
        
        return {
            'usage': usage,
            'data_types_collected': self._get_data_types_collected(),
            'retention_period': self._get_data_retention_info(),
        }
    
    def _get_data_types_collected(self) -> List[Dict[str, Any]]:
        """Get information about data types being collected."""
        data_types = [
            {
                'type': 'Profile Information',
                'description': 'Name, email, phone, address',
                'purpose': 'Account management and service delivery',
                'required': True
            },
            {
                'type': 'Usage Analytics',
                'description': 'How you use the platform',
                'purpose': 'Improve platform functionality',
                'required': False,
                'enabled': self.privacy_settings.allow_usage_analytics
            },
            {
                'type': 'Location Data',
                'description': 'Your location for service matching',
                'purpose': 'Find nearby services',
                'required': False,
                'enabled': self.privacy_settings.share_location_data
            }
        ]
        
        return data_types
    
    def _get_data_retention_info(self) -> Dict[str, Any]:
        """Get data retention information."""
        return {
            'profile_data': 'Retained while account is active',
            'usage_analytics': '2 years from collection',
            'marketing_data': 'Until consent is withdrawn',
            'backup_data': '30 days after account deletion'
        }
    
    def _get_consent_status(self) -> Dict[str, Any]:
        """Get current consent status."""
        try:
            consent = PrivacyConsent.objects.get(user=self.user)
            return {
                'has_consent': True,
                'consent_date': consent.consent_date,
                'consent_version': consent.consent_version,
                'consents': {
                    'data_processing': consent.data_processing,
                    'marketing': consent.marketing,
                    'analytics': consent.analytics,
                    'cookies': consent.cookies,
                    'third_party_sharing': consent.third_party_sharing,
                }
            }
        except PrivacyConsent.DoesNotExist:
            return {
                'has_consent': False,
                'needs_consent': True
            }
    
    def _get_recent_privacy_changes(self) -> List[Dict[str, Any]]:
        """Get recent privacy setting changes."""
        # This would typically come from a privacy change log
        # For now, return empty list
        return []
    
    def apply_privacy_preset(self, preset_name: str) -> Dict[str, Any]:
        """
        Apply a privacy preset.
        
        Args:
            preset_name: Name of the preset to apply
            
        Returns:
            dict: Result of applying the preset
        """
        if preset_name not in self.PRIVACY_PRESETS:
            return {'success': False, 'error': 'Invalid preset name'}
        
        preset = self.PRIVACY_PRESETS[preset_name]
        
        try:
            # Apply preset settings
            for field, value in preset['settings'].items():
                if hasattr(self.privacy_settings, field):
                    setattr(self.privacy_settings, field, value)
            
            self.privacy_settings.save()
            
            # Log the change
            log_info(
                f'Privacy preset applied: {preset_name}',
                user=self.user,
                extra_data={'preset': preset_name}
            )
            
            return {
                'success': True,
                'preset_applied': preset_name,
                'new_privacy_score': self._calculate_privacy_score(),
            }
            
        except Exception as e:
            log_error(
                error_type='privacy_preset_error',
                error_message=f'Error applying privacy preset: {preset_name}',
                user=self.user,
                exception=e
            )
            return {'success': False, 'error': 'Failed to apply preset'}
    
    def bulk_update_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Bulk update privacy settings.
        
        Args:
            settings: Dictionary of settings to update
            
        Returns:
            dict: Result of the bulk update
        """
        try:
            updated_fields = []
            
            for field, value in settings.items():
                if hasattr(self.privacy_settings, field):
                    old_value = getattr(self.privacy_settings, field)
                    if old_value != value:
                        setattr(self.privacy_settings, field, value)
                        updated_fields.append(field)
            
            if updated_fields:
                self.privacy_settings.save(update_fields=updated_fields)
                
                # Log the changes
                log_info(
                    'Privacy settings bulk updated',
                    user=self.user,
                    extra_data={'updated_fields': updated_fields}
                )
            
            return {
                'success': True,
                'updated_fields': updated_fields,
                'new_privacy_score': self._calculate_privacy_score(),
            }
            
        except Exception as e:
            log_error(
                error_type='privacy_bulk_update_error',
                error_message='Error during bulk privacy settings update',
                user=self.user,
                exception=e
            )
            return {'success': False, 'error': 'Failed to update settings'}


@login_required
@require_http_methods(["GET"])
def privacy_dashboard_ajax(request):
    """
    AJAX endpoint for getting privacy dashboard data.
    """
    try:
        privacy_manager = PrivacySettingsManager(request.user)
        dashboard_data = privacy_manager.get_privacy_dashboard_data()

        return JsonResponse({
            'success': True,
            'dashboard': dashboard_data,
        })

    except Exception as e:
        log_error(
            error_type='privacy_dashboard_error',
            error_message='Error loading privacy dashboard',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to load privacy dashboard',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def apply_privacy_preset_ajax(request):
    """
    AJAX endpoint for applying privacy presets.
    """
    try:
        data = json.loads(request.body)
        preset_name = data.get('preset_name')

        if not preset_name:
            return JsonResponse({
                'success': False,
                'error': 'Preset name is required',
            }, status=400)

        privacy_manager = PrivacySettingsManager(request.user)
        result = privacy_manager.apply_privacy_preset(preset_name)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='privacy_preset_ajax_error',
            error_message='Error applying privacy preset via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to apply privacy preset',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def bulk_update_privacy_ajax(request):
    """
    AJAX endpoint for bulk updating privacy settings.
    """
    try:
        data = json.loads(request.body)
        settings = data.get('settings', {})

        if not settings:
            return JsonResponse({
                'success': False,
                'error': 'Settings data is required',
            }, status=400)

        privacy_manager = PrivacySettingsManager(request.user)
        result = privacy_manager.bulk_update_settings(settings)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='privacy_bulk_update_ajax_error',
            error_message='Error bulk updating privacy settings via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to update privacy settings',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def privacy_impact_analysis_ajax(request):
    """
    AJAX endpoint for getting privacy impact analysis.
    """
    try:
        privacy_manager = PrivacySettingsManager(request.user)
        impact_data = privacy_manager._analyze_privacy_impact()
        privacy_score = privacy_manager._calculate_privacy_score()

        return JsonResponse({
            'success': True,
            'impact_analysis': impact_data,
            'privacy_score': privacy_score,
        })

    except Exception as e:
        log_error(
            error_type='privacy_impact_analysis_error',
            error_message='Error analyzing privacy impact',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to analyze privacy impact',
        }, status=500)
