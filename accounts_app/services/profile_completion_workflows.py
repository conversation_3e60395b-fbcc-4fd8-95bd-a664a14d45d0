"""
Enhanced Profile Completion Workflows Service.

This module provides advanced profile completion workflows with guided tours,
smart suggestions, completion incentives, and milestone tracking.
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.urls import reverse
from django.conf import settings
from django.template.loader import render_to_string
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json

from ..models import CustomerProfile, ServiceProviderProfile, ProfileCompletionMilestone
from ..utils.logging import log_info, log_error


class ProfileCompletionWorkflowService:
    """
    Advanced service for managing profile completion workflows.
    
    Features:
    - Progressive completion workflows
    - Smart field suggestions
    - Completion incentives and rewards
    - Guided tours and tutorials
    - Milestone tracking and achievements
    """
    
    # Completion incentives configuration
    COMPLETION_INCENTIVES = {
        'customer': {
            25: {
                'title': 'Getting Started',
                'description': 'You\'re on your way! Complete your basic info.',
                'reward': 'Profile visibility boost',
                'icon': 'fas fa-star',
                'color': 'warning'
            },
            50: {
                'title': 'Halfway There',
                'description': 'Great progress! Add your contact information.',
                'reward': 'Priority customer support',
                'icon': 'fas fa-medal',
                'color': 'info'
            },
            75: {
                'title': 'Almost Complete',
                'description': 'You\'re almost done! Add your profile picture.',
                'reward': 'Featured in search results',
                'icon': 'fas fa-trophy',
                'color': 'warning'
            },
            100: {
                'title': 'Profile Complete!',
                'description': 'Congratulations! Your profile is now complete.',
                'reward': 'Access to exclusive deals',
                'icon': 'fas fa-crown',
                'color': 'success'
            }
        },
        'provider': {
            30: {
                'title': 'Business Basics',
                'description': 'Set up your business information.',
                'reward': 'Business verification eligibility',
                'icon': 'fas fa-building',
                'color': 'primary'
            },
            60: {
                'title': 'Professional Profile',
                'description': 'Add your business logo and description.',
                'reward': 'Enhanced listing visibility',
                'icon': 'fas fa-briefcase',
                'color': 'info'
            },
            90: {
                'title': 'Ready for Business',
                'description': 'Complete your contact and location details.',
                'reward': 'Premium features access',
                'icon': 'fas fa-rocket',
                'color': 'warning'
            },
            100: {
                'title': 'Business Profile Complete!',
                'description': 'Your business is ready to attract customers!',
                'reward': 'Featured business listing',
                'icon': 'fas fa-star-of-life',
                'color': 'success'
            }
        }
    }
    
    # Smart suggestions for profile completion
    SMART_SUGGESTIONS = {
        'customer': [
            {
                'field': 'profile_picture',
                'title': 'Add a Profile Picture',
                'description': 'Profiles with pictures get 3x more engagement',
                'priority': 'high',
                'completion_boost': 15,
                'estimated_time': '2 minutes',
                'tips': [
                    'Use a clear, recent photo of yourself',
                    'Smile and make eye contact with the camera',
                    'Ensure good lighting and a clean background'
                ]
            },
            {
                'field': 'phone_number',
                'title': 'Add Your Phone Number',
                'description': 'Service providers can contact you directly',
                'priority': 'high',
                'completion_boost': 20,
                'estimated_time': '1 minute',
                'tips': [
                    'Use your primary phone number',
                    'Include area code for better matching',
                    'You can control who sees this in privacy settings'
                ]
            },
            {
                'field': 'address',
                'title': 'Add Your Location',
                'description': 'Find services near you more easily',
                'priority': 'medium',
                'completion_boost': 10,
                'estimated_time': '2 minutes',
                'tips': [
                    'Add your full address for accurate matching',
                    'This helps find services in your area',
                    'Your exact address is kept private'
                ]
            }
        ],
        'provider': [
            {
                'field': 'logo',
                'title': 'Upload Your Business Logo',
                'description': 'Businesses with logos get 5x more bookings',
                'priority': 'high',
                'completion_boost': 15,
                'estimated_time': '3 minutes',
                'tips': [
                    'Use a high-quality, clear logo',
                    'Ensure it represents your brand well',
                    'Square format works best'
                ]
            },
            {
                'field': 'description',
                'title': 'Write Your Business Description',
                'description': 'Tell customers what makes you special',
                'priority': 'high',
                'completion_boost': 20,
                'estimated_time': '5 minutes',
                'tips': [
                    'Highlight your unique services',
                    'Mention your experience and expertise',
                    'Keep it concise but informative'
                ]
            },
            {
                'field': 'website',
                'title': 'Add Your Website',
                'description': 'Drive traffic to your business website',
                'priority': 'medium',
                'completion_boost': 10,
                'estimated_time': '1 minute',
                'tips': [
                    'Include your full website URL',
                    'Make sure your website is mobile-friendly',
                    'Update your website regularly'
                ]
            }
        ]
    }
    
    def __init__(self, user, profile_type='customer'):
        """
        Initialize the workflow service.
        
        Args:
            user: User instance
            profile_type: Type of profile ('customer' or 'provider')
        """
        self.user = user
        self.profile_type = profile_type
        self.profile = self._get_profile()
    
    def _get_profile(self):
        """Get the appropriate profile instance."""
        if self.profile_type == 'customer':
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.profile_type == 'provider':
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def get_completion_workflow(self) -> Dict[str, Any]:
        """
        Get the complete profile completion workflow.
        
        Returns:
            dict: Comprehensive workflow data
        """
        if not self.profile:
            return {'error': 'Profile not found'}
        
        current_percentage = self.profile.profile_completion_percentage
        
        return {
            'current_completion': current_percentage,
            'next_milestone': self._get_next_milestone(current_percentage),
            'smart_suggestions': self._get_smart_suggestions(),
            'completion_incentives': self._get_completion_incentives(),
            'guided_tour': self._get_guided_tour_data(),
            'achievements': self._get_user_achievements(),
            'progress_history': self._get_progress_history(),
        }
    
    def _get_next_milestone(self, current_percentage: int) -> Optional[Dict[str, Any]]:
        """Get the next completion milestone."""
        incentives = self.COMPLETION_INCENTIVES.get(self.profile_type, {})
        
        for percentage in sorted(incentives.keys()):
            if current_percentage < percentage:
                milestone = incentives[percentage].copy()
                milestone['target_percentage'] = percentage
                milestone['progress_needed'] = percentage - current_percentage
                return milestone
        
        return None
    
    def _get_smart_suggestions(self) -> List[Dict[str, Any]]:
        """Get personalized smart suggestions for profile completion."""
        if not self.profile:
            return []
        
        suggestions = []
        base_suggestions = self.SMART_SUGGESTIONS.get(self.profile_type, [])
        
        for suggestion in base_suggestions:
            field_name = suggestion['field']
            
            # Check if field is already completed
            if hasattr(self.profile, field_name):
                field_value = getattr(self.profile, field_name)
                if field_value:  # Field has a value
                    continue
            
            # Add contextual information
            enhanced_suggestion = suggestion.copy()
            enhanced_suggestion['action_url'] = self._get_field_edit_url(field_name)
            enhanced_suggestion['is_applicable'] = True
            
            suggestions.append(enhanced_suggestion)
        
        # Sort by priority and completion boost
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        suggestions.sort(
            key=lambda x: (priority_order.get(x['priority'], 0), x['completion_boost']),
            reverse=True
        )
        
        return suggestions[:3]  # Return top 3 suggestions
    
    def _get_completion_incentives(self) -> List[Dict[str, Any]]:
        """Get completion incentives and rewards."""
        incentives = self.COMPLETION_INCENTIVES.get(self.profile_type, {})
        current_percentage = self.profile.profile_completion_percentage if self.profile else 0
        
        result = []
        for percentage, incentive in incentives.items():
            incentive_data = incentive.copy()
            incentive_data['target_percentage'] = percentage
            incentive_data['is_achieved'] = current_percentage >= percentage
            incentive_data['is_next'] = (
                current_percentage < percentage and 
                percentage == min([p for p in incentives.keys() if p > current_percentage], default=None)
            )
            result.append(incentive_data)
        
        return result
    
    def _get_guided_tour_data(self) -> Dict[str, Any]:
        """Get guided tour configuration."""
        if self.profile_type == 'customer':
            tour_steps = [
                {
                    'target': '#profile-picture-section',
                    'title': 'Add Your Profile Picture',
                    'content': 'Upload a clear photo of yourself to build trust with service providers.',
                    'placement': 'right'
                },
                {
                    'target': '#personal-info-section',
                    'title': 'Complete Personal Information',
                    'content': 'Fill in your name and contact details so providers can reach you.',
                    'placement': 'left'
                },
                {
                    'target': '#address-section',
                    'title': 'Add Your Location',
                    'content': 'Help us find services near you by adding your address.',
                    'placement': 'top'
                }
            ]
        else:
            tour_steps = [
                {
                    'target': '#business-logo-section',
                    'title': 'Upload Your Business Logo',
                    'content': 'A professional logo helps customers recognize and trust your business.',
                    'placement': 'right'
                },
                {
                    'target': '#business-info-section',
                    'title': 'Business Information',
                    'content': 'Complete your business details to attract more customers.',
                    'placement': 'left'
                },
                {
                    'target': '#contact-section',
                    'title': 'Contact Information',
                    'content': 'Make it easy for customers to reach you.',
                    'placement': 'top'
                }
            ]
        
        return {
            'steps': tour_steps,
            'auto_start': self._should_auto_start_tour(),
            'tour_id': f'{self.profile_type}_profile_completion'
        }
    
    def _get_user_achievements(self) -> List[Dict[str, Any]]:
        """Get user's profile completion achievements."""
        milestones = ProfileCompletionMilestone.objects.filter(
            user=self.user
        ).order_by('-achieved_at')
        
        achievements = []
        for milestone in milestones:
            achievements.append({
                'type': milestone.milestone_type,
                'title': milestone.get_milestone_type_display(),
                'achieved_at': milestone.achieved_at,
                'completion_percentage': milestone.completion_percentage,
                'description': milestone.description,
                'badge_icon': self._get_milestone_icon(milestone.milestone_type),
                'badge_color': self._get_milestone_color(milestone.milestone_type),
            })
        
        return achievements
    
    def _get_progress_history(self) -> List[Dict[str, Any]]:
        """Get profile completion progress history."""
        # This would typically come from a ProfileProgressHistory model
        # For now, return milestones as progress points
        milestones = ProfileCompletionMilestone.objects.filter(
            user=self.user
        ).order_by('achieved_at')
        
        history = []
        for milestone in milestones:
            history.append({
                'date': milestone.achieved_at,
                'percentage': milestone.completion_percentage,
                'milestone': milestone.get_milestone_type_display(),
                'description': f'Achieved {milestone.get_milestone_type_display()}'
            })
        
        return history
    
    def _get_field_edit_url(self, field_name: str) -> str:
        """Get the URL to edit a specific field."""
        if self.profile_type == 'customer':
            return reverse('accounts_app:customer_profile_edit')
        else:
            return reverse('accounts_app:service_provider_profile_edit')
    
    def _should_auto_start_tour(self) -> bool:
        """Determine if guided tour should auto-start."""
        if not self.profile:
            return False
        
        # Auto-start for new users with low completion
        completion = self.profile.profile_completion_percentage
        is_new_user = (timezone.now() - self.user.date_joined).days < 7
        
        return completion < 30 and is_new_user
    
    def _get_milestone_icon(self, milestone_type: str) -> str:
        """Get icon for milestone type."""
        icons = {
            'basic_info': 'fas fa-user',
            'contact_info': 'fas fa-phone',
            'profile_picture': 'fas fa-camera',
            'address_complete': 'fas fa-map-marker-alt',
            'full_profile': 'fas fa-check-circle',
            'verified': 'fas fa-shield-check',
            'first_booking': 'fas fa-calendar-check',
            'social_connected': 'fas fa-share-alt',
        }
        return icons.get(milestone_type, 'fas fa-star')
    
    def _get_milestone_color(self, milestone_type: str) -> str:
        """Get color for milestone type."""
        colors = {
            'basic_info': 'primary',
            'contact_info': 'info',
            'profile_picture': 'warning',
            'address_complete': 'secondary',
            'full_profile': 'success',
            'verified': 'success',
            'first_booking': 'primary',
            'social_connected': 'info',
        }
        return colors.get(milestone_type, 'secondary')
    
    def trigger_completion_check(self) -> Dict[str, Any]:
        """
        Trigger a completion check and award any new milestones.
        
        Returns:
            dict: Results of the completion check
        """
        if not self.profile:
            return {'error': 'Profile not found'}
        
        old_percentage = self.profile.profile_completion_percentage
        new_percentage = self.profile.calculate_completion_percentage()
        
        # Update profile completion percentage
        if old_percentage != new_percentage:
            self.profile.profile_completion_percentage = new_percentage
            self.profile.save(update_fields=['profile_completion_percentage'])
        
        # Check for new milestones
        new_milestones = ProfileCompletionMilestone.check_and_award_milestones(
            self.user, self.profile
        )
        
        return {
            'old_percentage': old_percentage,
            'new_percentage': new_percentage,
            'percentage_change': new_percentage - old_percentage,
            'new_milestones': new_milestones,
            'next_milestone': self._get_next_milestone(new_percentage),
        }


@login_required
@require_http_methods(["GET"])
def profile_completion_workflow_ajax(request):
    """
    AJAX endpoint for getting profile completion workflow data.
    """
    try:
        profile_type = 'customer' if request.user.is_customer else 'provider'
        workflow_service = ProfileCompletionWorkflowService(request.user, profile_type)
        workflow_data = workflow_service.get_completion_workflow()

        return JsonResponse({
            'success': True,
            'workflow': workflow_data,
        })

    except Exception as e:
        log_error(
            error_type='profile_completion_workflow_error',
            error_message='Error getting profile completion workflow',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to load completion workflow',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def trigger_completion_check_ajax(request):
    """
    AJAX endpoint for triggering profile completion check.
    """
    try:
        profile_type = 'customer' if request.user.is_customer else 'provider'
        workflow_service = ProfileCompletionWorkflowService(request.user, profile_type)
        check_results = workflow_service.trigger_completion_check()

        return JsonResponse({
            'success': True,
            'results': check_results,
        })

    except Exception as e:
        log_error(
            error_type='completion_check_error',
            error_message='Error triggering completion check',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to check completion status',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def start_guided_tour_ajax(request):
    """
    AJAX endpoint for starting guided tour.
    """
    try:
        profile_type = 'customer' if request.user.is_customer else 'provider'
        workflow_service = ProfileCompletionWorkflowService(request.user, profile_type)
        tour_data = workflow_service._get_guided_tour_data()

        # Log tour start
        log_info(
            'Profile completion guided tour started',
            user=request.user,
            extra_data={'profile_type': profile_type}
        )

        return JsonResponse({
            'success': True,
            'tour': tour_data,
        })

    except Exception as e:
        log_error(
            error_type='guided_tour_error',
            error_message='Error starting guided tour',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to start guided tour',
        }, status=500)
