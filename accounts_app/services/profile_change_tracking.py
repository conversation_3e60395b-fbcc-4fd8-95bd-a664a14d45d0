"""
Enhanced Profile Change History Tracking Service.

This module provides comprehensive profile change tracking with detailed audit logs,
change comparison views, rollback functionality, and change notifications.
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
from django.core.paginator import Paginator
from django.db.models import Q

from ..models import ProfileChangeHistory, CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_info, log_error


class ProfileChangeTracker:
    """
    Advanced profile change tracking service.
    
    Features:
    - Detailed change logging
    - Field-level comparison
    - Change rollback capabilities
    - Change notifications
    - Audit trail management
    """
    
    # Field categories for better organization
    FIELD_CATEGORIES = {
        'personal': ['first_name', 'last_name', 'gender', 'birth_month', 'birth_year'],
        'contact': ['phone_number', 'phone', 'email', 'contact_name'],
        'location': ['address', 'city', 'state', 'county', 'zip_code'],
        'business': ['legal_name', 'display_name', 'description', 'ein', 'website', 'instagram', 'facebook'],
        'media': ['profile_picture', 'logo'],
        'privacy': ['profile_visibility', 'show_email_to_providers', 'show_phone_to_providers', 'is_public'],
        'verification': ['is_verified', 'verification_date', 'verification_notes'],
    }
    
    # Sensitive fields that require special handling
    SENSITIVE_FIELDS = ['phone_number', 'phone', 'address', 'ein']
    
    # Fields that should not be tracked in detail
    EXCLUDED_FIELDS = ['id', 'user', 'created_at', 'updated_at', 'last_login']
    
    def __init__(self, user, profile_type='customer'):
        """
        Initialize change tracker.
        
        Args:
            user: User instance
            profile_type: Type of profile ('customer' or 'provider')
        """
        self.user = user
        self.profile_type = profile_type
        self.profile = self._get_profile()
    
    def _get_profile(self):
        """Get the appropriate profile instance."""
        if self.profile_type == 'customer':
            try:
                return CustomerProfile.objects.get(user=self.user)
            except CustomerProfile.DoesNotExist:
                return None
        elif self.profile_type == 'provider':
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def track_change(self, change_type: str, changed_fields: Dict[str, Any] = None, 
                    change_summary: str = '', changed_by: Any = None, 
                    request: Any = None) -> ProfileChangeHistory:
        """
        Track a profile change.
        
        Args:
            change_type: Type of change from CHANGE_TYPES
            changed_fields: Dictionary of changed fields with old/new values
            change_summary: Brief description of changes
            changed_by: User who made the change (if different from profile owner)
            request: Django request object for IP/user agent
            
        Returns:
            ProfileChangeHistory: Created change record
        """
        if not self.profile:
            raise ValueError("Profile not found")
        
        # Prepare change data
        change_data = self._prepare_change_data(changed_fields or {})
        
        # Get request metadata
        request_metadata = self._extract_request_metadata(request) if request else {}
        
        # Create change record
        change_record = ProfileChangeHistory.objects.create(
            profile=self.profile,
            user=self.user,
            change_type=change_type,
            changed_fields=change_data,
            change_summary=change_summary or self._generate_change_summary(change_data),
            changed_by=changed_by or self.user,
            ip_address=request_metadata.get('ip_address'),
            user_agent=request_metadata.get('user_agent'),
            session_key=request_metadata.get('session_key'),
        )
        
        # Log the change
        log_info(
            f'Profile change tracked: {change_type}',
            user=self.user,
            extra_data={
                'change_id': change_record.id,
                'change_type': change_type,
                'fields_changed': list(change_data.keys()) if change_data else [],
                'profile_type': self.profile_type,
            }
        )
        
        return change_record
    
    def _prepare_change_data(self, changed_fields: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare change data with proper formatting and security."""
        prepared_data = {}
        
        for field_name, change_info in changed_fields.items():
            if field_name in self.EXCLUDED_FIELDS:
                continue
            
            # Handle different change info formats
            if isinstance(change_info, dict) and 'old' in change_info and 'new' in change_info:
                old_value = change_info['old']
                new_value = change_info['new']
            elif isinstance(change_info, (list, tuple)) and len(change_info) == 2:
                old_value, new_value = change_info
            else:
                # Single value - assume it's the new value
                old_value = getattr(self.profile, field_name, None)
                new_value = change_info
            
            # Apply security filtering for sensitive fields
            if field_name in self.SENSITIVE_FIELDS:
                old_value = self._mask_sensitive_value(old_value)
                new_value = self._mask_sensitive_value(new_value)
            
            # Convert values to JSON-serializable format
            prepared_data[field_name] = {
                'old': self._serialize_value(old_value),
                'new': self._serialize_value(new_value),
                'category': self._get_field_category(field_name),
                'is_sensitive': field_name in self.SENSITIVE_FIELDS,
            }
        
        return prepared_data
    
    def _mask_sensitive_value(self, value):
        """Mask sensitive values for security."""
        if not value:
            return value
        
        value_str = str(value)
        if len(value_str) <= 4:
            return '*' * len(value_str)
        
        # Show first 2 and last 2 characters
        return value_str[:2] + '*' * (len(value_str) - 4) + value_str[-2:]
    
    def _serialize_value(self, value):
        """Convert value to JSON-serializable format."""
        if value is None:
            return None
        elif hasattr(value, 'url'):  # File field
            return value.url
        elif hasattr(value, 'isoformat'):  # DateTime
            return value.isoformat()
        else:
            return str(value)
    
    def _get_field_category(self, field_name: str) -> str:
        """Get category for a field."""
        for category, fields in self.FIELD_CATEGORIES.items():
            if field_name in fields:
                return category
        return 'other'
    
    def _generate_change_summary(self, change_data: Dict[str, Any]) -> str:
        """Generate a human-readable change summary."""
        if not change_data:
            return "Profile updated"
        
        categories = set()
        field_count = len(change_data)
        
        for field_info in change_data.values():
            categories.add(field_info.get('category', 'other'))
        
        if field_count == 1:
            field_name = list(change_data.keys())[0]
            return f"Updated {field_name.replace('_', ' ')}"
        elif len(categories) == 1:
            category = list(categories)[0]
            return f"Updated {category} information ({field_count} fields)"
        else:
            return f"Updated profile information ({field_count} fields)"
    
    def _extract_request_metadata(self, request) -> Dict[str, Any]:
        """Extract metadata from Django request."""
        if not request:
            return {}
        
        return {
            'ip_address': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'session_key': request.session.session_key if hasattr(request, 'session') else None,
        }
    
    def _get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_change_history(self, limit: int = 50, offset: int = 0, 
                          filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get paginated change history for the profile.
        
        Args:
            limit: Number of records to return
            offset: Number of records to skip
            filters: Optional filters to apply
            
        Returns:
            dict: Paginated change history data
        """
        if not self.profile:
            return {'changes': [], 'total': 0, 'has_more': False}
        
        # Build query
        content_type = ContentType.objects.get_for_model(self.profile)
        queryset = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id
        )
        
        # Apply filters
        if filters:
            if 'change_type' in filters:
                queryset = queryset.filter(change_type=filters['change_type'])
            
            if 'date_from' in filters:
                queryset = queryset.filter(changed_at__gte=filters['date_from'])
            
            if 'date_to' in filters:
                queryset = queryset.filter(changed_at__lte=filters['date_to'])
            
            if 'changed_by' in filters:
                queryset = queryset.filter(changed_by=filters['changed_by'])
        
        # Order by most recent first
        queryset = queryset.order_by('-changed_at')
        
        # Get total count
        total = queryset.count()
        
        # Apply pagination
        changes = queryset[offset:offset + limit]
        
        # Serialize changes
        serialized_changes = []
        for change in changes:
            serialized_changes.append(self._serialize_change_record(change))
        
        return {
            'changes': serialized_changes,
            'total': total,
            'has_more': total > offset + limit,
            'limit': limit,
            'offset': offset,
        }
    
    def _serialize_change_record(self, change: ProfileChangeHistory) -> Dict[str, Any]:
        """Serialize a change record for API response."""
        return {
            'id': change.id,
            'change_type': change.change_type,
            'change_type_display': change.get_change_type_display(),
            'change_summary': change.change_summary,
            'changed_at': change.changed_at.isoformat(),
            'changed_by': {
                'id': change.changed_by.id,
                'email': change.changed_by.email,
                'name': getattr(change.changed_by, 'get_full_name', lambda: change.changed_by.email)(),
            },
            'changed_fields': change.changed_fields,
            'field_count': len(change.changed_fields) if change.changed_fields else 0,
            'categories': self._get_change_categories(change.changed_fields),
            'ip_address': change.ip_address,
            'user_agent': change.user_agent,
            'can_rollback': self._can_rollback_change(change),
        }
    
    def _get_change_categories(self, changed_fields: Dict[str, Any]) -> List[str]:
        """Get categories of changed fields."""
        if not changed_fields:
            return []
        
        categories = set()
        for field_info in changed_fields.values():
            categories.add(field_info.get('category', 'other'))
        
        return list(categories)
    
    def _can_rollback_change(self, change: ProfileChangeHistory) -> bool:
        """Determine if a change can be rolled back."""
        # Don't allow rollback of very old changes
        if change.changed_at < timezone.now() - timedelta(days=30):
            return False
        
        # Don't allow rollback of certain change types
        non_rollback_types = ['create', 'verify', 'data_export', 'deletion_request']
        if change.change_type in non_rollback_types:
            return False
        
        return True
    
    def compare_changes(self, change_id1: int, change_id2: int) -> Dict[str, Any]:
        """
        Compare two change records.
        
        Args:
            change_id1: ID of first change record
            change_id2: ID of second change record
            
        Returns:
            dict: Comparison data
        """
        try:
            change1 = ProfileChangeHistory.objects.get(id=change_id1, user=self.user)
            change2 = ProfileChangeHistory.objects.get(id=change_id2, user=self.user)
        except ProfileChangeHistory.DoesNotExist:
            return {'error': 'Change record not found'}
        
        # Compare the changes
        comparison = {
            'change1': self._serialize_change_record(change1),
            'change2': self._serialize_change_record(change2),
            'field_differences': self._compare_changed_fields(
                change1.changed_fields, 
                change2.changed_fields
            ),
            'time_difference': abs((change2.changed_at - change1.changed_at).total_seconds()),
        }
        
        return comparison
    
    def _compare_changed_fields(self, fields1: Dict[str, Any], 
                               fields2: Dict[str, Any]) -> Dict[str, Any]:
        """Compare changed fields between two change records."""
        all_fields = set(fields1.keys()) | set(fields2.keys())
        
        differences = {
            'common_fields': [],
            'only_in_first': [],
            'only_in_second': [],
            'value_differences': [],
        }
        
        for field in all_fields:
            if field in fields1 and field in fields2:
                differences['common_fields'].append(field)
                
                # Check if values are different
                if fields1[field] != fields2[field]:
                    differences['value_differences'].append({
                        'field': field,
                        'change1_value': fields1[field],
                        'change2_value': fields2[field],
                    })
            elif field in fields1:
                differences['only_in_first'].append(field)
            else:
                differences['only_in_second'].append(field)
        
        return differences
    
    def rollback_change(self, change_id: int, reason: str = '') -> Dict[str, Any]:
        """
        Rollback a specific change.
        
        Args:
            change_id: ID of change to rollback
            reason: Reason for rollback
            
        Returns:
            dict: Rollback result
        """
        try:
            change = ProfileChangeHistory.objects.get(id=change_id, user=self.user)
        except ProfileChangeHistory.DoesNotExist:
            return {'success': False, 'error': 'Change record not found'}
        
        if not self._can_rollback_change(change):
            return {'success': False, 'error': 'This change cannot be rolled back'}
        
        if not change.changed_fields:
            return {'success': False, 'error': 'No field changes to rollback'}
        
        try:
            # Apply rollback
            rollback_data = {}
            for field_name, field_info in change.changed_fields.items():
                if not field_info.get('is_sensitive', False):
                    old_value = field_info.get('old')
                    if hasattr(self.profile, field_name):
                        setattr(self.profile, field_name, old_value)
                        rollback_data[field_name] = {
                            'old': field_info.get('new'),
                            'new': old_value
                        }
            
            # Save the profile
            self.profile.save()
            
            # Track the rollback as a new change
            rollback_change = self.track_change(
                change_type='rollback',
                changed_fields=rollback_data,
                change_summary=f'Rolled back change from {change.changed_at.strftime("%Y-%m-%d %H:%M")}. Reason: {reason}',
            )
            
            return {
                'success': True,
                'rollback_change_id': rollback_change.id,
                'fields_rolled_back': list(rollback_data.keys()),
            }
            
        except Exception as e:
            log_error(
                error_type='profile_rollback_error',
                error_message=f'Error rolling back change {change_id}',
                user=self.user,
                exception=e
            )
            return {'success': False, 'error': 'Failed to rollback change'}
    
    def get_change_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        Get change statistics for the profile.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            dict: Change statistics
        """
        if not self.profile:
            return {}
        
        since_date = timezone.now() - timedelta(days=days)
        content_type = ContentType.objects.get_for_model(self.profile)
        
        queryset = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id,
            changed_at__gte=since_date
        )
        
        # Count by change type
        change_type_counts = {}
        for change_type, _ in ProfileChangeHistory.CHANGE_TYPES:
            count = queryset.filter(change_type=change_type).count()
            if count > 0:
                change_type_counts[change_type] = count
        
        # Count by category
        category_counts = {}
        for change in queryset:
            if change.changed_fields:
                categories = self._get_change_categories(change.changed_fields)
                for category in categories:
                    category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            'total_changes': queryset.count(),
            'days_analyzed': days,
            'change_type_counts': change_type_counts,
            'category_counts': category_counts,
            'most_active_day': self._get_most_active_day(queryset),
            'average_changes_per_day': queryset.count() / days if days > 0 else 0,
        }
    
    def _get_most_active_day(self, queryset):
        """Get the most active day from the queryset."""
        # This would require more complex aggregation
        # For now, return None
        return None


@login_required
@require_http_methods(["GET"])
def profile_change_history_ajax(request):
    """
    AJAX endpoint for getting profile change history.
    """
    try:
        profile_type = 'customer' if request.user.is_customer else 'provider'
        tracker = ProfileChangeTracker(request.user, profile_type)

        # Get pagination parameters
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Get filters
        filters = {}
        if request.GET.get('change_type'):
            filters['change_type'] = request.GET.get('change_type')
        if request.GET.get('date_from'):
            filters['date_from'] = request.GET.get('date_from')
        if request.GET.get('date_to'):
            filters['date_to'] = request.GET.get('date_to')

        history_data = tracker.get_change_history(limit, offset, filters)

        return JsonResponse({
            'success': True,
            'history': history_data,
        })

    except Exception as e:
        log_error(
            error_type='profile_change_history_error',
            error_message='Error getting profile change history',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to load change history',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def compare_profile_changes_ajax(request):
    """
    AJAX endpoint for comparing profile changes.
    """
    try:
        change_id1 = request.GET.get('change_id1')
        change_id2 = request.GET.get('change_id2')

        if not change_id1 or not change_id2:
            return JsonResponse({
                'success': False,
                'error': 'Both change IDs are required',
            }, status=400)

        profile_type = 'customer' if request.user.is_customer else 'provider'
        tracker = ProfileChangeTracker(request.user, profile_type)

        comparison = tracker.compare_changes(int(change_id1), int(change_id2))

        if 'error' in comparison:
            return JsonResponse({
                'success': False,
                'error': comparison['error'],
            }, status=404)

        return JsonResponse({
            'success': True,
            'comparison': comparison,
        })

    except ValueError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid change ID format',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='profile_change_comparison_error',
            error_message='Error comparing profile changes',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to compare changes',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def rollback_profile_change_ajax(request):
    """
    AJAX endpoint for rolling back profile changes.
    """
    try:
        data = json.loads(request.body)
        change_id = data.get('change_id')
        reason = data.get('reason', '')

        if not change_id:
            return JsonResponse({
                'success': False,
                'error': 'Change ID is required',
            }, status=400)

        profile_type = 'customer' if request.user.is_customer else 'provider'
        tracker = ProfileChangeTracker(request.user, profile_type)

        result = tracker.rollback_change(int(change_id), reason)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except ValueError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid change ID format',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='profile_rollback_ajax_error',
            error_message='Error rolling back profile change via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to rollback change',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def profile_change_statistics_ajax(request):
    """
    AJAX endpoint for getting profile change statistics.
    """
    try:
        days = int(request.GET.get('days', 30))
        profile_type = 'customer' if request.user.is_customer else 'provider'
        tracker = ProfileChangeTracker(request.user, profile_type)

        statistics = tracker.get_change_statistics(days)

        return JsonResponse({
            'success': True,
            'statistics': statistics,
        })

    except ValueError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid days parameter',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='profile_change_statistics_error',
            error_message='Error getting profile change statistics',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to load change statistics',
        }, status=500)
