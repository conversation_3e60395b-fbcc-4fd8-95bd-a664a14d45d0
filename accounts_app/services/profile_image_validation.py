"""
Profile image validation and processing service.

This module provides comprehensive image validation, security checks,
and processing for user profile pictures.
"""

import os
import hashlib
import mimetypes
from typing import Dict, Any, Optional, Tuple
from io import BytesIO

from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.files.base import ContentFile

try:
    from PIL import Image, ImageOps, ExifTags
    from PIL.ExifTags import TAGS
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

from ..utils.logging import log_error, log_info


class ProfileImageValidator:
    """
    Advanced profile image validation with security checks.
    
    Features:
    - File type validation using multiple methods
    - Image dimension and size validation
    - Security scanning for malicious content
    - EXIF data stripping for privacy
    - Format conversion and optimization
    """
    
    # Configuration
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MIN_FILE_SIZE = 1024  # 1KB
    MIN_DIMENSION = 100  # pixels
    MAX_DIMENSION = 5000  # pixels
    MAX_ASPECT_RATIO = 10  # width/height or height/width
    
    ALLOWED_FORMATS = {
        'JPEG': ['image/jpeg', 'image/jpg'],
        'PNG': ['image/png'],
        'WebP': ['image/webp'],
        'HEIC': ['image/heic'],
        'HEIF': ['image/heif'],
    }
    
    ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.heic', '.heif']
    
    # Security patterns to detect in file content
    SUSPICIOUS_PATTERNS = [
        b'<script',
        b'javascript:',
        b'<?php',
        b'<%',
        b'<html',
        b'<body',
    ]
    
    def __init__(self, user=None):
        """
        Initialize validator.
        
        Args:
            user: User instance for logging purposes
        """
        self.user = user
        self.validation_errors = []
        self.warnings = []
    
    def validate_image(self, image_file: UploadedFile) -> Dict[str, Any]:
        """
        Comprehensive image validation.
        
        Args:
            image_file: Uploaded image file
            
        Returns:
            dict: Validation results with processed image
            
        Raises:
            ValidationError: If validation fails
        """
        if not image_file:
            raise ValidationError(_('No image file provided.'))
        
        # Reset validation state
        self.validation_errors = []
        self.warnings = []
        
        try:
            # Basic file validation
            self._validate_file_basics(image_file)
            
            # Security validation
            self._validate_file_security(image_file)
            
            # Image-specific validation
            image_data = self._validate_image_content(image_file)
            
            # Process and optimize image
            processed_image = self._process_image(image_file, image_data)
            
            return {
                'success': True,
                'processed_image': processed_image,
                'original_format': image_data.get('format'),
                'dimensions': image_data.get('dimensions'),
                'file_size': image_file.size,
                'warnings': self.warnings,
            }
            
        except ValidationError:
            raise
        except Exception as e:
            log_error(
                error_type='image_validation_error',
                error_message='Unexpected error during image validation',
                user=self.user,
                exception=e,
                extra_data={'filename': image_file.name}
            )
            raise ValidationError(
                _('Error processing image. Please try a different image.')
            )
    
    def _validate_file_basics(self, image_file: UploadedFile) -> None:
        """Validate basic file properties."""
        # File size validation
        if image_file.size > self.MAX_FILE_SIZE:
            raise ValidationError(
                _('Image file too large. Maximum size is {:.1f}MB.').format(
                    self.MAX_FILE_SIZE / (1024 * 1024)
                )
            )
        
        if image_file.size < self.MIN_FILE_SIZE:
            raise ValidationError(
                _('Image file too small. Minimum size is {}KB.').format(
                    self.MIN_FILE_SIZE // 1024
                )
            )
        
        # File extension validation
        if image_file.name:
            ext = os.path.splitext(image_file.name.lower())[1]
            if ext not in self.ALLOWED_EXTENSIONS:
                raise ValidationError(
                    _('Unsupported file extension. Allowed: {}').format(
                        ', '.join(self.ALLOWED_EXTENSIONS)
                    )
                )
        
        # Content type validation
        if hasattr(image_file, 'content_type') and image_file.content_type:
            allowed_types = []
            for format_types in self.ALLOWED_FORMATS.values():
                allowed_types.extend(format_types)
            
            if image_file.content_type.lower() not in allowed_types:
                raise ValidationError(
                    _('Unsupported image format. Supported formats: JPEG, PNG, WebP, HEIC/HEIF.')
                )
    
    def _validate_file_security(self, image_file: UploadedFile) -> None:
        """Validate file for security threats."""
        # Reset file pointer
        image_file.seek(0)
        
        # Read file content for security scanning
        content = image_file.read(8192)  # Read first 8KB for pattern matching
        image_file.seek(0)  # Reset pointer
        
        # Check for suspicious patterns
        for pattern in self.SUSPICIOUS_PATTERNS:
            if pattern in content.lower():
                raise ValidationError(
                    _('File contains suspicious content and cannot be uploaded.')
                )
        
        # Use python-magic for MIME type detection if available
        if MAGIC_AVAILABLE:
            try:
                mime_type = magic.from_buffer(content, mime=True)
                if not mime_type.startswith('image/'):
                    raise ValidationError(
                        _('File is not a valid image format.')
                    )
            except Exception as e:
                self.warnings.append(
                    _('Could not verify file type using advanced detection.')
                )
        
        # Check file header for common image signatures
        self._validate_file_signature(content)
    
    def _validate_file_signature(self, content: bytes) -> None:
        """Validate file signature (magic bytes)."""
        if len(content) < 4:
            raise ValidationError(_('File too small to be a valid image.'))
        
        # Common image file signatures
        signatures = {
            b'\xFF\xD8\xFF': 'JPEG',
            b'\x89PNG\r\n\x1a\n': 'PNG',
            b'RIFF': 'WebP',  # WebP starts with RIFF
            b'\x00\x00\x00\x20ftypheic': 'HEIC',
            b'\x00\x00\x00\x18ftypheif': 'HEIF',
        }
        
        # Check for known signatures
        signature_found = False
        for sig, format_name in signatures.items():
            if content.startswith(sig) or (format_name == 'WebP' and b'WEBP' in content[:12]):
                signature_found = True
                break
        
        if not signature_found:
            raise ValidationError(
                _('File does not appear to be a valid image format.')
            )
    
    def _validate_image_content(self, image_file: UploadedFile) -> Dict[str, Any]:
        """Validate image content using PIL."""
        if not PIL_AVAILABLE:
            self.warnings.append(
                _('Advanced image validation not available. Basic validation only.')
            )
            return {}
        
        try:
            # Reset file pointer
            image_file.seek(0)
            
            # Open and validate image
            with Image.open(image_file) as img:
                # Verify it's a valid image
                img.verify()
                
                # Reset file pointer after verify
                image_file.seek(0)
                
                # Re-open for detailed analysis
                with Image.open(image_file) as img:
                    width, height = img.size
                    format_name = img.format
                    mode = img.mode
                    
                    # Dimension validation
                    if width < self.MIN_DIMENSION or height < self.MIN_DIMENSION:
                        raise ValidationError(
                            _('Image too small. Minimum dimensions: {}x{} pixels.').format(
                                self.MIN_DIMENSION, self.MIN_DIMENSION
                            )
                        )
                    
                    if width > self.MAX_DIMENSION or height > self.MAX_DIMENSION:
                        raise ValidationError(
                            _('Image too large. Maximum dimensions: {}x{} pixels.').format(
                                self.MAX_DIMENSION, self.MAX_DIMENSION
                            )
                        )
                    
                    # Aspect ratio validation
                    aspect_ratio = max(width, height) / min(width, height)
                    if aspect_ratio > self.MAX_ASPECT_RATIO:
                        raise ValidationError(
                            _('Invalid image aspect ratio. Image appears to be too narrow or wide.')
                        )
                    
                    # Check for animated images
                    if hasattr(img, 'is_animated') and img.is_animated:
                        self.warnings.append(
                            _('Animated images will be converted to static images.')
                        )
                    
                    return {
                        'dimensions': (width, height),
                        'format': format_name,
                        'mode': mode,
                        'has_transparency': mode in ('RGBA', 'LA') or 'transparency' in img.info,
                    }
                    
        except ValidationError:
            raise
        except Exception as e:
            raise ValidationError(
                _('Invalid or corrupted image file. Please try a different image.')
            )

    def _process_image(self, image_file: UploadedFile, image_data: Dict[str, Any]) -> ContentFile:
        """Process and optimize image."""
        if not PIL_AVAILABLE:
            # Return original file if PIL not available
            image_file.seek(0)
            return ContentFile(image_file.read(), name=image_file.name)

        try:
            # Reset file pointer
            image_file.seek(0)

            with Image.open(image_file) as img:
                # Strip EXIF data for privacy
                img = self._strip_exif_data(img)

                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA'):
                    # Create white background for transparency
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # Optimize image size while maintaining quality
                img = self._optimize_image_size(img)

                # Save processed image
                output = BytesIO()
                img.save(output, format='JPEG', quality=85, optimize=True)
                output.seek(0)

                # Generate new filename
                original_name = image_file.name or 'profile_image'
                name_without_ext = os.path.splitext(original_name)[0]
                new_filename = f"{name_without_ext}_processed.jpg"

                return ContentFile(output.getvalue(), name=new_filename)

        except Exception as e:
            log_error(
                error_type='image_processing_error',
                error_message='Error processing image',
                user=self.user,
                exception=e
            )
            # Return original file if processing fails
            image_file.seek(0)
            return ContentFile(image_file.read(), name=image_file.name)

    def _strip_exif_data(self, img: Image.Image) -> Image.Image:
        """Strip EXIF data from image for privacy."""
        try:
            # Create new image without EXIF data
            data = list(img.getdata())
            image_without_exif = Image.new(img.mode, img.size)
            image_without_exif.putdata(data)
            return image_without_exif
        except Exception:
            # Return original if stripping fails
            return img

    def _optimize_image_size(self, img: Image.Image) -> Image.Image:
        """Optimize image size for web use."""
        width, height = img.size

        # Target maximum dimension for profile pictures
        max_profile_dimension = 800

        if width > max_profile_dimension or height > max_profile_dimension:
            # Calculate new dimensions maintaining aspect ratio
            if width > height:
                new_width = max_profile_dimension
                new_height = int((height * max_profile_dimension) / width)
            else:
                new_height = max_profile_dimension
                new_width = int((width * max_profile_dimension) / height)

            # Resize with high-quality resampling
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            self.warnings.append(
                _('Image was resized to {}x{} pixels for optimal performance.').format(
                    new_width, new_height
                )
            )

        return img

    def generate_image_hash(self, image_file: UploadedFile) -> str:
        """Generate hash of image content for duplicate detection."""
        image_file.seek(0)
        content = image_file.read()
        image_file.seek(0)

        return hashlib.sha256(content).hexdigest()

    def validate_and_process(self, image_file: UploadedFile) -> Tuple[ContentFile, Dict[str, Any]]:
        """
        Validate and process image in one step.

        Args:
            image_file: Uploaded image file

        Returns:
            tuple: (processed_image_file, validation_info)
        """
        validation_result = self.validate_image(image_file)

        return validation_result['processed_image'], {
            'original_format': validation_result.get('original_format'),
            'dimensions': validation_result.get('dimensions'),
            'file_size': validation_result.get('file_size'),
            'warnings': validation_result.get('warnings', []),
            'image_hash': self.generate_image_hash(image_file),
        }
