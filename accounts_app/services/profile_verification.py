"""
Profile Verification Service.

This module provides comprehensive profile verification functionality with document upload,
verification status tracking, verification badges, and admin verification tools.
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.files.uploadedfile import UploadedFile
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
from django.core.mail import EmailMessage
from django.conf import settings

from ..models import ProfileVerificationRequest, VerificationDocument, CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_info, log_error


class ProfileVerificationService:
    """
    Comprehensive profile verification service.
    
    Features:
    - Document upload and validation
    - Verification workflow management
    - Verification status tracking
    - Admin verification tools
    - Verification badges and certificates
    """
    
    # Verification types
    VERIFICATION_TYPES = {
        'identity': {
            'name': 'Identity Verification',
            'description': 'Verify your identity with government-issued ID',
            'required_documents': ['government_id'],
            'processing_time': '1-3 business days',
            'badge_color': 'primary',
            'badge_icon': 'fas fa-id-card',
        },
        'address': {
            'name': 'Address Verification',
            'description': 'Verify your address with utility bill or bank statement',
            'required_documents': ['address_proof'],
            'processing_time': '1-2 business days',
            'badge_color': 'info',
            'badge_icon': 'fas fa-map-marker-alt',
        },
        'business': {
            'name': 'Business Verification',
            'description': 'Verify your business with registration documents',
            'required_documents': ['business_registration', 'tax_document'],
            'processing_time': '3-5 business days',
            'badge_color': 'success',
            'badge_icon': 'fas fa-building',
        },
        'professional': {
            'name': 'Professional Verification',
            'description': 'Verify professional credentials and certifications',
            'required_documents': ['professional_license', 'certification'],
            'processing_time': '2-4 business days',
            'badge_color': 'warning',
            'badge_icon': 'fas fa-certificate',
        },
        'enhanced': {
            'name': 'Enhanced Verification',
            'description': 'Complete verification with background check',
            'required_documents': ['government_id', 'address_proof', 'background_check'],
            'processing_time': '5-7 business days',
            'badge_color': 'danger',
            'badge_icon': 'fas fa-shield-check',
        }
    }
    
    # Document types
    DOCUMENT_TYPES = {
        'government_id': {
            'name': 'Government ID',
            'description': 'Driver\'s license, passport, or national ID',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,  # 10MB
            'required_fields': ['document_number', 'expiry_date'],
        },
        'address_proof': {
            'name': 'Address Proof',
            'description': 'Utility bill, bank statement, or lease agreement',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,
            'required_fields': ['document_date'],
        },
        'business_registration': {
            'name': 'Business Registration',
            'description': 'Business license or incorporation documents',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,
            'required_fields': ['registration_number', 'registration_date'],
        },
        'tax_document': {
            'name': 'Tax Document',
            'description': 'Tax registration or EIN document',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,
            'required_fields': ['tax_id'],
        },
        'professional_license': {
            'name': 'Professional License',
            'description': 'Professional license or certification',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,
            'required_fields': ['license_number', 'issuing_authority'],
        },
        'certification': {
            'name': 'Certification',
            'description': 'Professional certification or training certificate',
            'accepted_formats': ['jpg', 'jpeg', 'png', 'pdf'],
            'max_size': 10 * 1024 * 1024,
            'required_fields': ['certification_name', 'issuing_organization'],
        },
        'background_check': {
            'name': 'Background Check',
            'description': 'Background check report from authorized provider',
            'accepted_formats': ['pdf'],
            'max_size': 20 * 1024 * 1024,  # 20MB
            'required_fields': ['check_date', 'provider_name'],
        }
    }
    
    def __init__(self, user):
        """
        Initialize verification service.
        
        Args:
            user: User instance
        """
        self.user = user
        self.profile = self._get_profile()
    
    def _get_profile(self):
        """Get the appropriate profile instance."""
        try:
            return CustomerProfile.objects.get(user=self.user)
        except CustomerProfile.DoesNotExist:
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
    
    def start_verification(self, verification_type: str, documents: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start a new verification process.
        
        Args:
            verification_type: Type of verification to start
            documents: Dictionary of document data
            
        Returns:
            dict: Verification request information
        """
        if verification_type not in self.VERIFICATION_TYPES:
            return {'success': False, 'error': 'Invalid verification type'}
        
        if not self.profile:
            return {'success': False, 'error': 'Profile not found'}
        
        # Check if verification is already in progress
        existing_request = ProfileVerificationRequest.objects.filter(
            user=self.user,
            verification_type=verification_type,
            status__in=['pending', 'in_review']
        ).first()
        
        if existing_request:
            return {
                'success': False,
                'error': 'Verification already in progress',
                'existing_request_id': existing_request.id
            }
        
        # Create verification request
        verification_request = ProfileVerificationRequest.objects.create(
            user=self.user,
            verification_type=verification_type,
            status='pending',
            submitted_at=timezone.now(),
            metadata={
                'user_agent': documents.get('user_agent', ''),
                'ip_address': documents.get('ip_address', ''),
            }
        )
        
        # Process uploaded documents
        uploaded_documents = []
        verification_info = self.VERIFICATION_TYPES[verification_type]
        
        for doc_type in verification_info['required_documents']:
            if doc_type in documents:
                doc_result = self._process_document(
                    verification_request, doc_type, documents[doc_type]
                )
                if doc_result['success']:
                    uploaded_documents.append(doc_result['document'])
                else:
                    # Cleanup and return error
                    verification_request.delete()
                    return doc_result
        
        # Update request status
        verification_request.status = 'submitted'
        verification_request.save()
        
        # Send notification to admins
        self._notify_admins_new_verification(verification_request)
        
        # Log verification start
        log_info(
            f'Verification started: {verification_type}',
            user=self.user,
            extra_data={
                'verification_id': verification_request.id,
                'verification_type': verification_type,
                'documents_count': len(uploaded_documents),
            }
        )
        
        return {
            'success': True,
            'verification_id': verification_request.id,
            'verification_type': verification_type,
            'status': verification_request.status,
            'estimated_processing_time': verification_info['processing_time'],
            'uploaded_documents': len(uploaded_documents),
        }
    
    def _process_document(self, verification_request: 'ProfileVerificationRequest',
                         document_type: str, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and validate uploaded document."""
        try:
            document_info = self.DOCUMENT_TYPES[document_type]
            uploaded_file = document_data.get('file')
            metadata = document_data.get('metadata', {})
            
            if not uploaded_file:
                return {'success': False, 'error': f'No file provided for {document_type}'}
            
            # Validate file
            validation_result = self._validate_document_file(uploaded_file, document_info)
            if not validation_result['success']:
                return validation_result
            
            # Create document record
            document = VerificationDocument.objects.create(
                verification_request=verification_request,
                document_type=document_type,
                file_name=uploaded_file.name,
                file_size=uploaded_file.size,
                file_path='',  # Would be set after saving to storage
                metadata=metadata,
                uploaded_at=timezone.now(),
            )
            
            # In a real implementation, save file to secure storage
            # document.file_path = save_to_secure_storage(uploaded_file)
            # document.save()
            
            return {
                'success': True,
                'document': {
                    'id': document.id,
                    'type': document_type,
                    'name': document.file_name,
                    'size': document.file_size,
                }
            }
            
        except Exception as e:
            log_error(
                error_type='document_processing_error',
                error_message=f'Error processing document: {document_type}',
                user=self.user,
                exception=e
            )
            return {'success': False, 'error': 'Failed to process document'}
    
    def _validate_document_file(self, uploaded_file: UploadedFile, 
                               document_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate uploaded document file."""
        # Check file size
        if uploaded_file.size > document_info['max_size']:
            max_size_mb = document_info['max_size'] / (1024 * 1024)
            return {
                'success': False,
                'error': f'File too large. Maximum size is {max_size_mb:.1f}MB'
            }
        
        # Check file format
        file_extension = uploaded_file.name.split('.')[-1].lower()
        if file_extension not in document_info['accepted_formats']:
            return {
                'success': False,
                'error': f'Invalid file format. Accepted formats: {", ".join(document_info["accepted_formats"])}'
            }
        
        # Additional security checks would go here
        # - Virus scanning
        # - Content validation
        # - Metadata stripping
        
        return {'success': True}
    
    def get_verification_status(self, verification_type: str = None) -> Dict[str, Any]:
        """
        Get verification status for user.
        
        Args:
            verification_type: Specific verification type to check
            
        Returns:
            dict: Verification status information
        """
        if verification_type:
            # Get specific verification status
            verification = ProfileVerificationRequest.objects.filter(
                user=self.user,
                verification_type=verification_type
            ).order_by('-submitted_at').first()
            
            if not verification:
                return {
                    'verification_type': verification_type,
                    'status': 'not_started',
                    'is_verified': False,
                }
            
            return self._serialize_verification_request(verification)
        else:
            # Get all verification statuses
            verifications = ProfileVerificationRequest.objects.filter(
                user=self.user
            ).order_by('-submitted_at')
            
            status_summary = {}
            for verification in verifications:
                v_type = verification.verification_type
                if v_type not in status_summary:
                    status_summary[v_type] = self._serialize_verification_request(verification)
            
            # Add not started verifications
            for v_type in self.VERIFICATION_TYPES:
                if v_type not in status_summary:
                    status_summary[v_type] = {
                        'verification_type': v_type,
                        'status': 'not_started',
                        'is_verified': False,
                    }
            
            return {
                'verifications': status_summary,
                'overall_verification_level': self._calculate_verification_level(status_summary),
            }
    
    def _serialize_verification_request(self, verification: 'ProfileVerificationRequest') -> Dict[str, Any]:
        """Serialize verification request for API response."""
        verification_info = self.VERIFICATION_TYPES[verification.verification_type]
        
        return {
            'id': verification.id,
            'verification_type': verification.verification_type,
            'verification_name': verification_info['name'],
            'status': verification.status,
            'is_verified': verification.status == 'approved',
            'submitted_at': verification.submitted_at.isoformat() if verification.submitted_at else None,
            'reviewed_at': verification.reviewed_at.isoformat() if verification.reviewed_at else None,
            'expires_at': verification.expires_at.isoformat() if verification.expires_at else None,
            'rejection_reason': verification.rejection_reason,
            'badge_info': {
                'color': verification_info['badge_color'],
                'icon': verification_info['badge_icon'],
            },
            'documents_count': verification.documents.count(),
            'processing_time': verification_info['processing_time'],
        }
    
    def _calculate_verification_level(self, verifications: Dict[str, Any]) -> str:
        """Calculate overall verification level."""
        verified_types = [
            v_type for v_type, info in verifications.items()
            if info.get('is_verified', False)
        ]
        
        if 'enhanced' in verified_types:
            return 'enhanced'
        elif 'business' in verified_types and 'identity' in verified_types:
            return 'business'
        elif 'professional' in verified_types and 'identity' in verified_types:
            return 'professional'
        elif 'identity' in verified_types and 'address' in verified_types:
            return 'verified'
        elif 'identity' in verified_types:
            return 'basic'
        else:
            return 'unverified'
    
    def get_verification_requirements(self, verification_type: str) -> Dict[str, Any]:
        """Get requirements for a specific verification type."""
        if verification_type not in self.VERIFICATION_TYPES:
            return {'error': 'Invalid verification type'}
        
        verification_info = self.VERIFICATION_TYPES[verification_type]
        
        requirements = {
            'verification_type': verification_type,
            'name': verification_info['name'],
            'description': verification_info['description'],
            'processing_time': verification_info['processing_time'],
            'required_documents': [],
        }
        
        for doc_type in verification_info['required_documents']:
            doc_info = self.DOCUMENT_TYPES[doc_type]
            requirements['required_documents'].append({
                'type': doc_type,
                'name': doc_info['name'],
                'description': doc_info['description'],
                'accepted_formats': doc_info['accepted_formats'],
                'max_size_mb': doc_info['max_size'] / (1024 * 1024),
                'required_fields': doc_info['required_fields'],
            })
        
        return requirements
    
    def _notify_admins_new_verification(self, verification_request: 'ProfileVerificationRequest'):
        """Send notification to admins about new verification request."""
        try:
            subject = f'New Verification Request - {verification_request.verification_type}'
            
            context = {
                'verification_request': verification_request,
                'user': self.user,
                'profile': self.profile,
                'admin_url': f'/admin/verification/{verification_request.id}/',
            }
            
            message = render_to_string(
                'accounts_app/emails/admin_verification_notification.html',
                context
            )
            
            # Send to admin emails
            admin_emails = getattr(settings, 'VERIFICATION_ADMIN_EMAILS', [])
            if admin_emails:
                email = EmailMessage(
                    subject=subject,
                    body=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=admin_emails,
                )
                email.content_subtype = 'html'
                email.send()
                
        except Exception as e:
            log_error(
                error_type='admin_notification_error',
                error_message='Failed to send admin verification notification',
                user=self.user,
                exception=e
            )


@login_required
@require_http_methods(["POST"])
def start_verification_ajax(request):
    """
    AJAX endpoint for starting verification process.
    """
    try:
        data = json.loads(request.body)
        verification_type = data.get('verification_type')
        documents = data.get('documents', {})

        if not verification_type:
            return JsonResponse({
                'success': False,
                'error': 'Verification type is required',
            }, status=400)

        # Add request metadata
        documents['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
        documents['ip_address'] = request.META.get('REMOTE_ADDR', '')

        verification_service = ProfileVerificationService(request.user)
        result = verification_service.start_verification(verification_type, documents)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='start_verification_ajax_error',
            error_message='Error starting verification via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to start verification',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def verification_status_ajax(request):
    """
    AJAX endpoint for getting verification status.
    """
    try:
        verification_type = request.GET.get('verification_type')
        verification_service = ProfileVerificationService(request.user)

        status = verification_service.get_verification_status(verification_type)

        return JsonResponse({
            'success': True,
            'status': status,
        })

    except Exception as e:
        log_error(
            error_type='verification_status_ajax_error',
            error_message='Error getting verification status via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to get verification status',
        }, status=500)


@login_required
@require_http_methods(["GET"])
def verification_requirements_ajax(request):
    """
    AJAX endpoint for getting verification requirements.
    """
    try:
        verification_type = request.GET.get('verification_type')

        if not verification_type:
            return JsonResponse({
                'success': False,
                'error': 'Verification type is required',
            }, status=400)

        verification_service = ProfileVerificationService(request.user)
        requirements = verification_service.get_verification_requirements(verification_type)

        if 'error' in requirements:
            return JsonResponse({
                'success': False,
                'error': requirements['error'],
            }, status=400)

        return JsonResponse({
            'success': True,
            'requirements': requirements,
        })

    except Exception as e:
        log_error(
            error_type='verification_requirements_ajax_error',
            error_message='Error getting verification requirements via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to get verification requirements',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def cancel_verification_ajax(request):
    """
    AJAX endpoint for cancelling verification request.
    """
    try:
        data = json.loads(request.body)
        verification_id = data.get('verification_id')

        if not verification_id:
            return JsonResponse({
                'success': False,
                'error': 'Verification ID is required',
            }, status=400)

        from ..models.verification import ProfileVerificationRequest

        try:
            verification = ProfileVerificationRequest.objects.get(
                id=verification_id,
                user=request.user,
                status__in=['pending', 'submitted', 'in_review']
            )
            verification.cancel()

            return JsonResponse({
                'success': True,
                'message': 'Verification request cancelled successfully',
            })

        except ProfileVerificationRequest.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Verification request not found or cannot be cancelled',
            }, status=404)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='cancel_verification_ajax_error',
            error_message='Error cancelling verification via AJAX',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'Failed to cancel verification',
        }, status=500)
