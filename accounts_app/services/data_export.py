# --- Standard Library Imports ---
import json
import csv
import io
from datetime import datetime
from typing import Dict, List, Any, Optional

# --- Django Imports ---
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone
from django.core.files.base import ContentFile
from django.core.mail import EmailMessage
from django.conf import settings

# --- Third-Party Imports ---
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

# --- Local App Imports ---
from ..models import CustomerProfile, ServiceProviderProfile, ProfilePrivacySettings, PrivacyConsent


class DataExportService:
    """
    Service for exporting user data in various formats for GDPR compliance.
    
    Features:
    - Multiple export formats (JSON, CSV, PDF)
    - Comprehensive data collection
    - Email delivery
    - Privacy-compliant data handling
    """
    
    def __init__(self, user):
        self.user = user
        self.export_timestamp = timezone.now()
    
    def collect_user_data(self, data_types: List[str] = None) -> Dict[str, Any]:
        """
        Collect all user data for export.
        
        Args:
            data_types: List of data types to include ('all' for everything)
            
        Returns:
            dict: Comprehensive user data
        """
        if not data_types or 'all' in data_types:
            data_types = ['profile', 'bookings', 'reviews', 'preferences', 'activity']
        
        data = {
            'export_info': {
                'user_id': self.user.id,
                'email': self.user.email,
                'export_date': self.export_timestamp.isoformat(),
                'data_types_included': data_types,
                'export_version': '1.0',
            },
            'user_account': self._get_user_account_data(),
        }
        
        if 'profile' in data_types:
            data['profile'] = self._get_profile_data()
        
        if 'preferences' in data_types:
            data['preferences'] = self._get_preferences_data()
        
        if 'bookings' in data_types:
            data['bookings'] = self._get_bookings_data()
        
        if 'reviews' in data_types:
            data['reviews'] = self._get_reviews_data()
        
        if 'activity' in data_types:
            data['activity'] = self._get_activity_data()
        
        return data
    
    def _get_user_account_data(self) -> Dict[str, Any]:
        """Get basic user account information."""
        return {
            'email': self.user.email,
            'date_joined': self.user.date_joined.isoformat(),
            'last_login': self.user.last_login.isoformat() if self.user.last_login else None,
            'is_active': self.user.is_active,
            'user_type': 'customer' if self.user.is_customer else 'service_provider' if self.user.is_service_provider else 'staff',
            'email_verified': getattr(self.user, 'emailaddress_set', None) and 
                            any(addr.verified for addr in self.user.emailaddress_set.all()),
        }
    
    def _get_profile_data(self) -> Dict[str, Any]:
        """Get profile information."""
        profile_data = {}
        
        if self.user.is_customer:
            try:
                profile = CustomerProfile.objects.get(user=self.user)
                profile_data = profile.export_data()
            except CustomerProfile.DoesNotExist:
                profile_data = {'error': 'Customer profile not found'}
        
        elif self.user.is_service_provider:
            try:
                profile = ServiceProviderProfile.objects.get(user=self.user)
                profile_data = profile.export_data()
            except ServiceProviderProfile.DoesNotExist:
                profile_data = {'error': 'Service provider profile not found'}
        
        return profile_data
    
    def _get_preferences_data(self) -> Dict[str, Any]:
        """Get user preferences and privacy settings."""
        preferences = {}
        
        # Privacy settings
        try:
            privacy_settings = ProfilePrivacySettings.objects.get(user=self.user)
            preferences['privacy_settings'] = {
                'profile_visibility': privacy_settings.get_profile_visibility_display(),
                'contact_permission': privacy_settings.get_contact_permission_display(),
                'data_sharing_level': privacy_settings.get_data_sharing_level_display(),
                'marketing_contact_allowed': privacy_settings.allow_marketing_contact,
                'promotional_contact_allowed': privacy_settings.allow_promotional_contact,
                'usage_analytics_allowed': privacy_settings.allow_usage_analytics,
                'personalized_ads_allowed': privacy_settings.allow_personalized_ads,
                'searchable_profile': privacy_settings.searchable_profile,
                'show_in_recommendations': privacy_settings.show_in_recommendations,
                'location_data_sharing': privacy_settings.share_location_data,
                'activity_tracking': privacy_settings.track_activity,
                'social_login_data_allowed': privacy_settings.allow_social_login_data,
                'partner_sharing_allowed': privacy_settings.share_with_partners,
                'last_reviewed': privacy_settings.last_reviewed_at.isoformat() if privacy_settings.last_reviewed_at else None,
            }
        except ProfilePrivacySettings.DoesNotExist:
            preferences['privacy_settings'] = {'error': 'Privacy settings not found'}
        
        # Privacy consents
        consents = PrivacyConsent.objects.filter(user=self.user).order_by('-timestamp')
        preferences['privacy_consents'] = [
            {
                'consent_type': consent.get_consent_type_display(),
                'is_granted': consent.is_granted,
                'consent_version': consent.consent_version,
                'timestamp': consent.timestamp.isoformat(),
                'expires_at': consent.expires_at.isoformat() if consent.expires_at else None,
            }
            for consent in consents
        ]
        
        return preferences
    
    def _get_bookings_data(self) -> Dict[str, Any]:
        """Get booking history data."""
        bookings_data = {'bookings': []}
        
        try:
            # Import here to avoid circular imports
            from bookings_app.models import Booking
            
            bookings = Booking.objects.filter(customer=self.user).order_by('-created_at')
            bookings_data['bookings'] = [
                {
                    'id': booking.id,
                    'venue_name': booking.venue.name if booking.venue else 'Unknown',
                    'service_name': booking.service.name if booking.service else 'Unknown',
                    'booking_date': booking.booking_date.isoformat() if booking.booking_date else None,
                    'status': booking.get_status_display() if hasattr(booking, 'get_status_display') else str(booking.status),
                    'total_amount': str(booking.total_amount) if hasattr(booking, 'total_amount') else None,
                    'created_at': booking.created_at.isoformat(),
                    'updated_at': booking.updated_at.isoformat() if hasattr(booking, 'updated_at') else None,
                }
                for booking in bookings
            ]
        except ImportError:
            bookings_data['error'] = 'Bookings app not available'
        except Exception as e:
            bookings_data['error'] = f'Error retrieving bookings: {str(e)}'
        
        return bookings_data
    
    def _get_reviews_data(self) -> Dict[str, Any]:
        """Get reviews and ratings data."""
        reviews_data = {'reviews': []}
        
        try:
            # Import here to avoid circular imports
            from reviews_app.models import Review
            
            reviews = Review.objects.filter(customer=self.user).order_by('-created_at')
            reviews_data['reviews'] = [
                {
                    'id': review.id,
                    'venue_name': review.venue.name if review.venue else 'Unknown',
                    'rating': review.rating,
                    'review_text': review.review_text,
                    'created_at': review.created_at.isoformat(),
                    'is_public': getattr(review, 'is_public', True),
                }
                for review in reviews
            ]
        except ImportError:
            reviews_data['error'] = 'Reviews app not available'
        except Exception as e:
            reviews_data['error'] = f'Error retrieving reviews: {str(e)}'
        
        return reviews_data
    
    def _get_activity_data(self) -> Dict[str, Any]:
        """Get user activity and audit trail data."""
        activity_data = {}
        
        # Profile changes
        try:
            from ..models import ProfileChangeHistory
            
            changes = ProfileChangeHistory.objects.filter(user=self.user).order_by('-timestamp')
            activity_data['profile_changes'] = [
                {
                    'change_type': change.get_change_type_display(),
                    'change_summary': change.change_summary,
                    'timestamp': change.timestamp.isoformat(),
                    'ip_address': change.ip_address,
                    'changed_by': change.changed_by.email if change.changed_by else 'Self',
                }
                for change in changes
            ]
        except ImportError:
            activity_data['profile_changes'] = {'error': 'Profile change history not available'}
        
        # Login history
        try:
            from ..models import LoginHistory
            
            logins = LoginHistory.objects.filter(user=self.user).order_by('-login_time')[:50]  # Last 50 logins
            activity_data['login_history'] = [
                {
                    'login_time': login.login_time.isoformat(),
                    'ip_address': login.ip_address,
                    'user_agent': login.user_agent,
                    'success': login.success,
                    'failure_reason': login.failure_reason,
                }
                for login in logins
            ]
        except Exception as e:
            activity_data['login_history'] = {'error': f'Error retrieving login history: {str(e)}'}
        
        return activity_data
    
    def export_as_json(self, data_types: List[str] = None) -> str:
        """
        Export data as JSON string.
        
        Args:
            data_types: List of data types to include
            
        Returns:
            str: JSON formatted data
        """
        data = self.collect_user_data(data_types)
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def export_as_csv(self, data_types: List[str] = None) -> str:
        """
        Export data as CSV string.
        
        Args:
            data_types: List of data types to include
            
        Returns:
            str: CSV formatted data
        """
        data = self.collect_user_data(data_types)
        output = io.StringIO()
        
        # Write basic user info
        writer = csv.writer(output)
        writer.writerow(['Data Export for User:', self.user.email])
        writer.writerow(['Export Date:', self.export_timestamp.strftime('%Y-%m-%d %H:%M:%S')])
        writer.writerow([])
        
        # Write each data section
        for section_name, section_data in data.items():
            if section_name == 'export_info':
                continue
                
            writer.writerow([f'=== {section_name.upper()} ==='])
            
            if isinstance(section_data, dict):
                for key, value in section_data.items():
                    if isinstance(value, (list, dict)):
                        writer.writerow([key, json.dumps(value)])
                    else:
                        writer.writerow([key, str(value)])
            else:
                writer.writerow(['Data', str(section_data)])
            
            writer.writerow([])
        
        return output.getvalue()
    
    def export_as_pdf(self, data_types: List[str] = None) -> bytes:
        """
        Export data as PDF bytes.
        
        Args:
            data_types: List of data types to include
            
        Returns:
            bytes: PDF file content
        """
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab is required for PDF export")
        
        data = self.collect_user_data(data_types)
        
        # Create PDF buffer
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        
        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
        )
        
        # Build PDF content
        story = []
        
        # Title
        story.append(Paragraph(f"Data Export for {self.user.email}", title_style))
        story.append(Paragraph(f"Generated on: {self.export_timestamp.strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Add each data section
        for section_name, section_data in data.items():
            if section_name == 'export_info':
                continue
                
            story.append(Paragraph(section_name.replace('_', ' ').title(), styles['Heading2']))
            
            if isinstance(section_data, dict):
                for key, value in section_data.items():
                    if isinstance(value, (list, dict)):
                        story.append(Paragraph(f"<b>{key}:</b> {json.dumps(value, indent=2)}", styles['Normal']))
                    else:
                        story.append(Paragraph(f"<b>{key}:</b> {str(value)}", styles['Normal']))
            else:
                story.append(Paragraph(str(section_data), styles['Normal']))
            
            story.append(Spacer(1, 12))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def create_export_file(self, format_type: str, data_types: List[str] = None) -> ContentFile:
        """
        Create export file in specified format.
        
        Args:
            format_type: 'json', 'csv', or 'pdf'
            data_types: List of data types to include
            
        Returns:
            ContentFile: File ready for download or email
        """
        filename = f"data_export_{self.user.id}_{self.export_timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        if format_type == 'json':
            content = self.export_as_json(data_types)
            file_content = ContentFile(content.encode('utf-8'))
            file_content.name = f"{filename}.json"
        elif format_type == 'csv':
            content = self.export_as_csv(data_types)
            file_content = ContentFile(content.encode('utf-8'))
            file_content.name = f"{filename}.csv"
        elif format_type == 'pdf':
            content = self.export_as_pdf(data_types)
            file_content = ContentFile(content)
            file_content.name = f"{filename}.pdf"
        else:
            raise ValueError(f"Unsupported format: {format_type}")
        
        return file_content
    
    def send_export_email(self, export_file: ContentFile, recipient_email: str = None):
        """
        Send export file via email.
        
        Args:
            export_file: The export file to send
            recipient_email: Email address (defaults to user's email)
        """
        if not recipient_email:
            recipient_email = self.user.email
        
        subject = f"Your Data Export from {settings.SITE_NAME}"
        message = f"""
        Dear {self.user.email},
        
        Your requested data export is attached to this email.
        
        Export Details:
        - Generated on: {self.export_timestamp.strftime('%Y-%m-%d %H:%M:%S')}
        - File format: {export_file.name.split('.')[-1].upper()}
        - File size: {len(export_file.read())} bytes
        
        This export contains all the personal data we have stored about you in our system.
        
        If you have any questions about this export or your data, please contact our support team.
        
        Best regards,
        The {settings.SITE_NAME} Team
        """
        
        # Reset file pointer
        export_file.seek(0)
        
        email = EmailMessage(
            subject=subject,
            body=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[recipient_email],
        )
        
        email.attach(export_file.name, export_file.read(), 'application/octet-stream')
        email.send()
        
        # Reset file pointer again
        export_file.seek(0)
