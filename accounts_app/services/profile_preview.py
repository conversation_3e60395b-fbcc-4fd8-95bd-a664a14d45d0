"""
Real-time profile preview service.

This module provides functionality for generating real-time profile previews
as users edit their profile information.
"""

from typing import Dict, Any, Optional
from django.template.loader import render_to_string
from django.http import JsonResponse
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from ..models import CustomerProfile, ServiceProviderProfile
from ..utils.logging import log_error, log_info


class ProfilePreviewService:
    """
    Service for generating real-time profile previews.
    
    Features:
    - Live profile data preview
    - Form validation feedback
    - Image preview generation
    - Completion percentage updates
    """
    
    def __init__(self, user, profile_type='customer'):
        """
        Initialize preview service.
        
        Args:
            user: User instance
            profile_type: Type of profile ('customer' or 'provider')
        """
        self.user = user
        self.profile_type = profile_type
        self.profile = self._get_profile()
    
    def _get_profile(self):
        """Get the appropriate profile instance."""
        if self.profile_type == 'customer':
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.profile_type == 'provider':
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def generate_preview(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate profile preview based on form data.
        
        Args:
            form_data: Dictionary of form field values
            
        Returns:
            dict: Preview data including HTML and metadata
        """
        try:
            # Create temporary profile instance with form data
            preview_profile = self._create_preview_profile(form_data)
            
            # Generate preview HTML
            preview_html = self._render_preview_html(preview_profile)
            
            # Calculate completion percentage
            completion_data = self._calculate_completion_preview(preview_profile)
            
            # Validate form data
            validation_results = self._validate_preview_data(form_data)
            
            return {
                'success': True,
                'preview_html': preview_html,
                'completion_percentage': completion_data['percentage'],
                'completion_details': completion_data['details'],
                'validation_results': validation_results,
                'profile_data': self._serialize_profile_data(preview_profile),
            }
            
        except Exception as e:
            log_error(
                error_type='profile_preview_error',
                error_message='Error generating profile preview',
                user=self.user,
                exception=e,
                extra_data={'form_data': form_data}
            )
            return {
                'success': False,
                'error': str(e),
            }
    
    def _create_preview_profile(self, form_data: Dict[str, Any]):
        """Create a temporary profile instance with form data."""
        if not self.profile:
            return None
        
        # Create a copy of the profile
        preview_profile = type(self.profile)()
        
        # Copy existing data
        for field in self.profile._meta.fields:
            if hasattr(self.profile, field.name):
                setattr(preview_profile, field.name, getattr(self.profile, field.name))
        
        # Apply form data
        for field_name, value in form_data.items():
            if hasattr(preview_profile, field_name):
                # Handle special field types
                if field_name in ['birth_month', 'birth_year'] and value == '':
                    value = None
                elif field_name == 'profile_picture' and isinstance(value, str):
                    # Skip file fields in preview
                    continue
                
                setattr(preview_profile, field_name, value)
        
        return preview_profile
    
    def _render_preview_html(self, preview_profile) -> str:
        """Render profile preview HTML."""
        if not preview_profile:
            return '<div class="alert alert-warning">Profile not available for preview.</div>'
        
        template_name = f'accounts_app/{self.profile_type}/profile_preview.html'
        
        try:
            return render_to_string(template_name, {
                'profile': preview_profile,
                'user': self.user,
                'is_preview': True,
            })
        except Exception:
            # Fallback to basic preview
            return self._render_basic_preview(preview_profile)
    
    def _render_basic_preview(self, preview_profile) -> str:
        """Render basic profile preview as fallback."""
        preview_data = []
        
        if self.profile_type == 'customer':
            fields = [
                ('first_name', 'First Name'),
                ('last_name', 'Last Name'),
                ('phone_number', 'Phone'),
                ('gender', 'Gender'),
                ('address', 'Address'),
                ('city', 'City'),
                ('zip_code', 'ZIP Code'),
            ]
        else:
            fields = [
                ('legal_name', 'Business Name'),
                ('display_name', 'DBA Name'),
                ('phone', 'Phone'),
                ('contact_name', 'Contact Name'),
                ('address', 'Address'),
                ('city', 'City'),
                ('state', 'State'),
                ('zip_code', 'ZIP Code'),
            ]
        
        for field_name, label in fields:
            value = getattr(preview_profile, field_name, None)
            if value:
                preview_data.append(f'<div><strong>{label}:</strong> {value}</div>')
        
        return '<div class="profile-preview">' + ''.join(preview_data) + '</div>'
    
    def _calculate_completion_preview(self, preview_profile) -> Dict[str, Any]:
        """Calculate completion percentage for preview profile."""
        if not preview_profile:
            return {'percentage': 0, 'details': {}}
        
        # Use the profile's completion calculation method
        if hasattr(preview_profile, 'calculate_completion_percentage'):
            percentage = preview_profile.calculate_completion_percentage()
        else:
            percentage = 0
        
        # Get completion details
        details = {}
        if hasattr(preview_profile, 'get_completion_suggestions'):
            try:
                suggestions = preview_profile.get_completion_suggestions()
                details['suggestions'] = suggestions
            except Exception:
                pass
        
        return {
            'percentage': percentage,
            'details': details,
        }
    
    def _validate_preview_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate form data for preview."""
        validation_results = {
            'is_valid': True,
            'field_errors': {},
            'warnings': [],
        }
        
        # Basic validation rules
        if self.profile_type == 'customer':
            validation_rules = {
                'first_name': {'min_length': 1, 'max_length': 100},
                'last_name': {'min_length': 1, 'max_length': 100},
                'phone_number': {'pattern': r'^\+?[\d\s\-\(\)]+$'},
                'zip_code': {'pattern': r'^\d{5}(-\d{4})?$'},
            }
        else:
            validation_rules = {
                'legal_name': {'min_length': 2, 'max_length': 200},
                'phone': {'pattern': r'^\+?[\d\s\-\(\)]+$'},
                'zip_code': {'pattern': r'^\d{5}(-\d{4})?$'},
                'ein': {'pattern': r'^\d{2}-\d{7}$'},
            }
        
        # Validate each field
        for field_name, rules in validation_rules.items():
            value = form_data.get(field_name, '')
            
            if not value:
                continue
            
            # Length validation
            if 'min_length' in rules and len(value) < rules['min_length']:
                validation_results['field_errors'][field_name] = f'Minimum length is {rules["min_length"]} characters'
                validation_results['is_valid'] = False
            
            if 'max_length' in rules and len(value) > rules['max_length']:
                validation_results['field_errors'][field_name] = f'Maximum length is {rules["max_length"]} characters'
                validation_results['is_valid'] = False
            
            # Pattern validation
            if 'pattern' in rules:
                import re
                if not re.match(rules['pattern'], value):
                    validation_results['field_errors'][field_name] = 'Invalid format'
                    validation_results['is_valid'] = False
        
        return validation_results
    
    def _serialize_profile_data(self, preview_profile) -> Dict[str, Any]:
        """Serialize profile data for JSON response."""
        if not preview_profile:
            return {}
        
        data = {}
        
        # Get basic fields
        if self.profile_type == 'customer':
            fields = ['first_name', 'last_name', 'phone_number', 'gender', 'address', 'city', 'zip_code']
        else:
            fields = ['legal_name', 'display_name', 'phone', 'contact_name', 'address', 'city', 'state', 'zip_code']
        
        for field_name in fields:
            value = getattr(preview_profile, field_name, None)
            if value is not None:
                data[field_name] = str(value)
        
        return data


@login_required
@require_http_methods(["POST"])
def profile_preview_ajax(request):
    """
    AJAX endpoint for real-time profile preview.
    
    Expects JSON data with form fields to preview.
    """
    try:
        # Parse JSON data
        form_data = json.loads(request.body)
        
        # Determine profile type
        profile_type = 'customer' if request.user.is_customer else 'provider'
        
        # Generate preview
        preview_service = ProfilePreviewService(request.user, profile_type)
        preview_data = preview_service.generate_preview(form_data)
        
        return JsonResponse(preview_data)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
        }, status=400)
    except Exception as e:
        log_error(
            error_type='profile_preview_ajax_error',
            error_message='Error in profile preview AJAX endpoint',
            user=request.user,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'error': 'An error occurred while generating preview',
        }, status=500)


@login_required
@require_http_methods(["POST"])
def profile_completion_preview_ajax(request):
    """
    AJAX endpoint for profile completion percentage preview.
    """
    try:
        form_data = json.loads(request.body)
        profile_type = 'customer' if request.user.is_customer else 'provider'
        
        preview_service = ProfilePreviewService(request.user, profile_type)
        preview_profile = preview_service._create_preview_profile(form_data)
        completion_data = preview_service._calculate_completion_preview(preview_profile)
        
        return JsonResponse({
            'success': True,
            'completion_percentage': completion_data['percentage'],
            'completion_details': completion_data['details'],
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
        }, status=500)
