# --- Standard Library Imports ---
import csv
import json
import io
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

# --- Django Imports ---
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.core.files.base import ContentFile
from django.db.models import Q, Count, Avg
from django.contrib.auth import get_user_model

# --- Local App Imports ---
from ..models import CustomerProfile, ServiceProviderProfile, ProfileChangeHistory, ProfilePrivacySettings

User = get_user_model()


class BulkProfileManagementService:
    """
    Service for bulk profile management operations.
    
    Features:
    - Bulk profile import/export
    - Batch profile updates
    - Administrative profile management
    - Profile analytics and reporting
    """
    
    def __init__(self, admin_user=None):
        self.admin_user = admin_user
    
    def export_profiles(self, user_type: str = 'all', format_type: str = 'csv', 
                       filters: Dict = None) -> Union[str, bytes]:
        """
        Export profiles in bulk.
        
        Args:
            user_type: 'customer', 'service_provider', or 'all'
            format_type: 'csv', 'json', or 'excel'
            filters: Additional filters to apply
            
        Returns:
            str or bytes: Exported data
        """
        # Build queryset based on user type
        if user_type == 'customer':
            profiles = CustomerProfile.objects.select_related('user').all()
        elif user_type == 'service_provider':
            profiles = ServiceProviderProfile.objects.select_related('user').all()
        else:
            # Export both types (more complex)
            return self._export_mixed_profiles(format_type, filters)
        
        # Apply filters
        if filters:
            profiles = self._apply_filters(profiles, filters)
        
        # Export based on format
        if format_type == 'csv':
            return self._export_to_csv(profiles, user_type)
        elif format_type == 'json':
            return self._export_to_json(profiles, user_type)
        elif format_type == 'excel':
            return self._export_to_excel(profiles, user_type)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _apply_filters(self, queryset, filters: Dict):
        """Apply filters to profile queryset."""
        if filters.get('verified_only'):
            queryset = queryset.filter(is_verified=True)
        
        if filters.get('completion_min'):
            queryset = queryset.filter(
                profile_completion_percentage__gte=filters['completion_min']
            )
        
        if filters.get('created_after'):
            queryset = queryset.filter(created_at__gte=filters['created_after'])
        
        if filters.get('created_before'):
            queryset = queryset.filter(created_at__lte=filters['created_before'])
        
        if filters.get('city'):
            queryset = queryset.filter(city__icontains=filters['city'])
        
        if filters.get('state'):
            queryset = queryset.filter(state=filters['state'])
        
        return queryset
    
    def _export_to_csv(self, profiles, user_type: str) -> str:
        """Export profiles to CSV format."""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Define headers based on user type
        if user_type == 'customer':
            headers = [
                'User ID', 'Email', 'First Name', 'Last Name', 'Phone Number',
                'Address', 'City', 'ZIP Code', 'Birth Month', 'Birth Year',
                'Profile Completion %', 'Is Verified', 'Created At', 'Updated At'
            ]
            writer.writerow(headers)
            
            for profile in profiles:
                writer.writerow([
                    profile.user.id,
                    profile.user.email,
                    profile.first_name,
                    profile.last_name,
                    profile.phone_number,
                    profile.address,
                    profile.city,
                    profile.zip_code,
                    profile.birth_month,
                    profile.birth_year,
                    profile.profile_completion_percentage,
                    profile.is_verified,
                    profile.created_at,
                    profile.updated_at,
                ])
        
        else:  # service_provider
            headers = [
                'User ID', 'Email', 'Legal Name', 'Display Name', 'Phone',
                'Contact Name', 'Address', 'City', 'State', 'ZIP Code',
                'Description', 'Website', 'Profile Completion %', 'Is Verified',
                'Created At', 'Updated At'
            ]
            writer.writerow(headers)
            
            for profile in profiles:
                writer.writerow([
                    profile.user.id,
                    profile.user.email,
                    profile.legal_name,
                    profile.display_name,
                    profile.phone,
                    profile.contact_name,
                    profile.address,
                    profile.city,
                    profile.get_state_display() if profile.state else '',
                    profile.zip_code,
                    profile.description,
                    profile.website,
                    profile.profile_completion_percentage,
                    profile.is_verified,
                    profile.created,
                    profile.updated,
                ])
        
        return output.getvalue()
    
    def _export_to_json(self, profiles, user_type: str) -> str:
        """Export profiles to JSON format."""
        data = []
        
        for profile in profiles:
            if user_type == 'customer':
                profile_data = {
                    'user_id': profile.user.id,
                    'email': profile.user.email,
                    'first_name': profile.first_name,
                    'last_name': profile.last_name,
                    'phone_number': profile.phone_number,
                    'address': profile.address,
                    'city': profile.city,
                    'zip_code': profile.zip_code,
                    'birth_month': profile.birth_month,
                    'birth_year': profile.birth_year,
                    'profile_completion_percentage': profile.profile_completion_percentage,
                    'is_verified': profile.is_verified,
                    'created_at': profile.created_at.isoformat(),
                    'updated_at': profile.updated_at.isoformat(),
                }
            else:  # service_provider
                profile_data = {
                    'user_id': profile.user.id,
                    'email': profile.user.email,
                    'legal_name': profile.legal_name,
                    'display_name': profile.display_name,
                    'phone': profile.phone,
                    'contact_name': profile.contact_name,
                    'address': profile.address,
                    'city': profile.city,
                    'state': profile.state,
                    'zip_code': profile.zip_code,
                    'description': profile.description,
                    'website': profile.website,
                    'profile_completion_percentage': profile.profile_completion_percentage,
                    'is_verified': profile.is_verified,
                    'created': profile.created.isoformat(),
                    'updated': profile.updated.isoformat(),
                }
            
            data.append(profile_data)
        
        return json.dumps(data, indent=2, default=str)
    
    def _export_to_excel(self, profiles, user_type: str) -> bytes:
        """Export profiles to Excel format."""
        try:
            import openpyxl
            from openpyxl.utils.dataframe import dataframe_to_rows
            import pandas as pd
        except ImportError:
            raise ImportError("openpyxl and pandas are required for Excel export")
        
        # Convert to DataFrame
        data = []
        for profile in profiles:
            if user_type == 'customer':
                data.append({
                    'User ID': profile.user.id,
                    'Email': profile.user.email,
                    'First Name': profile.first_name,
                    'Last Name': profile.last_name,
                    'Phone Number': profile.phone_number,
                    'Address': profile.address,
                    'City': profile.city,
                    'ZIP Code': profile.zip_code,
                    'Profile Completion %': profile.profile_completion_percentage,
                    'Is Verified': profile.is_verified,
                    'Created At': profile.created_at,
                    'Updated At': profile.updated_at,
                })
            else:
                data.append({
                    'User ID': profile.user.id,
                    'Email': profile.user.email,
                    'Legal Name': profile.legal_name,
                    'Display Name': profile.display_name,
                    'Phone': profile.phone,
                    'Contact Name': profile.contact_name,
                    'Address': profile.address,
                    'City': profile.city,
                    'State': profile.get_state_display() if profile.state else '',
                    'ZIP Code': profile.zip_code,
                    'Profile Completion %': profile.profile_completion_percentage,
                    'Is Verified': profile.is_verified,
                    'Created': profile.created,
                    'Updated': profile.updated,
                })
        
        df = pd.DataFrame(data)
        
        # Create Excel file
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=f'{user_type.title()} Profiles', index=False)
        
        output.seek(0)
        return output.getvalue()
    
    def import_profiles(self, file_content: str, file_format: str, user_type: str,
                       update_existing: bool = False) -> Dict[str, Any]:
        """
        Import profiles from file.
        
        Args:
            file_content: File content as string
            file_format: 'csv' or 'json'
            user_type: 'customer' or 'service_provider'
            update_existing: Whether to update existing profiles
            
        Returns:
            dict: Import results
        """
        results = {
            'total_processed': 0,
            'created': 0,
            'updated': 0,
            'errors': [],
            'warnings': [],
        }
        
        try:
            if file_format == 'csv':
                data = self._parse_csv_import(file_content)
            elif file_format == 'json':
                data = json.loads(file_content)
            else:
                raise ValueError(f"Unsupported format: {file_format}")
            
            with transaction.atomic():
                for i, row_data in enumerate(data):
                    try:
                        result = self._import_single_profile(
                            row_data, user_type, update_existing
                        )
                        
                        results['total_processed'] += 1
                        if result['created']:
                            results['created'] += 1
                        elif result['updated']:
                            results['updated'] += 1
                        
                        if result.get('warnings'):
                            results['warnings'].extend(result['warnings'])
                            
                    except Exception as e:
                        results['errors'].append({
                            'row': i + 1,
                            'error': str(e),
                            'data': row_data
                        })
            
        except Exception as e:
            results['errors'].append({
                'general': str(e)
            })
        
        return results
    
    def _parse_csv_import(self, csv_content: str) -> List[Dict]:
        """Parse CSV content for import."""
        reader = csv.DictReader(io.StringIO(csv_content))
        return list(reader)
    
    def _import_single_profile(self, data: Dict, user_type: str, 
                              update_existing: bool) -> Dict[str, Any]:
        """Import a single profile record."""
        result = {'created': False, 'updated': False, 'warnings': []}
        
        # Get or create user
        email = data.get('email') or data.get('Email')
        if not email:
            raise ValueError("Email is required")
        
        user, user_created = User.objects.get_or_create(
            email=email,
            defaults={'is_active': True}
        )
        
        # Get or create profile
        if user_type == 'customer':
            profile, profile_created = CustomerProfile.objects.get_or_create(
                user=user
            )
            
            # Update profile fields
            if profile_created or update_existing:
                profile.first_name = data.get('first_name', data.get('First Name', ''))
                profile.last_name = data.get('last_name', data.get('Last Name', ''))
                profile.phone_number = data.get('phone_number', data.get('Phone Number', ''))
                profile.address = data.get('address', data.get('Address', ''))
                profile.city = data.get('city', data.get('City', ''))
                profile.zip_code = data.get('zip_code', data.get('ZIP Code', ''))
                
                # Handle birth month/year
                birth_month = data.get('birth_month', data.get('Birth Month'))
                if birth_month:
                    try:
                        profile.birth_month = int(birth_month)
                    except (ValueError, TypeError):
                        result['warnings'].append(f"Invalid birth month: {birth_month}")
                
                birth_year = data.get('birth_year', data.get('Birth Year'))
                if birth_year:
                    try:
                        profile.birth_year = int(birth_year)
                    except (ValueError, TypeError):
                        result['warnings'].append(f"Invalid birth year: {birth_year}")
                
                profile.save()
                profile.update_completion_percentage()
                
                result['created'] = profile_created
                result['updated'] = not profile_created
        
        else:  # service_provider
            profile, profile_created = ServiceProviderProfile.objects.get_or_create(
                user=user
            )
            
            if profile_created or update_existing:
                profile.legal_name = data.get('legal_name', data.get('Legal Name', ''))
                profile.display_name = data.get('display_name', data.get('Display Name', ''))
                profile.phone = data.get('phone', data.get('Phone', ''))
                profile.contact_name = data.get('contact_name', data.get('Contact Name', ''))
                profile.address = data.get('address', data.get('Address', ''))
                profile.city = data.get('city', data.get('City', ''))
                profile.zip_code = data.get('zip_code', data.get('ZIP Code', ''))
                profile.description = data.get('description', data.get('Description', ''))
                profile.website = data.get('website', data.get('Website', ''))
                
                # Handle state
                state = data.get('state', data.get('State'))
                if state:
                    # Map state name to code if needed
                    profile.state = state[:2].upper()  # Simplified
                
                profile.save()
                profile.update_completion_percentage()
                
                result['created'] = profile_created
                result['updated'] = not profile_created
        
        # Log the import
        if self.admin_user:
            ProfileChangeHistory.log_change(
                profile=profile,
                change_type='admin_update',
                change_summary=f'Bulk import by {self.admin_user.email}',
                changed_by=self.admin_user
            )
        
        return result
    
    def batch_update_profiles(self, profile_ids: List[int], updates: Dict[str, Any],
                             user_type: str) -> Dict[str, Any]:
        """
        Batch update multiple profiles.
        
        Args:
            profile_ids: List of profile IDs to update
            updates: Dictionary of field updates
            user_type: 'customer' or 'service_provider'
            
        Returns:
            dict: Update results
        """
        results = {
            'total_processed': 0,
            'updated': 0,
            'errors': [],
        }
        
        try:
            # Get profiles
            if user_type == 'customer':
                profiles = CustomerProfile.objects.filter(id__in=profile_ids)
            else:
                profiles = ServiceProviderProfile.objects.filter(id__in=profile_ids)
            
            with transaction.atomic():
                for profile in profiles:
                    try:
                        # Apply updates
                        changed_fields = {}
                        for field_name, new_value in updates.items():
                            if hasattr(profile, field_name):
                                old_value = getattr(profile, field_name)
                                setattr(profile, field_name, new_value)
                                changed_fields[field_name] = {
                                    'old': str(old_value) if old_value is not None else None,
                                    'new': str(new_value)
                                }
                        
                        profile.save()
                        profile.update_completion_percentage()
                        
                        # Log the change
                        if self.admin_user and changed_fields:
                            ProfileChangeHistory.log_change(
                                profile=profile,
                                change_type='admin_update',
                                changed_fields=changed_fields,
                                change_summary=f'Batch update by {self.admin_user.email}',
                                changed_by=self.admin_user
                            )
                        
                        results['updated'] += 1
                        
                    except Exception as e:
                        results['errors'].append({
                            'profile_id': profile.id,
                            'error': str(e)
                        })
                    
                    results['total_processed'] += 1
        
        except Exception as e:
            results['errors'].append({
                'general': str(e)
            })
        
        return results
    
    def get_profile_analytics(self, user_type: str = 'all') -> Dict[str, Any]:
        """
        Get profile analytics and statistics.
        
        Args:
            user_type: 'customer', 'service_provider', or 'all'
            
        Returns:
            dict: Analytics data
        """
        analytics = {}
        
        if user_type in ['customer', 'all']:
            customer_profiles = CustomerProfile.objects.all()
            analytics['customers'] = {
                'total_count': customer_profiles.count(),
                'verified_count': customer_profiles.filter(is_verified=True).count(),
                'avg_completion': customer_profiles.aggregate(
                    avg=Avg('profile_completion_percentage')
                )['avg'] or 0,
                'completion_distribution': self._get_completion_distribution(customer_profiles),
                'recent_signups': customer_profiles.filter(
                    created_at__gte=timezone.now() - timedelta(days=30)
                ).count(),
            }
        
        if user_type in ['service_provider', 'all']:
            provider_profiles = ServiceProviderProfile.objects.all()
            analytics['service_providers'] = {
                'total_count': provider_profiles.count(),
                'verified_count': provider_profiles.filter(is_verified=True).count(),
                'avg_completion': provider_profiles.aggregate(
                    avg=Avg('profile_completion_percentage')
                )['avg'] or 0,
                'completion_distribution': self._get_completion_distribution(provider_profiles),
                'recent_signups': provider_profiles.filter(
                    created__gte=timezone.now() - timedelta(days=30)
                ).count(),
            }
        
        return analytics
    
    def _get_completion_distribution(self, queryset) -> Dict[str, int]:
        """Get distribution of profile completion percentages."""
        return {
            '0-25%': queryset.filter(profile_completion_percentage__lt=25).count(),
            '25-50%': queryset.filter(
                profile_completion_percentage__gte=25,
                profile_completion_percentage__lt=50
            ).count(),
            '50-75%': queryset.filter(
                profile_completion_percentage__gte=50,
                profile_completion_percentage__lt=75
            ).count(),
            '75-100%': queryset.filter(profile_completion_percentage__gte=75).count(),
        }
