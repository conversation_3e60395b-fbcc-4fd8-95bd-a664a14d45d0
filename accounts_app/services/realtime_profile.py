# --- Standard Library Imports ---
import json
from typing import Dict, List, Any, Optional

# --- Django Imports ---
from django.core.exceptions import ValidationError
from django.forms.models import model_to_dict
from django.utils import timezone
from django.core.validators import validate_email
from django.core.files.uploadedfile import InMemoryUploadedFile

# --- Local App Imports ---
from ..models import CustomerProfile, ServiceProviderProfile, ProfileChangeHistory
from ..forms.customer import CustomerProfileForm
from ..forms.provider import ServiceProviderProfileForm


class RealTimeProfileService:
    """
    Service for real-time profile preview and editing functionality.
    
    Features:
    - Live profile preview
    - Inline field editing
    - Instant validation
    - Auto-save functionality
    """
    
    def __init__(self, user):
        self.user = user
        self.profile = self._get_profile()
        self.user_type = 'customer' if user.is_customer else 'service_provider'
    
    def _get_profile(self):
        """Get the user's profile."""
        if self.user.is_customer:
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.user.is_service_provider:
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def get_profile_data(self) -> Dict[str, Any]:
        """
        Get current profile data for preview.
        
        Returns:
            dict: Profile data
        """
        if not self.profile:
            return {}
        
        # Convert model to dict
        profile_data = model_to_dict(self.profile)
        
        # Add computed fields
        profile_data.update({
            'completion_percentage': self.profile.profile_completion_percentage,
            'is_verified': self.profile.is_verified,
            'verification_date': self.profile.verification_date.isoformat() if self.profile.verification_date else None,
            'created_at': self.profile.created_at.isoformat() if hasattr(self.profile, 'created_at') else None,
            'updated_at': self.profile.updated_at.isoformat() if hasattr(self.profile, 'updated_at') else None,
        })
        
        # Format file fields
        if 'profile_picture' in profile_data and profile_data['profile_picture']:
            profile_data['profile_picture_url'] = self.profile.profile_picture.url
        if 'logo' in profile_data and profile_data['logo']:
            profile_data['logo_url'] = self.profile.logo.url
        
        # Add display values for choice fields
        if self.user_type == 'customer':
            if profile_data.get('gender'):
                profile_data['gender_display'] = self.profile.get_gender_display()
            if profile_data.get('birth_month'):
                profile_data['birth_month_display'] = self.profile.birth_month_name
        else:
            if profile_data.get('state'):
                profile_data['state_display'] = self.profile.get_state_display()
        
        return profile_data
    
    def validate_field(self, field_name: str, field_value: Any) -> Dict[str, Any]:
        """
        Validate a single field value in real-time.
        
        Args:
            field_name: Name of the field to validate
            field_value: Value to validate
            
        Returns:
            dict: Validation result
        """
        try:
            # Get appropriate form class
            if self.user_type == 'customer':
                form_class = CustomerProfileForm
            else:
                form_class = ServiceProviderProfileForm
            
            # Create form instance with current data
            current_data = model_to_dict(self.profile) if self.profile else {}
            current_data[field_name] = field_value
            
            # Create form for validation
            form = form_class(data=current_data, instance=self.profile)
            
            # Validate specific field
            try:
                form.full_clean()
                field_errors = form.errors.get(field_name, [])
                
                if field_errors:
                    return {
                        'is_valid': False,
                        'errors': field_errors,
                        'formatted_value': field_value,
                    }
                else:
                    # Field is valid, get formatted value
                    formatted_value = self._format_field_value(field_name, field_value)
                    return {
                        'is_valid': True,
                        'errors': [],
                        'formatted_value': formatted_value,
                        'suggestions': self._get_field_suggestions(field_name, field_value),
                    }
                    
            except ValidationError as e:
                return {
                    'is_valid': False,
                    'errors': [str(e)],
                    'formatted_value': field_value,
                }
                
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f'Validation error: {str(e)}'],
                'formatted_value': field_value,
            }
    
    def _format_field_value(self, field_name: str, field_value: Any) -> Any:
        """
        Format field value for display.
        
        Args:
            field_name: Name of the field
            field_value: Raw field value
            
        Returns:
            Any: Formatted value
        """
        # Phone number formatting
        if field_name in ['phone_number', 'phone'] and field_value:
            # Simple phone formatting (could be enhanced)
            digits = ''.join(filter(str.isdigit, str(field_value)))
            if len(digits) == 10:
                return f'({digits[:3]}) {digits[3:6]}-{digits[6:]}'
            elif len(digits) == 11 and digits[0] == '1':
                return f'+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}'
        
        # ZIP code formatting
        if field_name == 'zip_code' and field_value:
            digits = ''.join(filter(str.isdigit, str(field_value)))
            if len(digits) == 5:
                return digits
            elif len(digits) == 9:
                return f'{digits[:5]}-{digits[5:]}'
        
        # Capitalize names
        if field_name in ['first_name', 'last_name', 'contact_name', 'legal_name', 'display_name'] and field_value:
            return str(field_value).title()
        
        # Capitalize city
        if field_name == 'city' and field_value:
            return str(field_value).title()
        
        return field_value
    
    def _get_field_suggestions(self, field_name: str, field_value: Any) -> List[str]:
        """
        Get suggestions for field completion.
        
        Args:
            field_name: Name of the field
            field_value: Current field value
            
        Returns:
            list: Suggestions
        """
        suggestions = []
        
        # Email suggestions
        if field_name == 'email' and field_value and '@' not in str(field_value):
            common_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
            suggestions = [f'{field_value}@{domain}' for domain in common_domains]
        
        # Website suggestions
        if field_name == 'website' and field_value and not str(field_value).startswith('http'):
            suggestions = [f'https://{field_value}', f'https://www.{field_value}']
        
        return suggestions
    
    def update_field(self, field_name: str, field_value: Any, auto_save: bool = True) -> Dict[str, Any]:
        """
        Update a single field with real-time validation and optional auto-save.
        
        Args:
            field_name: Name of the field to update
            field_value: New field value
            auto_save: Whether to save immediately
            
        Returns:
            dict: Update result
        """
        if not self.profile:
            return {
                'success': False,
                'message': 'Profile not found',
            }
        
        try:
            # Validate field first
            validation_result = self.validate_field(field_name, field_value)
            
            if not validation_result['is_valid']:
                return {
                    'success': False,
                    'message': 'Validation failed',
                    'errors': validation_result['errors'],
                    'field_name': field_name,
                }
            
            # Store old value for change tracking
            old_value = getattr(self.profile, field_name, None)
            
            # Update field
            setattr(self.profile, field_name, validation_result['formatted_value'])
            
            if auto_save:
                # Save the profile
                self.profile.save(update_fields=[field_name])
                
                # Update completion percentage
                self.profile.update_completion_percentage()
                
                # Log the change
                try:
                    ProfileChangeHistory.log_change(
                        profile=self.profile,
                        change_type='update',
                        changed_fields={
                            field_name: {
                                'old': str(old_value) if old_value is not None else None,
                                'new': str(validation_result['formatted_value'])
                            }
                        },
                        change_summary=f'Real-time update: {field_name}'
                    )
                except Exception:
                    pass  # Don't fail the update if logging fails
            
            return {
                'success': True,
                'message': 'Field updated successfully',
                'field_name': field_name,
                'old_value': old_value,
                'new_value': validation_result['formatted_value'],
                'completion_percentage': self.profile.profile_completion_percentage,
                'auto_saved': auto_save,
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error updating field: {str(e)}',
                'field_name': field_name,
            }
    
    def bulk_update_fields(self, field_updates: Dict[str, Any], auto_save: bool = True) -> Dict[str, Any]:
        """
        Update multiple fields at once.
        
        Args:
            field_updates: Dictionary of field_name: field_value
            auto_save: Whether to save immediately
            
        Returns:
            dict: Bulk update result
        """
        if not self.profile:
            return {
                'success': False,
                'message': 'Profile not found',
            }
        
        results = {}
        changed_fields = {}
        update_fields = []
        
        try:
            # Validate and update each field
            for field_name, field_value in field_updates.items():
                validation_result = self.validate_field(field_name, field_value)
                
                if validation_result['is_valid']:
                    old_value = getattr(self.profile, field_name, None)
                    setattr(self.profile, field_name, validation_result['formatted_value'])
                    
                    results[field_name] = {
                        'success': True,
                        'old_value': old_value,
                        'new_value': validation_result['formatted_value'],
                    }
                    
                    changed_fields[field_name] = {
                        'old': str(old_value) if old_value is not None else None,
                        'new': str(validation_result['formatted_value'])
                    }
                    
                    update_fields.append(field_name)
                else:
                    results[field_name] = {
                        'success': False,
                        'errors': validation_result['errors'],
                    }
            
            if auto_save and update_fields:
                # Save all updated fields
                self.profile.save(update_fields=update_fields)
                
                # Update completion percentage
                self.profile.update_completion_percentage()
                
                # Log the changes
                try:
                    ProfileChangeHistory.log_change(
                        profile=self.profile,
                        change_type='update',
                        changed_fields=changed_fields,
                        change_summary=f'Bulk update: {", ".join(update_fields)}'
                    )
                except Exception:
                    pass  # Don't fail the update if logging fails
            
            successful_updates = sum(1 for result in results.values() if result['success'])
            
            return {
                'success': successful_updates > 0,
                'message': f'Updated {successful_updates} of {len(field_updates)} fields',
                'results': results,
                'completion_percentage': self.profile.profile_completion_percentage,
                'auto_saved': auto_save,
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error during bulk update: {str(e)}',
                'results': results,
            }
    
    def get_preview_html(self, template_name: str = None) -> str:
        """
        Get HTML preview of the profile.
        
        Args:
            template_name: Custom template name
            
        Returns:
            str: HTML preview
        """
        from django.template.loader import render_to_string
        
        if not template_name:
            template_name = f'dashboard_app/profile_preview_{self.user_type}.html'
        
        profile_data = self.get_profile_data()
        
        try:
            return render_to_string(template_name, {
                'profile': self.profile,
                'profile_data': profile_data,
                'user': self.user,
            })
        except Exception:
            # Fallback to basic preview
            return f'<div class="profile-preview">Profile for {self.user.email}</div>'
    
    def auto_save_session_data(self, session_key: str, field_data: Dict[str, Any]) -> bool:
        """
        Save field data to session for auto-save functionality.
        
        Args:
            session_key: Session key for storing data
            field_data: Field data to save
            
        Returns:
            bool: Whether data was saved successfully
        """
        try:
            # This would integrate with Django sessions or a caching system
            # For now, we'll just return True as a placeholder
            return True
        except Exception:
            return False
    
    def restore_session_data(self, session_key: str) -> Dict[str, Any]:
        """
        Restore field data from session.
        
        Args:
            session_key: Session key for retrieving data
            
        Returns:
            dict: Restored field data
        """
        try:
            # This would integrate with Django sessions or a caching system
            # For now, we'll just return empty dict as a placeholder
            return {}
        except Exception:
            return {}
