# --- Standard Library Imports ---
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# --- Django Imports ---
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.contenttypes.models import ContentType

# --- Local App Imports ---
from ..models import ProfileChangeHistory, CustomerProfile, ServiceProviderProfile


class ProfileChangeHistoryService:
    """
    Service for managing profile change history and audit trails.
    
    Features:
    - Change history tracking
    - Audit trail management
    - Change notifications
    - Rollback capabilities
    """
    
    def __init__(self, user):
        self.user = user
        self.profile = self._get_profile()
    
    def _get_profile(self):
        """Get the user's profile."""
        if self.user.is_customer:
            profile, created = CustomerProfile.objects.get_or_create(user=self.user)
            return profile
        elif self.user.is_service_provider:
            try:
                return ServiceProviderProfile.objects.get(user=self.user)
            except ServiceProviderProfile.DoesNotExist:
                return None
        return None
    
    def get_change_history(self, page: int = 1, per_page: int = 20, 
                          change_type: str = None, date_from: datetime = None, 
                          date_to: datetime = None) -> Dict[str, Any]:
        """
        Get paginated change history for the user's profile.
        
        Args:
            page: Page number
            per_page: Items per page
            change_type: Filter by change type
            date_from: Filter changes from this date
            date_to: Filter changes to this date
            
        Returns:
            dict: Paginated change history data
        """
        if not self.profile:
            return {
                'changes': [],
                'total_count': 0,
                'page_info': {},
                'filters': {},
            }
        
        # Build query
        content_type = ContentType.objects.get_for_model(self.profile)
        queryset = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id
        ).order_by('-timestamp')
        
        # Apply filters
        filters = {}
        if change_type:
            queryset = queryset.filter(change_type=change_type)
            filters['change_type'] = change_type
        
        if date_from:
            queryset = queryset.filter(timestamp__gte=date_from)
            filters['date_from'] = date_from
        
        if date_to:
            queryset = queryset.filter(timestamp__lte=date_to)
            filters['date_to'] = date_to
        
        # Paginate
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page)
        
        # Format changes
        changes = []
        for change in page_obj:
            changes.append(self._format_change_record(change))
        
        return {
            'changes': changes,
            'total_count': paginator.count,
            'page_info': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'has_previous': page_obj.has_previous(),
                'has_next': page_obj.has_next(),
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
            },
            'filters': filters,
        }
    
    def _format_change_record(self, change: ProfileChangeHistory) -> Dict[str, Any]:
        """
        Format a change record for display.
        
        Args:
            change: ProfileChangeHistory instance
            
        Returns:
            dict: Formatted change data
        """
        return {
            'id': change.id,
            'change_type': change.get_change_type_display(),
            'change_type_code': change.change_type,
            'change_summary': change.change_summary,
            'timestamp': change.timestamp,
            'changed_by': {
                'email': change.changed_by.email if change.changed_by else change.user.email,
                'is_self': change.changed_by == change.user if change.changed_by else True,
                'is_admin': change.changed_by.is_staff if change.changed_by else False,
            },
            'changed_fields': self._format_changed_fields(change.changed_fields),
            'ip_address': change.ip_address,
            'user_agent': self._format_user_agent(change.user_agent),
            'is_rollback': change.is_rollback,
            'can_rollback': change.can_rollback(),
            'rollback_count': change.rollbacks.count(),
        }
    
    def _format_changed_fields(self, changed_fields: Dict) -> List[Dict[str, Any]]:
        """
        Format changed fields for display.
        
        Args:
            changed_fields: Dictionary of changed fields
            
        Returns:
            list: Formatted field changes
        """
        formatted_fields = []
        
        field_display_names = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email Address',
            'phone_number': 'Phone Number',
            'phone': 'Phone Number',
            'address': 'Address',
            'city': 'City',
            'zip_code': 'ZIP Code',
            'profile_picture': 'Profile Picture',
            'logo': 'Business Logo',
            'legal_name': 'Legal Business Name',
            'description': 'Description',
            'website': 'Website',
            'instagram': 'Instagram',
            'facebook': 'Facebook',
            'profile_visibility': 'Profile Visibility',
            'contact_permission': 'Contact Permission',
            'data_sharing_level': 'Data Sharing Level',
        }
        
        for field_name, change_data in changed_fields.items():
            if isinstance(change_data, dict) and 'old' in change_data and 'new' in change_data:
                formatted_fields.append({
                    'field_name': field_name,
                    'display_name': field_display_names.get(field_name, field_name.replace('_', ' ').title()),
                    'old_value': change_data['old'],
                    'new_value': change_data['new'],
                    'is_sensitive': field_name in ['email', 'phone_number', 'phone', 'address'],
                })
        
        return formatted_fields
    
    def _format_user_agent(self, user_agent: str) -> Dict[str, str]:
        """
        Format user agent string for display.
        
        Args:
            user_agent: Raw user agent string
            
        Returns:
            dict: Formatted user agent info
        """
        if not user_agent:
            return {'raw': '', 'browser': 'Unknown', 'os': 'Unknown'}
        
        # Simple user agent parsing (could be enhanced with a library like user-agents)
        browser = 'Unknown'
        os = 'Unknown'
        
        if 'Chrome' in user_agent:
            browser = 'Chrome'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edge' in user_agent:
            browser = 'Edge'
        
        if 'Windows' in user_agent:
            os = 'Windows'
        elif 'Mac' in user_agent:
            os = 'macOS'
        elif 'Linux' in user_agent:
            os = 'Linux'
        elif 'Android' in user_agent:
            os = 'Android'
        elif 'iOS' in user_agent or 'iPhone' in user_agent or 'iPad' in user_agent:
            os = 'iOS'
        
        return {
            'raw': user_agent,
            'browser': browser,
            'os': os,
        }
    
    def get_change_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        Get change statistics for the profile.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            dict: Change statistics
        """
        if not self.profile:
            return {}
        
        content_type = ContentType.objects.get_for_model(self.profile)
        since_date = timezone.now() - timedelta(days=days)
        
        changes = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id,
            timestamp__gte=since_date
        )
        
        # Count by change type
        change_type_counts = {}
        for change_type, display_name in ProfileChangeHistory.CHANGE_TYPES:
            count = changes.filter(change_type=change_type).count()
            if count > 0:
                change_type_counts[change_type] = {
                    'display_name': display_name,
                    'count': count
                }
        
        # Count by day
        daily_counts = {}
        for i in range(days):
            date = (timezone.now() - timedelta(days=i)).date()
            count = changes.filter(timestamp__date=date).count()
            if count > 0:
                daily_counts[date.isoformat()] = count
        
        return {
            'total_changes': changes.count(),
            'change_type_counts': change_type_counts,
            'daily_counts': daily_counts,
            'most_active_day': max(daily_counts.items(), key=lambda x: x[1]) if daily_counts else None,
            'period_days': days,
        }
    
    def rollback_change(self, change_id: int, rolled_back_by=None, request=None) -> Dict[str, Any]:
        """
        Rollback a specific change.
        
        Args:
            change_id: ID of the change to rollback
            rolled_back_by: User performing the rollback
            request: Django request object
            
        Returns:
            dict: Rollback result
        """
        try:
            content_type = ContentType.objects.get_for_model(self.profile)
            change = ProfileChangeHistory.objects.get(
                id=change_id,
                content_type=content_type,
                object_id=self.profile.id
            )
            
            if not change.can_rollback():
                return {
                    'success': False,
                    'message': 'This change cannot be rolled back.',
                }
            
            # Perform rollback
            rollback_change = change.create_rollback(
                rolled_back_by=rolled_back_by or self.user,
                request=request
            )
            
            return {
                'success': True,
                'message': 'Change rolled back successfully.',
                'rollback_change_id': rollback_change.id,
            }
            
        except ProfileChangeHistory.DoesNotExist:
            return {
                'success': False,
                'message': 'Change not found.',
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error rolling back change: {str(e)}',
            }
    
    def get_recent_changes(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get recent changes for quick display.
        
        Args:
            limit: Maximum number of changes to return
            
        Returns:
            list: Recent changes
        """
        if not self.profile:
            return []
        
        content_type = ContentType.objects.get_for_model(self.profile)
        recent_changes = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id
        ).order_by('-timestamp')[:limit]
        
        return [self._format_change_record(change) for change in recent_changes]
    
    def export_change_history(self, format_type: str = 'json') -> str:
        """
        Export complete change history.
        
        Args:
            format_type: Export format ('json' or 'csv')
            
        Returns:
            str: Exported data
        """
        if not self.profile:
            return ''
        
        content_type = ContentType.objects.get_for_model(self.profile)
        all_changes = ProfileChangeHistory.objects.filter(
            content_type=content_type,
            object_id=self.profile.id
        ).order_by('-timestamp')
        
        if format_type == 'json':
            import json
            changes_data = [self._format_change_record(change) for change in all_changes]
            return json.dumps(changes_data, indent=2, default=str)
        
        elif format_type == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'Timestamp', 'Change Type', 'Summary', 'Changed By', 
                'IP Address', 'Is Rollback', 'Changed Fields'
            ])
            
            # Write data
            for change in all_changes:
                formatted_change = self._format_change_record(change)
                writer.writerow([
                    formatted_change['timestamp'],
                    formatted_change['change_type'],
                    formatted_change['change_summary'],
                    formatted_change['changed_by']['email'],
                    formatted_change['ip_address'],
                    formatted_change['is_rollback'],
                    str(formatted_change['changed_fields'])
                ])
            
            return output.getvalue()
        
        return ''
