# --- Standard Library Imports ---
from datetime import datetime

# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError


# --- Privacy Settings Models ---

class ProfilePrivacySettings(models.Model):
    """
    Comprehensive privacy settings for user profiles.
    
    Features:
    - Granular visibility controls
    - Data sharing preferences
    - Contact permissions
    - Marketing preferences
    """
    
    VISIBILITY_CHOICES = [
        ('public', _('Public - Visible to everyone')),
        ('limited', _('Limited - Visible to service providers/customers only')),
        ('private', _('Private - Only visible to me')),
        ('custom', _('Custom - Use detailed settings below')),
    ]
    
    CONTACT_PERMISSION_CHOICES = [
        ('anyone', _('Anyone can contact me')),
        ('verified', _('Only verified users can contact me')),
        ('bookings', _('Only users I have bookings with')),
        ('none', _('No one can contact me directly')),
    ]
    
    DATA_SHARING_CHOICES = [
        ('full', _('Share all data for better recommendations')),
        ('limited', _('Share only basic data')),
        ('minimal', _('Share minimal data required for service')),
        ('none', _('Do not share data for analytics')),
    ]
    
    user = models.OneToOneField(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='privacy_settings',
        verbose_name=_('user')
    )
    
    # General visibility settings
    profile_visibility = models.CharField(
        _('profile visibility'),
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default='public',
        help_text=_('Who can see your profile information')
    )
    
    # Detailed visibility controls (used when profile_visibility is 'custom')
    show_name = models.BooleanField(
        _('show name'),
        default=True,
        help_text=_('Allow others to see your name')
    )
    show_profile_picture = models.BooleanField(
        _('show profile picture'),
        default=True,
        help_text=_('Allow others to see your profile picture')
    )
    show_email = models.BooleanField(
        _('show email'),
        default=False,
        help_text=_('Allow others to see your email address')
    )
    show_phone = models.BooleanField(
        _('show phone'),
        default=False,
        help_text=_('Allow others to see your phone number')
    )
    show_address = models.BooleanField(
        _('show address'),
        default=False,
        help_text=_('Allow others to see your address')
    )
    show_booking_history = models.BooleanField(
        _('show booking history'),
        default=False,
        help_text=_('Allow service providers to see your booking history')
    )
    show_reviews = models.BooleanField(
        _('show reviews'),
        default=True,
        help_text=_('Allow others to see reviews you have written')
    )
    
    # Contact permissions
    contact_permission = models.CharField(
        _('contact permission'),
        max_length=20,
        choices=CONTACT_PERMISSION_CHOICES,
        default='verified',
        help_text=_('Who can contact you directly')
    )
    allow_marketing_contact = models.BooleanField(
        _('allow marketing contact'),
        default=False,
        help_text=_('Allow businesses to contact you for marketing purposes')
    )
    allow_promotional_contact = models.BooleanField(
        _('allow promotional contact'),
        default=True,
        help_text=_('Allow CozyWish to send you promotional offers')
    )
    
    # Data sharing and analytics
    data_sharing_level = models.CharField(
        _('data sharing level'),
        max_length=20,
        choices=DATA_SHARING_CHOICES,
        default='limited',
        help_text=_('How much data to share for analytics and recommendations')
    )
    allow_usage_analytics = models.BooleanField(
        _('allow usage analytics'),
        default=True,
        help_text=_('Allow CozyWish to analyze your usage for service improvements')
    )
    allow_personalized_ads = models.BooleanField(
        _('allow personalized ads'),
        default=False,
        help_text=_('Allow personalized advertisements based on your activity')
    )
    
    # Search and discovery
    searchable_profile = models.BooleanField(
        _('searchable profile'),
        default=True,
        help_text=_('Allow your profile to appear in search results')
    )
    show_in_recommendations = models.BooleanField(
        _('show in recommendations'),
        default=True,
        help_text=_('Allow your profile to be recommended to service providers')
    )
    
    # Location and tracking
    share_location_data = models.BooleanField(
        _('share location data'),
        default=True,
        help_text=_('Share location data for better local recommendations')
    )
    track_activity = models.BooleanField(
        _('track activity'),
        default=True,
        help_text=_('Track activity for personalized experience')
    )
    
    # Third-party integrations
    allow_social_login_data = models.BooleanField(
        _('allow social login data'),
        default=True,
        help_text=_('Allow importing data from social login providers')
    )
    share_with_partners = models.BooleanField(
        _('share with partners'),
        default=False,
        help_text=_('Share data with trusted CozyWish partners')
    )
    
    # Metadata
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True,
        help_text=_('When privacy settings were created')
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True,
        help_text=_('When privacy settings were last updated')
    )
    last_reviewed_at = models.DateTimeField(
        _('last reviewed at'),
        null=True,
        blank=True,
        help_text=_('When user last reviewed their privacy settings')
    )
    
    class Meta:
        verbose_name = _('Profile Privacy Settings')
        verbose_name_plural = _('Profile Privacy Settings')
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['profile_visibility']),
            models.Index(fields=['contact_permission']),
            models.Index(fields=['data_sharing_level']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - Privacy Settings"
    
    def clean(self):
        """Validate privacy settings."""
        super().clean()
        
        # If profile is private, ensure contact permission is restrictive
        if self.profile_visibility == 'private' and self.contact_permission == 'anyone':
            raise ValidationError({
                'contact_permission': _('Contact permission must be restrictive when profile is private.')
            })
    
    def get_effective_visibility(self, field_name):
        """
        Get effective visibility for a specific field based on profile visibility setting.
        
        Args:
            field_name (str): Name of the field to check
            
        Returns:
            bool: Whether the field should be visible
        """
        if self.profile_visibility == 'private':
            return False
        elif self.profile_visibility == 'public':
            return True
        elif self.profile_visibility == 'limited':
            # Limited visibility - show basic info but not sensitive data
            limited_fields = ['show_name', 'show_profile_picture', 'show_reviews']
            return field_name in limited_fields
        elif self.profile_visibility == 'custom':
            # Use individual field settings
            return getattr(self, field_name, False)
        
        return False
    
    def mark_as_reviewed(self):
        """Mark privacy settings as reviewed by user."""
        self.last_reviewed_at = timezone.now()
        self.save(update_fields=['last_reviewed_at'])
    
    def needs_review(self, days=90):
        """
        Check if privacy settings need review.
        
        Args:
            days (int): Number of days after which review is needed
            
        Returns:
            bool: Whether settings need review
        """
        if not self.last_reviewed_at:
            return True
        
        review_threshold = timezone.now() - timezone.timedelta(days=days)
        return self.last_reviewed_at < review_threshold
    
    def get_privacy_summary(self):
        """
        Get a summary of current privacy settings.
        
        Returns:
            dict: Summary of privacy settings
        """
        return {
            'profile_visibility': self.get_profile_visibility_display(),
            'contact_permission': self.get_contact_permission_display(),
            'data_sharing': self.get_data_sharing_level_display(),
            'marketing_allowed': self.allow_marketing_contact,
            'searchable': self.searchable_profile,
            'location_sharing': self.share_location_data,
            'needs_review': self.needs_review(),
        }
    
    @classmethod
    def get_or_create_for_user(cls, user):
        """
        Get or create privacy settings for a user with sensible defaults.
        
        Args:
            user: User instance
            
        Returns:
            tuple: (ProfilePrivacySettings instance, created boolean)
        """
        defaults = {
            'profile_visibility': 'limited' if user.is_service_provider else 'public',
            'contact_permission': 'verified',
            'data_sharing_level': 'limited',
            'allow_marketing_contact': False,
            'allow_promotional_contact': True,
            'searchable_profile': True,
            'show_in_recommendations': True,
            'share_location_data': True,
            'track_activity': True,
        }
        
        return cls.objects.get_or_create(user=user, defaults=defaults)


# --- Privacy Consent Tracking ---

class PrivacyConsent(models.Model):
    """
    Track user consent for various privacy-related activities.

    Features:
    - GDPR compliance
    - Consent versioning
    - Withdrawal tracking
    - Audit trail
    """

    CONSENT_TYPES = [
        ('data_processing', _('Data Processing')),
        ('marketing', _('Marketing Communications')),
        ('analytics', _('Usage Analytics')),
        ('cookies', _('Cookies and Tracking')),
        ('third_party_sharing', _('Third Party Data Sharing')),
        ('location_tracking', _('Location Tracking')),
        ('personalized_ads', _('Personalized Advertisements')),
        ('social_integration', _('Social Media Integration')),
    ]

    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='privacy_consents',
        verbose_name=_('user')
    )

    consent_type = models.CharField(
        _('consent type'),
        max_length=30,
        choices=CONSENT_TYPES,
        help_text=_('Type of consent given or withdrawn')
    )

    is_granted = models.BooleanField(
        _('consent granted'),
        help_text=_('Whether consent is granted (True) or withdrawn (False)')
    )

    consent_version = models.CharField(
        _('consent version'),
        max_length=20,
        default='1.0',
        help_text=_('Version of the consent terms')
    )

    consent_text = models.TextField(
        _('consent text'),
        blank=True,
        help_text=_('The actual consent text shown to the user')
    )

    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True,
        help_text=_('IP address when consent was given/withdrawn')
    )

    user_agent = models.TextField(
        _('user agent'),
        blank=True,
        help_text=_('Browser/device information when consent was given')
    )

    timestamp = models.DateTimeField(
        _('timestamp'),
        auto_now_add=True,
        help_text=_('When consent was given or withdrawn')
    )

    expires_at = models.DateTimeField(
        _('expires at'),
        null=True,
        blank=True,
        help_text=_('When this consent expires (if applicable)')
    )

    class Meta:
        verbose_name = _('Privacy Consent')
        verbose_name_plural = _('Privacy Consents')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'consent_type']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['is_granted']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        status = 'Granted' if self.is_granted else 'Withdrawn'
        return f"{self.user.email} - {self.get_consent_type_display()} - {status}"

    @classmethod
    def record_consent(cls, user, consent_type, is_granted, version='1.0',
                      consent_text='', request=None, expires_at=None):
        """
        Record a consent decision.

        Args:
            user: User instance
            consent_type: Type of consent from CONSENT_TYPES
            is_granted: Whether consent is granted
            version: Version of consent terms
            consent_text: The consent text shown to user
            request: Django request object for IP/user agent
            expires_at: When consent expires

        Returns:
            PrivacyConsent: The created consent record
        """
        consent_data = {
            'user': user,
            'consent_type': consent_type,
            'is_granted': is_granted,
            'consent_version': version,
            'consent_text': consent_text,
            'expires_at': expires_at,
        }

        if request:
            consent_data.update({
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })

        return cls.objects.create(**consent_data)

    @classmethod
    def get_current_consent(cls, user, consent_type):
        """
        Get the current consent status for a user and consent type.

        Args:
            user: User instance
            consent_type: Type of consent to check

        Returns:
            PrivacyConsent or None: Latest consent record
        """
        return cls.objects.filter(
            user=user,
            consent_type=consent_type
        ).order_by('-timestamp').first()

    @classmethod
    def has_valid_consent(cls, user, consent_type):
        """
        Check if user has valid consent for a specific type.

        Args:
            user: User instance
            consent_type: Type of consent to check

        Returns:
            bool: Whether user has valid consent
        """
        consent = cls.get_current_consent(user, consent_type)
        if not consent or not consent.is_granted:
            return False

        # Check if consent has expired
        if consent.expires_at and consent.expires_at < timezone.now():
            return False

        return True
