"""
Data Export Tracking Models.

This module provides models for tracking data export requests, scheduled exports,
and export history for GDPR compliance and user convenience.
"""

import json
from datetime import datetime, timedelta
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class DataExportRequest(models.Model):
    """
    Track individual data export requests.
    
    Features:
    - Export status tracking
    - File metadata storage
    - Download tracking
    - Expiration management
    """
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('expired', _('Expired')),
        ('cancelled', _('Cancelled')),
    ]
    
    FORMAT_CHOICES = [
        ('json', _('JSON')),
        ('csv', _('CSV')),
        ('pdf', _('PDF')),
        ('xml', _('XML')),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='data_export_requests',
        help_text=_('User who requested the export')
    )
    
    export_format = models.CharField(
        max_length=10,
        choices=FORMAT_CHOICES,
        help_text=_('Format of the exported data')
    )
    
    data_types = models.JSONField(
        default=list,
        help_text=_('Types of data included in the export')
    )
    
    export_options = models.JSONField(
        default=dict,
        help_text=_('Additional export options (compression, template, etc.)')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('Current status of the export request')
    )
    
    # Request tracking
    requested_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the export was requested')
    )
    
    processing_started_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When processing started')
    )
    
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the export was completed')
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the export file expires')
    )
    
    # File information
    file_name = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Name of the generated export file')
    )
    
    file_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_('Size of the export file in bytes')
    )
    
    file_path = models.CharField(
        max_length=500,
        blank=True,
        help_text=_('Path to the export file in storage')
    )
    
    # Download tracking
    download_count = models.PositiveIntegerField(
        default=0,
        help_text=_('Number of times the export has been downloaded')
    )
    
    last_downloaded_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the export was last downloaded')
    )
    
    # Error handling
    error_message = models.TextField(
        blank=True,
        help_text=_('Error message if export failed')
    )
    
    # Metadata
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text=_('IP address of the requester')
    )
    
    user_agent = models.TextField(
        blank=True,
        help_text=_('User agent of the requester')
    )
    
    class Meta:
        db_table = 'accounts_data_export_request'
        verbose_name = _('Data Export Request')
        verbose_name_plural = _('Data Export Requests')
        ordering = ['-requested_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'expires_at']),
            models.Index(fields=['requested_at']),
        ]
    
    def __str__(self):
        return f"Export {self.id} - {self.user.email} ({self.export_format})"
    
    def save(self, *args, **kwargs):
        # Set expiration date for completed exports (30 days)
        if self.status == 'completed' and not self.expires_at:
            self.expires_at = timezone.now() + timedelta(days=30)
        
        # Set processing start time
        if self.status == 'processing' and not self.processing_started_at:
            self.processing_started_at = timezone.now()
        
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Check if the export has expired."""
        return self.expires_at and self.expires_at < timezone.now()
    
    @property
    def is_downloadable(self):
        """Check if the export is available for download."""
        return self.status == 'completed' and not self.is_expired
    
    @property
    def processing_duration(self):
        """Get processing duration in seconds."""
        if self.processing_started_at and self.completed_at:
            return (self.completed_at - self.processing_started_at).total_seconds()
        return None
    
    @property
    def file_size_human(self):
        """Get human-readable file size."""
        if not self.file_size:
            return "Unknown"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"
    
    def mark_as_failed(self, error_message: str):
        """Mark export as failed with error message."""
        self.status = 'failed'
        self.error_message = error_message
        self.save()
    
    def mark_as_completed(self, file_name: str, file_size: int, file_path: str = ''):
        """Mark export as completed with file information."""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.file_name = file_name
        self.file_size = file_size
        self.file_path = file_path
        self.save()


class ScheduledDataExport(models.Model):
    """
    Track scheduled/recurring data exports.
    
    Features:
    - Recurring export schedules
    - Schedule management
    - Execution tracking
    - Automatic cleanup
    """
    
    SCHEDULE_TYPE_CHOICES = [
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('yearly', _('Yearly')),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='scheduled_exports',
        help_text=_('User who scheduled the export')
    )
    
    export_format = models.CharField(
        max_length=10,
        choices=DataExportRequest.FORMAT_CHOICES,
        help_text=_('Format for scheduled exports')
    )
    
    data_types = models.JSONField(
        default=list,
        help_text=_('Types of data to include in scheduled exports')
    )
    
    schedule_type = models.CharField(
        max_length=20,
        choices=SCHEDULE_TYPE_CHOICES,
        help_text=_('Frequency of the scheduled export')
    )
    
    schedule_options = models.JSONField(
        default=dict,
        help_text=_('Additional schedule configuration')
    )
    
    # Schedule status
    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether the schedule is active')
    )
    
    # Timing
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the schedule was created')
    )
    
    next_run_at = models.DateTimeField(
        help_text=_('When the next export should run')
    )
    
    last_run_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the schedule last ran')
    )
    
    # Execution tracking
    total_runs = models.PositiveIntegerField(
        default=0,
        help_text=_('Total number of times this schedule has run')
    )
    
    successful_runs = models.PositiveIntegerField(
        default=0,
        help_text=_('Number of successful runs')
    )
    
    failed_runs = models.PositiveIntegerField(
        default=0,
        help_text=_('Number of failed runs')
    )
    
    last_error = models.TextField(
        blank=True,
        help_text=_('Last error message if any')
    )
    
    class Meta:
        db_table = 'accounts_scheduled_data_export'
        verbose_name = _('Scheduled Data Export')
        verbose_name_plural = _('Scheduled Data Exports')
        ordering = ['next_run_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['is_active', 'next_run_at']),
            models.Index(fields=['schedule_type']),
        ]
    
    def __str__(self):
        return f"Scheduled Export {self.id} - {self.user.email} ({self.schedule_type})"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage."""
        if self.total_runs == 0:
            return 0
        return (self.successful_runs / self.total_runs) * 100
    
    def calculate_next_run(self):
        """Calculate the next run time based on schedule type."""
        now = timezone.now()
        
        if self.schedule_type == 'daily':
            return now + timedelta(days=1)
        elif self.schedule_type == 'weekly':
            return now + timedelta(weeks=1)
        elif self.schedule_type == 'monthly':
            # Add approximately one month
            return now + timedelta(days=30)
        elif self.schedule_type == 'quarterly':
            return now + timedelta(days=90)
        elif self.schedule_type == 'yearly':
            return now + timedelta(days=365)
        else:
            return now + timedelta(days=1)  # Default to daily
    
    def mark_run_completed(self, success: bool, error_message: str = ''):
        """Mark a scheduled run as completed."""
        self.last_run_at = timezone.now()
        self.total_runs += 1
        
        if success:
            self.successful_runs += 1
            self.last_error = ''
        else:
            self.failed_runs += 1
            self.last_error = error_message
        
        # Calculate next run time
        self.next_run_at = self.calculate_next_run()
        self.save()
    
    def deactivate(self):
        """Deactivate the scheduled export."""
        self.is_active = False
        self.save()


class DataExportTemplate(models.Model):
    """
    Store custom export templates for users.
    
    Features:
    - Custom export configurations
    - Reusable templates
    - Template sharing
    - Template versioning
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='export_templates',
        help_text=_('User who created the template')
    )
    
    name = models.CharField(
        max_length=100,
        help_text=_('Name of the export template')
    )
    
    description = models.TextField(
        blank=True,
        help_text=_('Description of the template')
    )
    
    export_format = models.CharField(
        max_length=10,
        choices=DataExportRequest.FORMAT_CHOICES,
        help_text=_('Default export format for this template')
    )
    
    data_types = models.JSONField(
        default=list,
        help_text=_('Data types included in this template')
    )
    
    export_options = models.JSONField(
        default=dict,
        help_text=_('Export options for this template')
    )
    
    is_public = models.BooleanField(
        default=False,
        help_text=_('Whether this template can be used by other users')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the template was created')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When the template was last updated')
    )
    
    usage_count = models.PositiveIntegerField(
        default=0,
        help_text=_('Number of times this template has been used')
    )
    
    class Meta:
        db_table = 'accounts_data_export_template'
        verbose_name = _('Data Export Template')
        verbose_name_plural = _('Data Export Templates')
        ordering = ['-created_at']
        unique_together = ['user', 'name']
        indexes = [
            models.Index(fields=['user', 'is_public']),
            models.Index(fields=['is_public']),
        ]
    
    def __str__(self):
        return f"Template: {self.name} ({self.user.email})"
    
    def increment_usage(self):
        """Increment the usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])
