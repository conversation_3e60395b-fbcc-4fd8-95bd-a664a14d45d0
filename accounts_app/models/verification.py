"""
Profile Verification Models.

This module provides models for profile verification requests, document management,
and verification status tracking.
"""

import json
from datetime import datetime, timedelta
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class ProfileVerificationRequest(models.Model):
    """
    Track profile verification requests.
    
    Features:
    - Verification workflow management
    - Status tracking
    - Document association
    - Admin review tools
    """
    
    VERIFICATION_TYPE_CHOICES = [
        ('identity', _('Identity Verification')),
        ('address', _('Address Verification')),
        ('business', _('Business Verification')),
        ('professional', _('Professional Verification')),
        ('enhanced', _('Enhanced Verification')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('submitted', _('Submitted')),
        ('in_review', _('In Review')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('expired', _('Expired')),
        ('cancelled', _('Cancelled')),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='verification_requests',
        help_text=_('User requesting verification')
    )
    
    verification_type = models.CharField(
        max_length=20,
        choices=VERIFICATION_TYPE_CHOICES,
        help_text=_('Type of verification requested')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('Current status of the verification request')
    )
    
    # Timestamps
    submitted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the verification was submitted')
    )
    
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the verification was reviewed')
    )
    
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the verification was approved')
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the verification expires')
    )
    
    # Review information
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_verifications',
        help_text=_('Admin who reviewed the verification')
    )
    
    rejection_reason = models.TextField(
        blank=True,
        help_text=_('Reason for rejection if applicable')
    )
    
    admin_notes = models.TextField(
        blank=True,
        help_text=_('Internal admin notes')
    )
    
    # Metadata
    metadata = models.JSONField(
        default=dict,
        help_text=_('Additional metadata (IP, user agent, etc.)')
    )
    
    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the request was created')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When the request was last updated')
    )
    
    class Meta:
        db_table = 'accounts_profile_verification_request'
        verbose_name = _('Profile Verification Request')
        verbose_name_plural = _('Profile Verification Requests')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'verification_type']),
            models.Index(fields=['status', 'submitted_at']),
            models.Index(fields=['verification_type', 'status']),
        ]
    
    def __str__(self):
        return f"Verification {self.id} - {self.user.email} ({self.verification_type})"
    
    def save(self, *args, **kwargs):
        # Set expiration date for approved verifications
        if self.status == 'approved' and not self.expires_at:
            # Different verification types have different validity periods
            validity_periods = {
                'identity': 365,  # 1 year
                'address': 180,   # 6 months
                'business': 365,  # 1 year
                'professional': 730,  # 2 years
                'enhanced': 365,  # 1 year
            }
            days = validity_periods.get(self.verification_type, 365)
            self.expires_at = timezone.now() + timedelta(days=days)
        
        # Set approved timestamp
        if self.status == 'approved' and not self.approved_at:
            self.approved_at = timezone.now()
        
        # Set reviewed timestamp
        if self.status in ['approved', 'rejected'] and not self.reviewed_at:
            self.reviewed_at = timezone.now()
        
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Check if the verification has expired."""
        return self.expires_at and self.expires_at < timezone.now()
    
    @property
    def is_active(self):
        """Check if the verification is currently active."""
        return self.status == 'approved' and not self.is_expired
    
    @property
    def processing_duration(self):
        """Get processing duration in hours."""
        if self.submitted_at and self.reviewed_at:
            return (self.reviewed_at - self.submitted_at).total_seconds() / 3600
        return None
    
    @property
    def days_until_expiry(self):
        """Get days until expiry."""
        if self.expires_at:
            delta = self.expires_at - timezone.now()
            return max(0, delta.days)
        return None
    
    def approve(self, reviewed_by: User, admin_notes: str = ''):
        """Approve the verification request."""
        self.status = 'approved'
        self.reviewed_by = reviewed_by
        self.admin_notes = admin_notes
        self.save()
    
    def reject(self, reviewed_by: User, rejection_reason: str, admin_notes: str = ''):
        """Reject the verification request."""
        self.status = 'rejected'
        self.reviewed_by = reviewed_by
        self.rejection_reason = rejection_reason
        self.admin_notes = admin_notes
        self.save()
    
    def cancel(self):
        """Cancel the verification request."""
        self.status = 'cancelled'
        self.save()


class VerificationDocument(models.Model):
    """
    Store verification documents uploaded by users.
    
    Features:
    - Secure document storage
    - Document metadata tracking
    - File validation
    - Admin review tools
    """
    
    DOCUMENT_TYPE_CHOICES = [
        ('government_id', _('Government ID')),
        ('address_proof', _('Address Proof')),
        ('business_registration', _('Business Registration')),
        ('tax_document', _('Tax Document')),
        ('professional_license', _('Professional License')),
        ('certification', _('Certification')),
        ('background_check', _('Background Check')),
    ]
    
    verification_request = models.ForeignKey(
        ProfileVerificationRequest,
        on_delete=models.CASCADE,
        related_name='documents',
        help_text=_('Associated verification request')
    )
    
    document_type = models.CharField(
        max_length=30,
        choices=DOCUMENT_TYPE_CHOICES,
        help_text=_('Type of document')
    )
    
    file_name = models.CharField(
        max_length=255,
        help_text=_('Original filename')
    )
    
    file_size = models.PositiveIntegerField(
        help_text=_('File size in bytes')
    )
    
    file_path = models.CharField(
        max_length=500,
        help_text=_('Path to the stored file')
    )
    
    file_hash = models.CharField(
        max_length=64,
        blank=True,
        help_text=_('SHA-256 hash of the file')
    )
    
    # Document metadata
    metadata = models.JSONField(
        default=dict,
        help_text=_('Document metadata (document number, dates, etc.)')
    )
    
    # Review status
    is_reviewed = models.BooleanField(
        default=False,
        help_text=_('Whether the document has been reviewed')
    )
    
    is_valid = models.BooleanField(
        default=False,
        help_text=_('Whether the document is valid')
    )
    
    review_notes = models.TextField(
        blank=True,
        help_text=_('Admin review notes for this document')
    )
    
    # Timestamps
    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the document was uploaded')
    )
    
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the document was reviewed')
    )
    
    class Meta:
        db_table = 'accounts_verification_document'
        verbose_name = _('Verification Document')
        verbose_name_plural = _('Verification Documents')
        ordering = ['-uploaded_at']
        indexes = [
            models.Index(fields=['verification_request', 'document_type']),
            models.Index(fields=['is_reviewed', 'uploaded_at']),
        ]
    
    def __str__(self):
        return f"Document {self.id} - {self.document_type} ({self.file_name})"
    
    @property
    def file_size_human(self):
        """Get human-readable file size."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"
    
    def mark_as_reviewed(self, is_valid: bool, review_notes: str = ''):
        """Mark document as reviewed."""
        self.is_reviewed = True
        self.is_valid = is_valid
        self.review_notes = review_notes
        self.reviewed_at = timezone.now()
        self.save()


class VerificationBadge(models.Model):
    """
    Track verification badges earned by users.
    
    Features:
    - Badge management
    - Badge display
    - Badge expiration
    - Badge hierarchy
    """
    
    BADGE_TYPE_CHOICES = [
        ('identity_verified', _('Identity Verified')),
        ('address_verified', _('Address Verified')),
        ('business_verified', _('Business Verified')),
        ('professional_verified', _('Professional Verified')),
        ('enhanced_verified', _('Enhanced Verified')),
        ('trusted_member', _('Trusted Member')),
        ('premium_verified', _('Premium Verified')),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='verification_badges',
        help_text=_('User who earned the badge')
    )
    
    badge_type = models.CharField(
        max_length=30,
        choices=BADGE_TYPE_CHOICES,
        help_text=_('Type of verification badge')
    )
    
    verification_request = models.ForeignKey(
        ProfileVerificationRequest,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text=_('Verification request that earned this badge')
    )
    
    # Badge status
    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether the badge is currently active')
    )
    
    # Timestamps
    earned_at = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the badge was earned')
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the badge expires')
    )
    
    revoked_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the badge was revoked')
    )
    
    # Badge metadata
    badge_data = models.JSONField(
        default=dict,
        help_text=_('Additional badge data (level, score, etc.)')
    )
    
    class Meta:
        db_table = 'accounts_verification_badge'
        verbose_name = _('Verification Badge')
        verbose_name_plural = _('Verification Badges')
        ordering = ['-earned_at']
        unique_together = ['user', 'badge_type']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['badge_type', 'is_active']),
        ]
    
    def __str__(self):
        return f"Badge {self.badge_type} - {self.user.email}"
    
    @property
    def is_expired(self):
        """Check if the badge has expired."""
        return self.expires_at and self.expires_at < timezone.now()
    
    @property
    def is_valid(self):
        """Check if the badge is currently valid."""
        return self.is_active and not self.is_expired and not self.revoked_at
    
    def revoke(self, reason: str = ''):
        """Revoke the badge."""
        self.is_active = False
        self.revoked_at = timezone.now()
        if reason:
            self.badge_data['revocation_reason'] = reason
        self.save()
    
    def renew(self, expires_at: datetime = None):
        """Renew the badge."""
        self.is_active = True
        self.revoked_at = None
        if expires_at:
            self.expires_at = expires_at
        self.save()
