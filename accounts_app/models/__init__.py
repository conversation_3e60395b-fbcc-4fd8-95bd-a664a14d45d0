# --- Import all models for backward compatibility ---

# User models
from .user import CustomUser, CustomUserManager

# Session management models
from .session_management import UserSession, SessionSecurityEvent

# Security models
from .security import LoginHistory, LoginAlert

# Profile models
from .profiles import CustomerProfile, ServiceProviderProfile

# Profile tracking models
from .profile_tracking import ProfileChangeHistory, ProfileCompletionMilestone

# Privacy models
from .privacy import ProfilePrivacySettings, PrivacyConsent

# Team models
from .team import TeamMember

# Make all models available at the package level for backward compatibility
__all__ = [
    'CustomUser',
    'CustomUserManager',
    'LoginHistory',
    'LoginAlert',
    'CustomerProfile',
    'ServiceProviderProfile',
    'ProfileChangeHistory',
    'ProfileCompletionMilestone',
    'ProfilePrivacySettings',
    'PrivacyConsent',
    'TeamMember',
]
