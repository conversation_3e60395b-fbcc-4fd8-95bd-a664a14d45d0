# --- Standard Library Imports ---
import json
from datetime import datetime, timedelta

# --- Django Imports ---
from django.core.validators import RegexValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

# --- Local App Imports ---
from accounts_app.utils import get_customer_profile_image_path, get_provider_profile_image_path


# --- Customer Profile ---

class CustomerProfile(models.Model):
    """Stores comprehensive customer profile information"""
    # Gender Constants
    MALE = 'M'
    FEMALE = 'F'
    OTHER = 'O'
    
    GENDER_CHOICES = (
        (MALE, _('Male')),
        (FEMALE, _('Female')),
        (OTHER, _('Other')),
    )
    
    # Month Constants
    JANUARY = 1
    FEBRUARY = 2
    MARCH = 3
    APRIL = 4
    MAY = 5
    JUNE = 6
    JULY = 7
    AUGUST = 8
    SEPTEMBER = 9
    OCTOBER = 10
    NOVEMBER = 11
    DECEMBER = 12
    
    MONTH_CHOICES = (
        (JANUARY, _('January')),
        (FEBRUARY, _('February')),
        (MARCH, _('March')),
        (APRIL, _('April')),
        (MAY, _('May')),
        (JUNE, _('June')),
        (JULY, _('July')),
        (AUGUST, _('August')),
        (SEPTEMBER, _('September')),
        (OCTOBER, _('October')),
        (NOVEMBER, _('November')),
        (DECEMBER, _('December')),
    )

    user = models.OneToOneField(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='customer_profile',
        verbose_name=_('user account')
    )
    first_name = models.CharField(
        _('first name'), 
        max_length=100,
        blank=True
    )
    last_name = models.CharField(
        _('last name'), 
        max_length=100,
        blank=True
    )
    profile_picture = models.ImageField(
        _('profile picture'),
        upload_to=get_customer_profile_image_path,
        blank=True,
        null=True,
        help_text=_('Profile image uploaded to cloud storage')
    )
    gender = models.CharField(
        _('gender'),
        max_length=1,
        choices=GENDER_CHOICES,
        blank=True
    )
    birth_month = models.PositiveSmallIntegerField(
        _('birth month'),
        choices=MONTH_CHOICES,
        null=True,
        blank=True
    )
    birth_year = models.PositiveSmallIntegerField(
        _('birth year'),
        null=True,
        blank=True,
        help_text=_('Format: YYYY')
    )
    phone_number = models.CharField(
        _('phone number'),
        max_length=20,
        blank=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Enter a valid phone number (e.g., +**********)')
        )]
    )
    address = models.CharField(
        _('street address'),
        max_length=255,
        blank=True
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=True
    )
    zip_code = models.CharField(
        _('postal code'),
        max_length=10,
        blank=True
    )

    # Profile completion and verification tracking
    profile_completion_percentage = models.PositiveSmallIntegerField(
        _('profile completion percentage'),
        default=0,
        help_text=_('Calculated percentage of profile completion (0-100)')
    )
    last_completion_check = models.DateTimeField(
        _('last completion check'),
        null=True,
        blank=True,
        help_text=_('When profile completion was last calculated')
    )
    is_verified = models.BooleanField(
        _('profile verified'),
        default=False,
        help_text=_('Whether the profile has been verified by admin')
    )
    verification_date = models.DateTimeField(
        _('verification date'),
        null=True,
        blank=True,
        help_text=_('When the profile was verified')
    )
    verification_notes = models.TextField(
        _('verification notes'),
        blank=True,
        max_length=500,
        help_text=_('Admin notes about profile verification')
    )

    # Privacy and visibility settings
    PRIVACY_CHOICES = [
        ('public', _('Public - Visible to everyone')),
        ('private', _('Private - Only visible to me')),
        ('limited', _('Limited - Visible to service providers only')),
    ]

    profile_visibility = models.CharField(
        _('profile visibility'),
        max_length=20,
        choices=PRIVACY_CHOICES,
        default='public',
        help_text=_('Who can see your profile information')
    )
    show_email_to_providers = models.BooleanField(
        _('show email to service providers'),
        default=True,
        help_text=_('Allow service providers to see your email address')
    )
    show_phone_to_providers = models.BooleanField(
        _('show phone to service providers'),
        default=True,
        help_text=_('Allow service providers to see your phone number')
    )
    allow_marketing_contact = models.BooleanField(
        _('allow marketing contact'),
        default=False,
        help_text=_('Allow service providers to contact you for marketing')
    )

    # Data export and GDPR compliance
    data_export_requested = models.BooleanField(
        _('data export requested'),
        default=False,
        help_text=_('Whether user has requested data export')
    )
    data_export_request_date = models.DateTimeField(
        _('data export request date'),
        null=True,
        blank=True,
        help_text=_('When data export was requested')
    )
    data_export_completed_date = models.DateTimeField(
        _('data export completed date'),
        null=True,
        blank=True,
        help_text=_('When data export was completed')
    )
    account_deletion_requested = models.BooleanField(
        _('account deletion requested'),
        default=False,
        help_text=_('Whether user has requested account deletion')
    )
    account_deletion_request_date = models.DateTimeField(
        _('account deletion request date'),
        null=True,
        blank=True,
        help_text=_('When account deletion was requested')
    )

    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Customer Profile')
        verbose_name_plural = _('Customer Profiles')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['last_name', 'first_name']),
            models.Index(fields=['city']),
            models.Index(fields=['zip_code']),
            models.Index(fields=['profile_completion_percentage']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['profile_visibility']),
            models.Index(fields=['data_export_requested']),
            models.Index(fields=['account_deletion_requested']),
        ]

    def __str__(self):
        return _("%(email)s's profile") % {'email': self.user.email}

    @property
    def full_name(self) -> str:
        """Customer's full name combining first and last names"""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def birth_month_name(self) -> str:
        """Get localized month name for birth month"""
        return dict(self.MONTH_CHOICES).get(self.birth_month, '')

    def get_absolute_url(self):
        return reverse('accounts_app:customer_profile')

    def calculate_completion_percentage(self) -> int:
        """
        Calculate profile completion percentage based on filled fields with weights.

        Returns:
            int: Completion percentage (0-100)
        """
        # Define field weights (total should be 100)
        field_weights = {
            'first_name': 15,
            'last_name': 15,
            'phone_number': 20,
            'profile_picture': 15,
            'address': 10,
            'city': 8,
            'zip_code': 7,
            'birth_month': 5,
            'birth_year': 5,
        }

        completed_weight = 0
        total_weight = sum(field_weights.values())

        for field_name, weight in field_weights.items():
            value = getattr(self, field_name, None)
            if value:  # Field has a value
                completed_weight += weight

        # Special case for birth date (both month and year needed)
        if self.birth_month and self.birth_year:
            # Already counted in individual fields, no additional bonus
            pass
        elif self.birth_month or self.birth_year:
            # Partial birth date, reduce weight by half
            completed_weight -= 2.5  # Half of birth_month + birth_year weights

        percentage = int((completed_weight / total_weight) * 100)
        return min(100, max(0, percentage))

    def update_completion_percentage(self, save=True) -> int:
        """
        Update and optionally save the profile completion percentage.

        Args:
            save (bool): Whether to save the model after updating

        Returns:
            int: Updated completion percentage
        """
        self.profile_completion_percentage = self.calculate_completion_percentage()
        self.last_completion_check = timezone.now()

        if save:
            self.save(update_fields=['profile_completion_percentage', 'last_completion_check'])

        return self.profile_completion_percentage

    def get_incomplete_fields(self) -> list:
        """
        Get list of incomplete profile fields with their display names.

        Returns:
            list: List of tuples (field_name, display_name) for incomplete fields
        """
        incomplete_fields = []
        field_mapping = {
            'first_name': _('First Name'),
            'last_name': _('Last Name'),
            'phone_number': _('Phone Number'),
            'profile_picture': _('Profile Picture'),
            'address': _('Street Address'),
            'city': _('City'),
            'zip_code': _('ZIP Code'),
            'birth_month': _('Birth Month'),
            'birth_year': _('Birth Year'),
        }

        for field_name, display_name in field_mapping.items():
            value = getattr(self, field_name, None)
            if not value:
                incomplete_fields.append((field_name, display_name))

        return incomplete_fields

    def get_completion_suggestions(self) -> list:
        """
        Get smart suggestions for completing profile.

        Returns:
            list: List of suggestion dictionaries with priority and message
        """
        suggestions = []
        incomplete_fields = self.get_incomplete_fields()

        # High priority suggestions
        if not self.first_name or not self.last_name:
            suggestions.append({
                'priority': 'high',
                'message': _('Add your name to help service providers identify you'),
                'fields': ['first_name', 'last_name'],
                'action': 'complete_name'
            })

        if not self.phone_number:
            suggestions.append({
                'priority': 'high',
                'message': _('Add your phone number for booking confirmations'),
                'fields': ['phone_number'],
                'action': 'add_phone'
            })

        if not self.profile_picture:
            suggestions.append({
                'priority': 'medium',
                'message': _('Upload a profile picture to personalize your account'),
                'fields': ['profile_picture'],
                'action': 'upload_picture'
            })

        # Location suggestions
        if not self.address or not self.city or not self.zip_code:
            suggestions.append({
                'priority': 'medium',
                'message': _('Complete your address for better service recommendations'),
                'fields': ['address', 'city', 'zip_code'],
                'action': 'complete_address'
            })

        return suggestions

    def mark_verified(self, notes='', admin_user=None):
        """
        Mark profile as verified.

        Args:
            notes (str): Verification notes
            admin_user: Admin user performing verification
        """
        self.is_verified = True
        self.verification_date = timezone.now()
        self.verification_notes = notes
        self.save(update_fields=['is_verified', 'verification_date', 'verification_notes'])

    def request_data_export(self):
        """Request data export for GDPR compliance."""
        self.data_export_requested = True
        self.data_export_request_date = timezone.now()
        self.save(update_fields=['data_export_requested', 'data_export_request_date'])

    def complete_data_export(self):
        """Mark data export as completed."""
        self.data_export_completed_date = timezone.now()
        self.save(update_fields=['data_export_completed_date'])

    def request_account_deletion(self):
        """Request account deletion for GDPR compliance."""
        self.account_deletion_requested = True
        self.account_deletion_request_date = timezone.now()
        self.save(update_fields=['account_deletion_requested', 'account_deletion_request_date'])

    def export_data(self, format='json') -> dict:
        """
        Export profile data in specified format for GDPR compliance.

        Args:
            format (str): Export format ('json', 'dict')

        Returns:
            dict: Profile data dictionary
        """
        data = {
            'profile_info': {
                'first_name': self.first_name,
                'last_name': self.last_name,
                'email': self.user.email,
                'phone_number': self.phone_number,
                'gender': self.get_gender_display() if self.gender else None,
                'birth_month': self.birth_month_name if self.birth_month else None,
                'birth_year': self.birth_year,
                'address': self.address,
                'city': self.city,
                'zip_code': self.zip_code,
            },
            'profile_settings': {
                'profile_visibility': self.get_profile_visibility_display(),
                'show_email_to_providers': self.show_email_to_providers,
                'show_phone_to_providers': self.show_phone_to_providers,
                'allow_marketing_contact': self.allow_marketing_contact,
            },
            'profile_status': {
                'completion_percentage': self.profile_completion_percentage,
                'is_verified': self.is_verified,
                'verification_date': self.verification_date.isoformat() if self.verification_date else None,
                'created_at': self.created_at.isoformat(),
                'updated_at': self.updated_at.isoformat(),
            },
            'export_info': {
                'export_date': timezone.now().isoformat(),
                'export_format': format,
                'data_version': '1.0',
            }
        }

        return data


# --- Service Provider Profile ---

class ServiceProviderProfile(models.Model):
    """Represents a service provider business with complete contact and operational details"""

    # US State Abbreviations
    ALABAMA = 'AL'
    ALASKA = 'AK'
    ARIZONA = 'AZ'
    ARKANSAS = 'AR'
    CALIFORNIA = 'CA'
    COLORADO = 'CO'
    CONNECTICUT = 'CT'
    DELAWARE = 'DE'
    FLORIDA = 'FL'
    GEORGIA = 'GA'
    HAWAII = 'HI'
    IDAHO = 'ID'
    ILLINOIS = 'IL'
    INDIANA = 'IN'
    IOWA = 'IA'
    KANSAS = 'KS'
    KENTUCKY = 'KY'
    LOUISIANA = 'LA'
    MAINE = 'ME'
    MARYLAND = 'MD'
    MASSACHUSETTS = 'MA'
    MICHIGAN = 'MI'
    MINNESOTA = 'MN'
    MISSISSIPPI = 'MS'
    MISSOURI = 'MO'
    MONTANA = 'MT'
    NEBRASKA = 'NE'
    NEVADA = 'NV'
    NEW_HAMPSHIRE = 'NH'
    NEW_JERSEY = 'NJ'
    NEW_MEXICO = 'NM'
    NEW_YORK = 'NY'
    NORTH_CAROLINA = 'NC'
    NORTH_DAKOTA = 'ND'
    OHIO = 'OH'
    OKLAHOMA = 'OK'
    OREGON = 'OR'
    PENNSYLVANIA = 'PA'
    RHODE_ISLAND = 'RI'
    SOUTH_CAROLINA = 'SC'
    SOUTH_DAKOTA = 'SD'
    TENNESSEE = 'TN'
    TEXAS = 'TX'
    UTAH = 'UT'
    VERMONT = 'VT'
    VIRGINIA = 'VA'
    WASHINGTON = 'WA'
    WEST_VIRGINIA = 'WV'
    WISCONSIN = 'WI'
    WYOMING = 'WY'

    STATE_CHOICES = (
        (ALABAMA, _('Alabama')),
        (ALASKA, _('Alaska')),
        (ARIZONA, _('Arizona')),
        (ARKANSAS, _('Arkansas')),
        (CALIFORNIA, _('California')),
        (COLORADO, _('Colorado')),
        (CONNECTICUT, _('Connecticut')),
        (DELAWARE, _('Delaware')),
        (FLORIDA, _('Florida')),
        (GEORGIA, _('Georgia')),
        (HAWAII, _('Hawaii')),
        (IDAHO, _('Idaho')),
        (ILLINOIS, _('Illinois')),
        (INDIANA, _('Indiana')),
        (IOWA, _('Iowa')),
        (KANSAS, _('Kansas')),
        (KENTUCKY, _('Kentucky')),
        (LOUISIANA, _('Louisiana')),
        (MAINE, _('Maine')),
        (MARYLAND, _('Maryland')),
        (MASSACHUSETTS, _('Massachusetts')),
        (MICHIGAN, _('Michigan')),
        (MINNESOTA, _('Minnesota')),
        (MISSISSIPPI, _('Mississippi')),
        (MISSOURI, _('Missouri')),
        (MONTANA, _('Montana')),
        (NEBRASKA, _('Nebraska')),
        (NEVADA, _('Nevada')),
        (NEW_HAMPSHIRE, _('New Hampshire')),
        (NEW_JERSEY, _('New Jersey')),
        (NEW_MEXICO, _('New Mexico')),
        (NEW_YORK, _('New York')),
        (NORTH_CAROLINA, _('North Carolina')),
        (NORTH_DAKOTA, _('North Dakota')),
        (OHIO, _('Ohio')),
        (OKLAHOMA, _('Oklahoma')),
        (OREGON, _('Oregon')),
        (PENNSYLVANIA, _('Pennsylvania')),
        (RHODE_ISLAND, _('Rhode Island')),
        (SOUTH_CAROLINA, _('South Carolina')),
        (SOUTH_DAKOTA, _('South Dakota')),
        (TENNESSEE, _('Tennessee')),
        (TEXAS, _('Texas')),
        (UTAH, _('Utah')),
        (VERMONT, _('Vermont')),
        (VIRGINIA, _('Virginia')),
        (WASHINGTON, _('Washington')),
        (WEST_VIRGINIA, _('West Virginia')),
        (WISCONSIN, _('Wisconsin')),
        (WYOMING, _('Wyoming')),
    )

    user = models.OneToOneField(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='service_provider_profile',
        verbose_name=_('business account')
    )
    legal_name = models.CharField(
        _('legal business name'),
        max_length=200,
        help_text=_('Official registered business name')
    )
    display_name = models.CharField(
        _('public display name'),
        max_length=200,
        blank=True,
        help_text=_('Name shown to customers (if different from legal name)')
    )
    description = models.TextField(
        _('business description'),
        max_length=500,
        blank=True,
        help_text=_('Brief overview of services offered (500 characters max)')
    )
    logo = models.ImageField(
        _('business logo'),
        upload_to=get_provider_profile_image_path,
        blank=True,
        null=True,
        help_text=_('Company logo displayed on your profile')
    )
    phone = models.CharField(
        _('business phone'),
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Enter a valid phone number (e.g., +**********)')
        )]
    )
    contact_name = models.CharField(
        _('primary contact'),
        max_length=100,
        help_text=_('Name of main business contact')
    )
    address = models.CharField(
        _('street address'),
        max_length=255
    )
    city = models.CharField(
        _('city'),
        max_length=100
    )
    state = models.CharField(
        _('state'),
        max_length=2,
        choices=STATE_CHOICES
    )
    county = models.CharField(
        _('county'),
        max_length=100,
        blank=True
    )
    zip_code = models.CharField(
        _('ZIP code'),
        max_length=10
    )
    ein = models.CharField(
        _('EIN number'),
        max_length=20,
        blank=True,
        help_text=_('Employer Identification Number (optional)')
    )
    website = models.URLField(
        _('website URL'),
        blank=True
    )
    instagram = models.URLField(
        _('Instagram URL'),
        blank=True
    )
    facebook = models.URLField(
        _('Facebook URL'),
        blank=True
    )
    is_public = models.BooleanField(
        _('public visibility'),
        default=True,
        help_text=_('Show business in public listings')
    )

    # Tutorial and onboarding tracking
    venue_creation_tutorial_completed = models.BooleanField(
        _('venue creation tutorial completed'),
        default=False,
        help_text=_('Whether the user has completed the venue creation guided tour')
    )

    # Profile completion and verification tracking
    profile_completion_percentage = models.PositiveSmallIntegerField(
        _('profile completion percentage'),
        default=0,
        help_text=_('Calculated percentage of profile completion (0-100)')
    )
    last_completion_check = models.DateTimeField(
        _('last completion check'),
        null=True,
        blank=True,
        help_text=_('When profile completion was last calculated')
    )
    is_verified = models.BooleanField(
        _('business verified'),
        default=False,
        help_text=_('Whether the business has been verified by admin')
    )
    verification_date = models.DateTimeField(
        _('verification date'),
        null=True,
        blank=True,
        help_text=_('When the business was verified')
    )
    verification_notes = models.TextField(
        _('verification notes'),
        blank=True,
        max_length=500,
        help_text=_('Admin notes about business verification')
    )

    # Enhanced privacy and visibility settings
    BUSINESS_PRIVACY_CHOICES = [
        ('public', _('Public - Visible in all listings')),
        ('limited', _('Limited - Visible to existing customers only')),
        ('private', _('Private - Not visible in public listings')),
    ]

    business_visibility = models.CharField(
        _('business visibility'),
        max_length=20,
        choices=BUSINESS_PRIVACY_CHOICES,
        default='public',
        help_text=_('Who can see your business in listings')
    )
    show_contact_info = models.BooleanField(
        _('show contact information'),
        default=True,
        help_text=_('Show phone and email to potential customers')
    )
    allow_direct_booking = models.BooleanField(
        _('allow direct booking'),
        default=True,
        help_text=_('Allow customers to book services directly')
    )
    accept_marketing_partnerships = models.BooleanField(
        _('accept marketing partnerships'),
        default=False,
        help_text=_('Allow CozyWish to include you in marketing partnerships')
    )

    # Data export and GDPR compliance
    data_export_requested = models.BooleanField(
        _('data export requested'),
        default=False,
        help_text=_('Whether business has requested data export')
    )
    data_export_request_date = models.DateTimeField(
        _('data export request date'),
        null=True,
        blank=True,
        help_text=_('When data export was requested')
    )
    data_export_completed_date = models.DateTimeField(
        _('data export completed date'),
        null=True,
        blank=True,
        help_text=_('When data export was completed')
    )
    account_deletion_requested = models.BooleanField(
        _('account deletion requested'),
        default=False,
        help_text=_('Whether business has requested account deletion')
    )
    account_deletion_request_date = models.DateTimeField(
        _('account deletion request date'),
        null=True,
        blank=True,
        help_text=_('When account deletion was requested')
    )

    created = models.DateTimeField(_('created at'), auto_now_add=True)
    updated = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service Provider')
        verbose_name_plural = _('Service Providers')
        ordering = ['-created']
        indexes = [
            models.Index(fields=['legal_name']),
            models.Index(fields=['city', 'state']),
            models.Index(fields=['is_public']),
            models.Index(fields=['profile_completion_percentage']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['business_visibility']),
            models.Index(fields=['data_export_requested']),
            models.Index(fields=['account_deletion_requested']),
        ]

    def __str__(self):
        return f"{self.legal_name} ({self.user.email})"

    @property
    def business_name(self) -> str:
        """Name to display in UI (DBA name if available)"""
        return self.display_name or self.legal_name

    @property
    def full_address(self) -> str:
        """Formatted complete business address"""
        components = [
            self.address,
            self.city,
            f"{self.state} {self.zip_code}"
        ]
        if self.county:
            components.insert(2, self.county)
        return ', '.join(filter(None, components))

    def get_absolute_url(self):
        return reverse('accounts_app:service_provider_profile')

    def calculate_completion_percentage(self) -> int:
        """
        Calculate business profile completion percentage based on filled fields with weights.

        Returns:
            int: Completion percentage (0-100)
        """
        # Define field weights for business profile (total should be 100)
        field_weights = {
            'legal_name': 20,
            'phone': 15,
            'contact_name': 10,
            'address': 10,
            'city': 8,
            'state': 7,
            'zip_code': 5,
            'description': 10,
            'logo': 10,
            'website': 5,
        }

        completed_weight = 0
        total_weight = sum(field_weights.values())

        for field_name, weight in field_weights.items():
            value = getattr(self, field_name, None)
            if value:  # Field has a value
                completed_weight += weight

        percentage = int((completed_weight / total_weight) * 100)
        return min(100, max(0, percentage))

    def update_completion_percentage(self, save=True) -> int:
        """
        Update and optionally save the business profile completion percentage.

        Args:
            save (bool): Whether to save the model after updating

        Returns:
            int: Updated completion percentage
        """
        self.profile_completion_percentage = self.calculate_completion_percentage()
        self.last_completion_check = timezone.now()

        if save:
            self.save(update_fields=['profile_completion_percentage', 'last_completion_check'])

        return self.profile_completion_percentage

    def get_incomplete_fields(self) -> list:
        """
        Get list of incomplete business profile fields with their display names.

        Returns:
            list: List of tuples (field_name, display_name) for incomplete fields
        """
        incomplete_fields = []
        field_mapping = {
            'legal_name': _('Legal Business Name'),
            'phone': _('Business Phone'),
            'contact_name': _('Primary Contact'),
            'address': _('Street Address'),
            'city': _('City'),
            'state': _('State'),
            'zip_code': _('ZIP Code'),
            'description': _('Business Description'),
            'logo': _('Business Logo'),
            'website': _('Website URL'),
        }

        for field_name, display_name in field_mapping.items():
            value = getattr(self, field_name, None)
            if not value:
                incomplete_fields.append((field_name, display_name))

        return incomplete_fields

    def get_completion_suggestions(self) -> list:
        """
        Get smart suggestions for completing business profile.

        Returns:
            list: List of suggestion dictionaries with priority and message
        """
        suggestions = []
        incomplete_fields = self.get_incomplete_fields()

        # High priority suggestions
        if not self.description:
            suggestions.append({
                'priority': 'high',
                'message': _('Add a business description to attract more customers'),
                'fields': ['description'],
                'action': 'add_description'
            })

        if not self.logo:
            suggestions.append({
                'priority': 'high',
                'message': _('Upload your business logo to build brand recognition'),
                'fields': ['logo'],
                'action': 'upload_logo'
            })

        if not self.website:
            suggestions.append({
                'priority': 'medium',
                'message': _('Add your website URL to drive more traffic'),
                'fields': ['website'],
                'action': 'add_website'
            })

        # Social media suggestions
        if not self.instagram and not self.facebook:
            suggestions.append({
                'priority': 'medium',
                'message': _('Connect your social media accounts to increase visibility'),
                'fields': ['instagram', 'facebook'],
                'action': 'add_social_media'
            })

        return suggestions

    def mark_verified(self, notes='', admin_user=None):
        """
        Mark business as verified.

        Args:
            notes (str): Verification notes
            admin_user: Admin user performing verification
        """
        self.is_verified = True
        self.verification_date = timezone.now()
        self.verification_notes = notes
        self.save(update_fields=['is_verified', 'verification_date', 'verification_notes'])

    def request_data_export(self):
        """Request data export for GDPR compliance."""
        self.data_export_requested = True
        self.data_export_request_date = timezone.now()
        self.save(update_fields=['data_export_requested', 'data_export_request_date'])

    def complete_data_export(self):
        """Mark data export as completed."""
        self.data_export_completed_date = timezone.now()
        self.save(update_fields=['data_export_completed_date'])

    def request_account_deletion(self):
        """Request account deletion for GDPR compliance."""
        self.account_deletion_requested = True
        self.account_deletion_request_date = timezone.now()
        self.save(update_fields=['account_deletion_requested', 'account_deletion_request_date'])

    def export_data(self, format='json') -> dict:
        """
        Export business profile data in specified format for GDPR compliance.

        Args:
            format (str): Export format ('json', 'dict')

        Returns:
            dict: Business profile data dictionary
        """
        data = {
            'business_info': {
                'legal_name': self.legal_name,
                'display_name': self.display_name,
                'description': self.description,
                'phone': self.phone,
                'contact_name': self.contact_name,
                'email': self.user.email,
                'address': self.address,
                'city': self.city,
                'state': self.get_state_display(),
                'county': self.county,
                'zip_code': self.zip_code,
                'ein': self.ein,
                'website': self.website,
                'instagram': self.instagram,
                'facebook': self.facebook,
            },
            'business_settings': {
                'business_visibility': self.get_business_visibility_display(),
                'show_contact_info': self.show_contact_info,
                'allow_direct_booking': self.allow_direct_booking,
                'accept_marketing_partnerships': self.accept_marketing_partnerships,
                'is_public': self.is_public,
            },
            'business_status': {
                'completion_percentage': self.profile_completion_percentage,
                'is_verified': self.is_verified,
                'verification_date': self.verification_date.isoformat() if self.verification_date else None,
                'venue_creation_tutorial_completed': self.venue_creation_tutorial_completed,
                'created': self.created.isoformat(),
                'updated': self.updated.isoformat(),
            },
            'export_info': {
                'export_date': timezone.now().isoformat(),
                'export_format': format,
                'data_version': '1.0',
            }
        }

        return data
