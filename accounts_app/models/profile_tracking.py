# --- Standard Library Imports ---
import json
from datetime import datetime

# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType


# --- Profile Change History ---

class ProfileChangeHistory(models.Model):
    """
    Track all changes made to user profiles for audit trail and rollback capabilities.
    
    Features:
    - Complete change tracking
    - Field-level change detection
    - Rollback capabilities
    - Admin audit trail
    """
    
    CHANGE_TYPES = [
        ('create', _('Profile Created')),
        ('update', _('Profile Updated')),
        ('verify', _('Profile Verified')),
        ('unverify', _('Profile Unverified')),
        ('privacy_change', _('Privacy Settings Changed')),
        ('picture_upload', _('Profile Picture Updated')),
        ('picture_remove', _('Profile Picture Removed')),
        ('data_export', _('Data Export Requested')),
        ('deletion_request', _('Account Deletion Requested')),
        ('admin_update', _('Admin Update')),
    ]
    
    # Generic foreign key to support both CustomerProfile and ServiceProviderProfile
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        help_text=_('Type of profile (Customer or Service Provider)')
    )
    object_id = models.PositiveIntegerField(
        help_text=_('ID of the profile object')
    )
    profile = GenericForeignKey('content_type', 'object_id')
    
    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='profile_changes',
        help_text=_('User who owns the profile')
    )
    
    change_type = models.CharField(
        _('change type'),
        max_length=20,
        choices=CHANGE_TYPES,
        help_text=_('Type of change made to the profile')
    )
    
    changed_fields = models.JSONField(
        _('changed fields'),
        default=dict,
        help_text=_('Dictionary of field names and their old/new values')
    )
    
    change_summary = models.CharField(
        _('change summary'),
        max_length=255,
        blank=True,
        help_text=_('Brief description of the changes made')
    )
    
    changed_by = models.ForeignKey(
        'CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='profile_changes_made',
        help_text=_('User who made the change (if different from profile owner)')
    )
    
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True,
        help_text=_('IP address from which the change was made')
    )
    
    user_agent = models.TextField(
        _('user agent'),
        blank=True,
        help_text=_('Browser/client information')
    )
    
    timestamp = models.DateTimeField(
        _('timestamp'),
        auto_now_add=True,
        help_text=_('When the change was made')
    )
    
    is_rollback = models.BooleanField(
        _('is rollback'),
        default=False,
        help_text=_('Whether this change is a rollback of a previous change')
    )
    
    rollback_of = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='rollbacks',
        help_text=_('Original change that this rollback reverts')
    )
    
    class Meta:
        verbose_name = _('Profile Change History')
        verbose_name_plural = _('Profile Change History')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['change_type']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['changed_by']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_change_type_display()} - {self.timestamp}"
    
    @classmethod
    def log_change(cls, profile, change_type, changed_fields=None, change_summary='', 
                   changed_by=None, request=None):
        """
        Log a profile change.
        
        Args:
            profile: Profile instance (CustomerProfile or ServiceProviderProfile)
            change_type: Type of change from CHANGE_TYPES
            changed_fields: Dictionary of changed fields with old/new values
            change_summary: Brief description of changes
            changed_by: User who made the change (if different from profile owner)
            request: Django request object for IP/user agent
        """
        content_type = ContentType.objects.get_for_model(profile)
        
        change_data = {
            'content_type': content_type,
            'object_id': profile.id,
            'user': profile.user,
            'change_type': change_type,
            'changed_fields': changed_fields or {},
            'change_summary': change_summary,
            'changed_by': changed_by,
        }
        
        if request:
            change_data.update({
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        return cls.objects.create(**change_data)
    
    def can_rollback(self) -> bool:
        """
        Check if this change can be rolled back.
        
        Returns:
            bool: True if rollback is possible
        """
        # Can't rollback if already rolled back
        if self.rollbacks.exists():
            return False
        
        # Can't rollback creation
        if self.change_type == 'create':
            return False
        
        # Can't rollback if profile no longer exists
        if not self.profile:
            return False
        
        return True
    
    def create_rollback(self, rolled_back_by=None, request=None):
        """
        Create a rollback of this change.
        
        Args:
            rolled_back_by: User performing the rollback
            request: Django request object
            
        Returns:
            ProfileChangeHistory: The rollback change record
        """
        if not self.can_rollback():
            raise ValueError("This change cannot be rolled back")
        
        # Create reverse changes
        reverse_fields = {}
        for field_name, change_data in self.changed_fields.items():
            if isinstance(change_data, dict) and 'old' in change_data and 'new' in change_data:
                reverse_fields[field_name] = {
                    'old': change_data['new'],
                    'new': change_data['old']
                }
        
        # Apply the rollback to the profile
        profile = self.profile
        for field_name, change_data in reverse_fields.items():
            if hasattr(profile, field_name):
                setattr(profile, field_name, change_data['new'])
        
        profile.save()
        
        # Log the rollback
        rollback_change = self.__class__.log_change(
            profile=profile,
            change_type='update',
            changed_fields=reverse_fields,
            change_summary=f'Rollback of change from {self.timestamp}',
            changed_by=rolled_back_by,
            request=request
        )
        
        rollback_change.is_rollback = True
        rollback_change.rollback_of = self
        rollback_change.save(update_fields=['is_rollback', 'rollback_of'])
        
        return rollback_change


# --- Profile Completion Tracking ---

class ProfileCompletionMilestone(models.Model):
    """
    Track profile completion milestones and achievements.
    
    Features:
    - Milestone tracking
    - Achievement badges
    - Progress rewards
    """
    
    MILESTONE_TYPES = [
        ('basic_info', _('Basic Information Complete')),
        ('contact_info', _('Contact Information Complete')),
        ('profile_picture', _('Profile Picture Added')),
        ('address_complete', _('Address Information Complete')),
        ('full_profile', _('Profile 100% Complete')),
        ('verified', _('Profile Verified')),
        ('first_booking', _('First Booking Made')),
        ('social_connected', _('Social Media Connected')),
    ]
    
    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='completion_milestones',
        help_text=_('User who achieved the milestone')
    )
    
    milestone_type = models.CharField(
        _('milestone type'),
        max_length=20,
        choices=MILESTONE_TYPES,
        help_text=_('Type of milestone achieved')
    )
    
    achieved_at = models.DateTimeField(
        _('achieved at'),
        auto_now_add=True,
        help_text=_('When the milestone was achieved')
    )
    
    completion_percentage = models.PositiveSmallIntegerField(
        _('completion percentage'),
        help_text=_('Profile completion percentage when milestone was achieved')
    )
    
    reward_given = models.BooleanField(
        _('reward given'),
        default=False,
        help_text=_('Whether a reward was given for this milestone')
    )
    
    reward_type = models.CharField(
        _('reward type'),
        max_length=50,
        blank=True,
        help_text=_('Type of reward given (badge, discount, etc.)')
    )
    
    class Meta:
        verbose_name = _('Profile Completion Milestone')
        verbose_name_plural = _('Profile Completion Milestones')
        ordering = ['-achieved_at']
        unique_together = ['user', 'milestone_type']
        indexes = [
            models.Index(fields=['user', 'milestone_type']),
            models.Index(fields=['achieved_at']),
            models.Index(fields=['completion_percentage']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_milestone_type_display()}"
    
    @classmethod
    def check_and_award_milestones(cls, user, profile):
        """
        Check and award any new milestones for a user's profile.
        
        Args:
            user: User instance
            profile: Profile instance (CustomerProfile or ServiceProviderProfile)
        """
        completion_percentage = profile.profile_completion_percentage
        
        # Define milestone conditions
        milestone_conditions = {
            'basic_info': lambda p: bool(p.first_name and p.last_name) if hasattr(p, 'first_name') else bool(p.legal_name),
            'contact_info': lambda p: bool(p.phone_number if hasattr(p, 'phone_number') else p.phone),
            'profile_picture': lambda p: bool(p.profile_picture if hasattr(p, 'profile_picture') else p.logo),
            'address_complete': lambda p: bool(p.address and p.city and p.zip_code),
            'full_profile': lambda p: completion_percentage >= 100,
            'verified': lambda p: p.is_verified,
        }
        
        # Check each milestone
        for milestone_type, condition in milestone_conditions.items():
            if condition(profile):
                # Award milestone if not already achieved
                milestone, created = cls.objects.get_or_create(
                    user=user,
                    milestone_type=milestone_type,
                    defaults={
                        'completion_percentage': completion_percentage,
                    }
                )
                
                if created:
                    # Award reward for new milestone
                    milestone.award_reward()
    
    def award_reward(self):
        """Award reward for achieving this milestone."""
        # Define rewards for different milestones
        rewards = {
            'basic_info': 'bronze_badge',
            'contact_info': 'silver_badge',
            'profile_picture': 'photo_badge',
            'address_complete': 'location_badge',
            'full_profile': 'gold_badge',
            'verified': 'verified_badge',
        }
        
        reward_type = rewards.get(self.milestone_type)
        if reward_type:
            self.reward_type = reward_type
            self.reward_given = True
            self.save(update_fields=['reward_type', 'reward_given'])
