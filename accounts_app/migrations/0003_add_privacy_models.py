# Generated by Django 5.2.3 on 2025-07-08 04:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0002_enhance_profile_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrivacyConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consent_type', models.CharField(choices=[('data_processing', 'Data Processing'), ('marketing', 'Marketing Communications'), ('analytics', 'Usage Analytics'), ('cookies', 'Cookies and Tracking'), ('third_party_sharing', 'Third Party Data Sharing'), ('location_tracking', 'Location Tracking'), ('personalized_ads', 'Personalized Advertisements'), ('social_integration', 'Social Media Integration')], help_text='Type of consent given or withdrawn', max_length=30, verbose_name='consent type')),
                ('is_granted', models.BooleanField(help_text='Whether consent is granted (True) or withdrawn (False)', verbose_name='consent granted')),
                ('consent_version', models.CharField(default='1.0', help_text='Version of the consent terms', max_length=20, verbose_name='consent version')),
                ('consent_text', models.TextField(blank=True, help_text='The actual consent text shown to the user', verbose_name='consent text')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address when consent was given/withdrawn', null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, help_text='Browser/device information when consent was given', verbose_name='user agent')),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='When consent was given or withdrawn', verbose_name='timestamp')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this consent expires (if applicable)', null=True, verbose_name='expires at')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='privacy_consents', to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Privacy Consent',
                'verbose_name_plural': 'Privacy Consents',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'consent_type'], name='accounts_ap_user_id_ae40ef_idx'), models.Index(fields=['timestamp'], name='accounts_ap_timesta_dc425a_idx'), models.Index(fields=['is_granted'], name='accounts_ap_is_gran_6793c6_idx'), models.Index(fields=['expires_at'], name='accounts_ap_expires_df4064_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProfilePrivacySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_visibility', models.CharField(choices=[('public', 'Public - Visible to everyone'), ('limited', 'Limited - Visible to service providers/customers only'), ('private', 'Private - Only visible to me'), ('custom', 'Custom - Use detailed settings below')], default='public', help_text='Who can see your profile information', max_length=20, verbose_name='profile visibility')),
                ('show_name', models.BooleanField(default=True, help_text='Allow others to see your name', verbose_name='show name')),
                ('show_profile_picture', models.BooleanField(default=True, help_text='Allow others to see your profile picture', verbose_name='show profile picture')),
                ('show_email', models.BooleanField(default=False, help_text='Allow others to see your email address', verbose_name='show email')),
                ('show_phone', models.BooleanField(default=False, help_text='Allow others to see your phone number', verbose_name='show phone')),
                ('show_address', models.BooleanField(default=False, help_text='Allow others to see your address', verbose_name='show address')),
                ('show_booking_history', models.BooleanField(default=False, help_text='Allow service providers to see your booking history', verbose_name='show booking history')),
                ('show_reviews', models.BooleanField(default=True, help_text='Allow others to see reviews you have written', verbose_name='show reviews')),
                ('contact_permission', models.CharField(choices=[('anyone', 'Anyone can contact me'), ('verified', 'Only verified users can contact me'), ('bookings', 'Only users I have bookings with'), ('none', 'No one can contact me directly')], default='verified', help_text='Who can contact you directly', max_length=20, verbose_name='contact permission')),
                ('allow_marketing_contact', models.BooleanField(default=False, help_text='Allow businesses to contact you for marketing purposes', verbose_name='allow marketing contact')),
                ('allow_promotional_contact', models.BooleanField(default=True, help_text='Allow CozyWish to send you promotional offers', verbose_name='allow promotional contact')),
                ('data_sharing_level', models.CharField(choices=[('full', 'Share all data for better recommendations'), ('limited', 'Share only basic data'), ('minimal', 'Share minimal data required for service'), ('none', 'Do not share data for analytics')], default='limited', help_text='How much data to share for analytics and recommendations', max_length=20, verbose_name='data sharing level')),
                ('allow_usage_analytics', models.BooleanField(default=True, help_text='Allow CozyWish to analyze your usage for service improvements', verbose_name='allow usage analytics')),
                ('allow_personalized_ads', models.BooleanField(default=False, help_text='Allow personalized advertisements based on your activity', verbose_name='allow personalized ads')),
                ('searchable_profile', models.BooleanField(default=True, help_text='Allow your profile to appear in search results', verbose_name='searchable profile')),
                ('show_in_recommendations', models.BooleanField(default=True, help_text='Allow your profile to be recommended to service providers', verbose_name='show in recommendations')),
                ('share_location_data', models.BooleanField(default=True, help_text='Share location data for better local recommendations', verbose_name='share location data')),
                ('track_activity', models.BooleanField(default=True, help_text='Track activity for personalized experience', verbose_name='track activity')),
                ('allow_social_login_data', models.BooleanField(default=True, help_text='Allow importing data from social login providers', verbose_name='allow social login data')),
                ('share_with_partners', models.BooleanField(default=False, help_text='Share data with trusted CozyWish partners', verbose_name='share with partners')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When privacy settings were created', verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When privacy settings were last updated', verbose_name='updated at')),
                ('last_reviewed_at', models.DateTimeField(blank=True, help_text='When user last reviewed their privacy settings', null=True, verbose_name='last reviewed at')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='privacy_settings', to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Profile Privacy Settings',
                'verbose_name_plural': 'Profile Privacy Settings',
                'ordering': ['-updated_at'],
                'indexes': [models.Index(fields=['user'], name='accounts_ap_user_id_c65144_idx'), models.Index(fields=['profile_visibility'], name='accounts_ap_profile_f2f31b_idx'), models.Index(fields=['contact_permission'], name='accounts_ap_contact_5f29d1_idx'), models.Index(fields=['data_sharing_level'], name='accounts_ap_data_sh_ee2ce7_idx')],
            },
        ),
    ]
