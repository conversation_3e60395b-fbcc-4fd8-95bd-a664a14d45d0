# Generated by Django 5.2.3 on 2025-07-08 03:51

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts_app', '0001_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProfileChangeHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(help_text='ID of the profile object')),
                ('change_type', models.CharField(choices=[('create', 'Profile Created'), ('update', 'Profile Updated'), ('verify', 'Profile Verified'), ('unverify', 'Profile Unverified'), ('privacy_change', 'Privacy Settings Changed'), ('picture_upload', 'Profile Picture Updated'), ('picture_remove', 'Profile Picture Removed'), ('data_export', 'Data Export Requested'), ('deletion_request', 'Account Deletion Requested'), ('admin_update', 'Admin Update')], help_text='Type of change made to the profile', max_length=20, verbose_name='change type')),
                ('changed_fields', models.JSONField(default=dict, help_text='Dictionary of field names and their old/new values', verbose_name='changed fields')),
                ('change_summary', models.CharField(blank=True, help_text='Brief description of the changes made', max_length=255, verbose_name='change summary')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address from which the change was made', null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, help_text='Browser/client information', verbose_name='user agent')),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='When the change was made', verbose_name='timestamp')),
                ('is_rollback', models.BooleanField(default=False, help_text='Whether this change is a rollback of a previous change', verbose_name='is rollback')),
            ],
            options={
                'verbose_name': 'Profile Change History',
                'verbose_name_plural': 'Profile Change History',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ProfileCompletionMilestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('milestone_type', models.CharField(choices=[('basic_info', 'Basic Information Complete'), ('contact_info', 'Contact Information Complete'), ('profile_picture', 'Profile Picture Added'), ('address_complete', 'Address Information Complete'), ('full_profile', 'Profile 100% Complete'), ('verified', 'Profile Verified'), ('first_booking', 'First Booking Made'), ('social_connected', 'Social Media Connected')], help_text='Type of milestone achieved', max_length=20, verbose_name='milestone type')),
                ('achieved_at', models.DateTimeField(auto_now_add=True, help_text='When the milestone was achieved', verbose_name='achieved at')),
                ('completion_percentage', models.PositiveSmallIntegerField(help_text='Profile completion percentage when milestone was achieved', verbose_name='completion percentage')),
                ('reward_given', models.BooleanField(default=False, help_text='Whether a reward was given for this milestone', verbose_name='reward given')),
                ('reward_type', models.CharField(blank=True, help_text='Type of reward given (badge, discount, etc.)', max_length=50, verbose_name='reward type')),
            ],
            options={
                'verbose_name': 'Profile Completion Milestone',
                'verbose_name_plural': 'Profile Completion Milestones',
                'ordering': ['-achieved_at'],
            },
        ),
        migrations.CreateModel(
            name='SessionSecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('login_success', 'Successful Login'), ('login_failed', 'Failed Login'), ('logout', 'User Logout'), ('session_timeout', 'Session Timeout'), ('concurrent_login', 'Concurrent Login Detected'), ('suspicious_location', 'Suspicious Location'), ('password_change', 'Password Changed'), ('security_termination', 'Security Termination')], help_text='Type of security event', max_length=50)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address where event occurred', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string')),
                ('location', models.CharField(blank=True, help_text='Approximate location', max_length=200)),
                ('details', models.JSONField(blank=True, default=dict, help_text='Additional event details')),
                ('risk_score', models.IntegerField(default=0, help_text='Risk score (0-100)')),
                ('is_suspicious', models.BooleanField(default=False, help_text='Whether this event is flagged as suspicious')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the event occurred')),
            ],
            options={
                'verbose_name': 'Session Security Event',
                'verbose_name_plural': 'Session Security Events',
                'db_table': 'accounts_session_security_events',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(help_text='Django session key', max_length=40, unique=True)),
                ('session_id', models.UUIDField(default=uuid.uuid4, help_text='Unique session identifier', unique=True)),
                ('device_name', models.CharField(blank=True, help_text='User-friendly device name', max_length=200)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the session', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string')),
                ('location', models.CharField(blank=True, help_text='Approximate location (city, country)', max_length=200)),
                ('is_remember_me', models.BooleanField(default=False, help_text="Whether this session uses 'remember me' functionality")),
                ('is_active', models.BooleanField(default=True, help_text='Whether the session is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the session was created')),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last activity timestamp')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When the session expires', null=True)),
                ('terminated_at', models.DateTimeField(blank=True, help_text='When the session was terminated', null=True)),
                ('termination_reason', models.CharField(blank=True, choices=[('logout', 'User logout'), ('timeout', 'Session timeout'), ('security', 'Security termination'), ('admin', 'Admin termination'), ('expired', 'Natural expiration')], help_text='Reason for session termination', max_length=50)),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'db_table': 'accounts_user_sessions',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='account_deletion_request_date',
            field=models.DateTimeField(blank=True, help_text='When account deletion was requested', null=True, verbose_name='account deletion request date'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='account_deletion_requested',
            field=models.BooleanField(default=False, help_text='Whether user has requested account deletion', verbose_name='account deletion requested'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='allow_marketing_contact',
            field=models.BooleanField(default=False, help_text='Allow service providers to contact you for marketing', verbose_name='allow marketing contact'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='data_export_completed_date',
            field=models.DateTimeField(blank=True, help_text='When data export was completed', null=True, verbose_name='data export completed date'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='data_export_request_date',
            field=models.DateTimeField(blank=True, help_text='When data export was requested', null=True, verbose_name='data export request date'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='data_export_requested',
            field=models.BooleanField(default=False, help_text='Whether user has requested data export', verbose_name='data export requested'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='is_verified',
            field=models.BooleanField(default=False, help_text='Whether the profile has been verified by admin', verbose_name='profile verified'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='last_completion_check',
            field=models.DateTimeField(blank=True, help_text='When profile completion was last calculated', null=True, verbose_name='last completion check'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='profile_completion_percentage',
            field=models.PositiveSmallIntegerField(default=0, help_text='Calculated percentage of profile completion (0-100)', verbose_name='profile completion percentage'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='profile_visibility',
            field=models.CharField(choices=[('public', 'Public - Visible to everyone'), ('private', 'Private - Only visible to me'), ('limited', 'Limited - Visible to service providers only')], default='public', help_text='Who can see your profile information', max_length=20, verbose_name='profile visibility'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='show_email_to_providers',
            field=models.BooleanField(default=True, help_text='Allow service providers to see your email address', verbose_name='show email to service providers'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='show_phone_to_providers',
            field=models.BooleanField(default=True, help_text='Allow service providers to see your phone number', verbose_name='show phone to service providers'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='verification_date',
            field=models.DateTimeField(blank=True, help_text='When the profile was verified', null=True, verbose_name='verification date'),
        ),
        migrations.AddField(
            model_name='customerprofile',
            name='verification_notes',
            field=models.TextField(blank=True, help_text='Admin notes about profile verification', max_length=500, verbose_name='verification notes'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='accept_marketing_partnerships',
            field=models.BooleanField(default=False, help_text='Allow CozyWish to include you in marketing partnerships', verbose_name='accept marketing partnerships'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='account_deletion_request_date',
            field=models.DateTimeField(blank=True, help_text='When account deletion was requested', null=True, verbose_name='account deletion request date'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='account_deletion_requested',
            field=models.BooleanField(default=False, help_text='Whether business has requested account deletion', verbose_name='account deletion requested'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='allow_direct_booking',
            field=models.BooleanField(default=True, help_text='Allow customers to book services directly', verbose_name='allow direct booking'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='business_visibility',
            field=models.CharField(choices=[('public', 'Public - Visible in all listings'), ('limited', 'Limited - Visible to existing customers only'), ('private', 'Private - Not visible in public listings')], default='public', help_text='Who can see your business in listings', max_length=20, verbose_name='business visibility'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='data_export_completed_date',
            field=models.DateTimeField(blank=True, help_text='When data export was completed', null=True, verbose_name='data export completed date'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='data_export_request_date',
            field=models.DateTimeField(blank=True, help_text='When data export was requested', null=True, verbose_name='data export request date'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='data_export_requested',
            field=models.BooleanField(default=False, help_text='Whether business has requested data export', verbose_name='data export requested'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='is_verified',
            field=models.BooleanField(default=False, help_text='Whether the business has been verified by admin', verbose_name='business verified'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='last_completion_check',
            field=models.DateTimeField(blank=True, help_text='When profile completion was last calculated', null=True, verbose_name='last completion check'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='profile_completion_percentage',
            field=models.PositiveSmallIntegerField(default=0, help_text='Calculated percentage of profile completion (0-100)', verbose_name='profile completion percentage'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='show_contact_info',
            field=models.BooleanField(default=True, help_text='Show phone and email to potential customers', verbose_name='show contact information'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='verification_date',
            field=models.DateTimeField(blank=True, help_text='When the business was verified', null=True, verbose_name='verification date'),
        ),
        migrations.AddField(
            model_name='serviceproviderprofile',
            name='verification_notes',
            field=models.TextField(blank=True, help_text='Admin notes about business verification', max_length=500, verbose_name='verification notes'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='email',
            field=models.EmailField(error_messages={'unique': 'A user with this email already exists.'}, help_text='Required. Enter a valid email address.', max_length=254, unique=True, verbose_name='email address'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('customer', 'Customer'), ('service_provider', 'Service Provider'), ('admin', 'Admin')], default='customer', help_text='User role determines access permissions.', max_length=20, verbose_name='role'),
        ),
        migrations.AddIndex(
            model_name='customerprofile',
            index=models.Index(fields=['profile_completion_percentage'], name='accounts_ap_profile_dda421_idx'),
        ),
        migrations.AddIndex(
            model_name='customerprofile',
            index=models.Index(fields=['is_verified'], name='accounts_ap_is_veri_bf086e_idx'),
        ),
        migrations.AddIndex(
            model_name='customerprofile',
            index=models.Index(fields=['profile_visibility'], name='accounts_ap_profile_a549ff_idx'),
        ),
        migrations.AddIndex(
            model_name='customerprofile',
            index=models.Index(fields=['data_export_requested'], name='accounts_ap_data_ex_45b6e5_idx'),
        ),
        migrations.AddIndex(
            model_name='customerprofile',
            index=models.Index(fields=['account_deletion_requested'], name='accounts_ap_account_b72368_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceproviderprofile',
            index=models.Index(fields=['profile_completion_percentage'], name='accounts_ap_profile_9ad98f_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceproviderprofile',
            index=models.Index(fields=['is_verified'], name='accounts_ap_is_veri_4d8f37_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceproviderprofile',
            index=models.Index(fields=['business_visibility'], name='accounts_ap_busines_4a5373_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceproviderprofile',
            index=models.Index(fields=['data_export_requested'], name='accounts_ap_data_ex_515776_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceproviderprofile',
            index=models.Index(fields=['account_deletion_requested'], name='accounts_ap_account_6e2941_idx'),
        ),
        migrations.AddField(
            model_name='profilechangehistory',
            name='changed_by',
            field=models.ForeignKey(blank=True, help_text='User who made the change (if different from profile owner)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='profile_changes_made', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='profilechangehistory',
            name='content_type',
            field=models.ForeignKey(help_text='Type of profile (Customer or Service Provider)', on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='profilechangehistory',
            name='rollback_of',
            field=models.ForeignKey(blank=True, help_text='Original change that this rollback reverts', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rollbacks', to='accounts_app.profilechangehistory'),
        ),
        migrations.AddField(
            model_name='profilechangehistory',
            name='user',
            field=models.ForeignKey(help_text='User who owns the profile', on_delete=django.db.models.deletion.CASCADE, related_name='profile_changes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='profilecompletionmilestone',
            name='user',
            field=models.ForeignKey(help_text='User who achieved the milestone', on_delete=django.db.models.deletion.CASCADE, related_name='completion_milestones', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionsecurityevent',
            name='user',
            field=models.ForeignKey(blank=True, help_text='User associated with the event (if any)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='usersession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionsecurityevent',
            name='session',
            field=models.ForeignKey(blank=True, help_text='Session associated with the event (if any)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_events', to='accounts_app.usersession'),
        ),
        migrations.AddIndex(
            model_name='profilechangehistory',
            index=models.Index(fields=['user', 'timestamp'], name='accounts_ap_user_id_45b224_idx'),
        ),
        migrations.AddIndex(
            model_name='profilechangehistory',
            index=models.Index(fields=['content_type', 'object_id'], name='accounts_ap_content_90f70c_idx'),
        ),
        migrations.AddIndex(
            model_name='profilechangehistory',
            index=models.Index(fields=['change_type'], name='accounts_ap_change__202a4e_idx'),
        ),
        migrations.AddIndex(
            model_name='profilechangehistory',
            index=models.Index(fields=['timestamp'], name='accounts_ap_timesta_aea9bd_idx'),
        ),
        migrations.AddIndex(
            model_name='profilechangehistory',
            index=models.Index(fields=['changed_by'], name='accounts_ap_changed_9420a7_idx'),
        ),
        migrations.AddIndex(
            model_name='profilecompletionmilestone',
            index=models.Index(fields=['user', 'milestone_type'], name='accounts_ap_user_id_f1c6ca_idx'),
        ),
        migrations.AddIndex(
            model_name='profilecompletionmilestone',
            index=models.Index(fields=['achieved_at'], name='accounts_ap_achieve_485b50_idx'),
        ),
        migrations.AddIndex(
            model_name='profilecompletionmilestone',
            index=models.Index(fields=['completion_percentage'], name='accounts_ap_complet_c660f9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='profilecompletionmilestone',
            unique_together={('user', 'milestone_type')},
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', 'is_active'], name='accounts_us_user_id_a477bb_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_key'], name='accounts_us_session_6ada2e_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['last_activity'], name='accounts_us_last_ac_e5bb4c_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionsecurityevent',
            index=models.Index(fields=['user', 'event_type'], name='accounts_se_user_id_34ca00_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionsecurityevent',
            index=models.Index(fields=['ip_address'], name='accounts_se_ip_addr_6aae0c_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionsecurityevent',
            index=models.Index(fields=['created_at'], name='accounts_se_created_5cd442_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionsecurityevent',
            index=models.Index(fields=['is_suspicious'], name='accounts_se_is_susp_40baf2_idx'),
        ),
    ]
