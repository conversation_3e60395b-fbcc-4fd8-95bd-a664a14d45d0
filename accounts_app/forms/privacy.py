# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

# --- Third-Party Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, HTML, Submit, Button
from crispy_forms.bootstrap import InlineRadios, InlineCheckboxes

# --- Local App Imports ---
from ..models import ProfilePrivacySettings, PrivacyConsent
from .common import AccessibleFormMixin


class ProfilePrivacySettingsForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for managing comprehensive profile privacy settings.
    
    Features:
    - Grouped privacy controls
    - Clear explanations for each setting
    - Validation for setting combinations
    - GDPR compliance helpers
    """
    
    class Meta:
        model = ProfilePrivacySettings
        fields = [
            'profile_visibility', 'show_name', 'show_profile_picture', 'show_email',
            'show_phone', 'show_address', 'show_booking_history', 'show_reviews',
            'contact_permission', 'allow_marketing_contact', 'allow_promotional_contact',
            'data_sharing_level', 'allow_usage_analytics', 'allow_personalized_ads',
            'searchable_profile', 'show_in_recommendations', 'share_location_data',
            'track_activity', 'allow_social_login_data', 'share_with_partners'
        ]
        widgets = {
            'profile_visibility': forms.RadioSelect(),
            'contact_permission': forms.RadioSelect(),
            'data_sharing_level': forms.RadioSelect(),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add help text and styling
        self.fields['profile_visibility'].help_text = _(
            'Choose who can see your profile. Select "Custom" for detailed control.'
        )
        self.fields['contact_permission'].help_text = _(
            'Control who can contact you directly through the platform.'
        )
        self.fields['data_sharing_level'].help_text = _(
            'Choose how much data to share for personalized recommendations.'
        )
        
        # Set up crispy forms layout
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'privacy-settings-form'
        
        self.helper.layout = Layout(
            HTML('<div class="privacy-form-intro mb-4">'),
            HTML('<h4 class="text-brand-cw"><i class="fas fa-shield-alt me-2"></i>Privacy Settings</h4>'),
            HTML('<p class="text-muted">Control how your information is shared and who can see your profile.</p>'),
            HTML('</div>'),
            
            Fieldset(
                _('Profile Visibility'),
                InlineRadios('profile_visibility'),
                HTML('<div id="custom-visibility-controls" style="display: none;" class="mt-3 p-3 bg-light rounded">'),
                HTML('<h6>Custom Visibility Controls</h6>'),
                Row(
                    Column('show_name', css_class='col-md-6'),
                    Column('show_profile_picture', css_class='col-md-6'),
                ),
                Row(
                    Column('show_email', css_class='col-md-6'),
                    Column('show_phone', css_class='col-md-6'),
                ),
                Row(
                    Column('show_address', css_class='col-md-6'),
                    Column('show_booking_history', css_class='col-md-6'),
                ),
                'show_reviews',
                HTML('</div>'),
                css_class='privacy-section'
            ),
            
            Fieldset(
                _('Contact Permissions'),
                InlineRadios('contact_permission'),
                Row(
                    Column('allow_marketing_contact', css_class='col-md-6'),
                    Column('allow_promotional_contact', css_class='col-md-6'),
                ),
                css_class='privacy-section'
            ),
            
            Fieldset(
                _('Data Sharing & Analytics'),
                InlineRadios('data_sharing_level'),
                Row(
                    Column('allow_usage_analytics', css_class='col-md-6'),
                    Column('allow_personalized_ads', css_class='col-md-6'),
                ),
                css_class='privacy-section'
            ),
            
            Fieldset(
                _('Search & Discovery'),
                Row(
                    Column('searchable_profile', css_class='col-md-6'),
                    Column('show_in_recommendations', css_class='col-md-6'),
                ),
                css_class='privacy-section'
            ),
            
            Fieldset(
                _('Location & Tracking'),
                Row(
                    Column('share_location_data', css_class='col-md-6'),
                    Column('track_activity', css_class='col-md-6'),
                ),
                css_class='privacy-section'
            ),
            
            Fieldset(
                _('Third-Party Integration'),
                Row(
                    Column('allow_social_login_data', css_class='col-md-6'),
                    Column('share_with_partners', css_class='col-md-6'),
                ),
                css_class='privacy-section'
            ),
            
            HTML('<div class="form-actions mt-4">'),
            Submit('save', _('Save Privacy Settings'), css_class='btn btn-primary me-2'),
            Button('review_later', _('Review Later'), css_class='btn btn-outline-secondary'),
            HTML('</div>'),
        )
    
    def clean(self):
        """Validate privacy settings combinations."""
        cleaned_data = super().clean()
        
        profile_visibility = cleaned_data.get('profile_visibility')
        contact_permission = cleaned_data.get('contact_permission')
        
        # Validate logical combinations
        if profile_visibility == 'private' and contact_permission == 'anyone':
            raise ValidationError(
                _('If your profile is private, you cannot allow anyone to contact you.')
            )
        
        # Warn about restrictive settings
        if (profile_visibility == 'private' and 
            not cleaned_data.get('searchable_profile', True)):
            self.add_error(None, ValidationError(
                _('Warning: Your profile will be very difficult to find with these settings.')
            ))
        
        return cleaned_data
    
    def save(self, commit=True):
        """Save privacy settings and mark as reviewed."""
        instance = super().save(commit=False)
        
        if commit:
            instance.save()
            instance.mark_as_reviewed()
        
        return instance


class PrivacyConsentForm(forms.Form):
    """
    Form for recording privacy consent decisions.
    
    Features:
    - GDPR compliant consent recording
    - Clear consent descriptions
    - Granular consent options
    """
    
    CONSENT_CHOICES = [
        ('data_processing', _('Data Processing')),
        ('marketing', _('Marketing Communications')),
        ('analytics', _('Usage Analytics')),
        ('cookies', _('Cookies and Tracking')),
        ('third_party_sharing', _('Third Party Data Sharing')),
        ('location_tracking', _('Location Tracking')),
        ('personalized_ads', _('Personalized Advertisements')),
        ('social_integration', _('Social Media Integration')),
    ]
    
    # Create individual consent fields
    data_processing = forms.BooleanField(
        label=_('Data Processing'),
        required=True,
        help_text=_('Required: Allow us to process your data to provide our services.')
    )
    
    marketing = forms.BooleanField(
        label=_('Marketing Communications'),
        required=False,
        help_text=_('Optional: Receive marketing emails about new services and offers.')
    )
    
    analytics = forms.BooleanField(
        label=_('Usage Analytics'),
        required=False,
        help_text=_('Optional: Help us improve our service by analyzing usage patterns.')
    )
    
    cookies = forms.BooleanField(
        label=_('Cookies and Tracking'),
        required=False,
        help_text=_('Optional: Allow cookies for enhanced functionality and analytics.')
    )
    
    third_party_sharing = forms.BooleanField(
        label=_('Third Party Data Sharing'),
        required=False,
        help_text=_('Optional: Share data with trusted partners for better recommendations.')
    )
    
    location_tracking = forms.BooleanField(
        label=_('Location Tracking'),
        required=False,
        help_text=_('Optional: Use your location for local service recommendations.')
    )
    
    personalized_ads = forms.BooleanField(
        label=_('Personalized Advertisements'),
        required=False,
        help_text=_('Optional: Show ads tailored to your interests and activity.')
    )
    
    social_integration = forms.BooleanField(
        label=_('Social Media Integration'),
        required=False,
        help_text=_('Optional: Connect with social media for easier login and sharing.')
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set up crispy forms layout
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'consent-form'
        
        self.helper.layout = Layout(
            HTML('<div class="consent-form-intro mb-4">'),
            HTML('<h4 class="text-brand-cw"><i class="fas fa-check-circle me-2"></i>Privacy Consent</h4>'),
            HTML('<p class="text-muted">Please review and provide consent for the following data uses:</p>'),
            HTML('</div>'),
            
            Fieldset(
                _('Required Consent'),
                'data_processing',
                css_class='required-consent'
            ),
            
            Fieldset(
                _('Optional Consent'),
                'marketing',
                'analytics',
                'cookies',
                'third_party_sharing',
                'location_tracking',
                'personalized_ads',
                'social_integration',
                css_class='optional-consent'
            ),
            
            HTML('<div class="form-actions mt-4">'),
            Submit('save_consent', _('Save Consent Preferences'), css_class='btn btn-primary'),
            HTML('</div>'),
        )
    
    def save_consents(self, request=None):
        """Save all consent decisions."""
        if not self.user:
            raise ValueError("User is required to save consents")
        
        consent_records = []
        
        for field_name, consent_type in [
            ('data_processing', 'data_processing'),
            ('marketing', 'marketing'),
            ('analytics', 'analytics'),
            ('cookies', 'cookies'),
            ('third_party_sharing', 'third_party_sharing'),
            ('location_tracking', 'location_tracking'),
            ('personalized_ads', 'personalized_ads'),
            ('social_integration', 'social_integration'),
        ]:
            is_granted = self.cleaned_data.get(field_name, False)
            
            consent = PrivacyConsent.record_consent(
                user=self.user,
                consent_type=consent_type,
                is_granted=is_granted,
                version='1.0',
                consent_text=self.fields[field_name].help_text,
                request=request
            )
            consent_records.append(consent)
        
        return consent_records


class DataExportRequestForm(forms.Form):
    """
    Form for requesting data export (GDPR compliance).
    
    Features:
    - Multiple export formats
    - Data type selection
    - Delivery method options
    """
    
    EXPORT_FORMAT_CHOICES = [
        ('json', _('JSON - Machine readable format')),
        ('csv', _('CSV - Spreadsheet format')),
        ('pdf', _('PDF - Human readable document')),
    ]
    
    DATA_TYPE_CHOICES = [
        ('profile', _('Profile Information')),
        ('bookings', _('Booking History')),
        ('reviews', _('Reviews and Ratings')),
        ('preferences', _('Settings and Preferences')),
        ('activity', _('Activity Log')),
        ('all', _('All Available Data')),
    ]
    
    export_format = forms.ChoiceField(
        label=_('Export Format'),
        choices=EXPORT_FORMAT_CHOICES,
        initial='json',
        widget=forms.RadioSelect(),
        help_text=_('Choose the format for your data export.')
    )
    
    data_types = forms.MultipleChoiceField(
        label=_('Data to Export'),
        choices=DATA_TYPE_CHOICES,
        initial=['all'],
        widget=forms.CheckboxSelectMultiple(),
        help_text=_('Select which types of data to include in the export.')
    )
    
    email_delivery = forms.BooleanField(
        label=_('Email Delivery'),
        initial=True,
        required=False,
        help_text=_('Send the export file to your email address when ready.')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('<div class="export-form-intro mb-4">'),
            HTML('<h4 class="text-brand-cw"><i class="fas fa-download me-2"></i>Data Export Request</h4>'),
            HTML('<p class="text-muted">Request a copy of your personal data stored in our system.</p>'),
            HTML('</div>'),
            
            InlineRadios('export_format'),
            'data_types',
            'email_delivery',
            
            HTML('<div class="form-actions mt-4">'),
            Submit('request_export', _('Request Data Export'), css_class='btn btn-primary'),
            HTML('</div>'),
        )
