# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import PasswordChangeForm, UserCreationForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports ---
from crispy_forms.helper import FormHelper

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ProfileImageForm, ImageUploadForm
    from utils.image_service import ImageService
except ImportError:
    ProfileImageForm = None
    ImageUploadForm = None
    ImageService = None

# --- Local App Imports ---
from ..crispy_layouts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>orm<PERSON>el<PERSON>, PasswordChangeFormHelper, ProfileFormHelper
from ..fields import Phone<PERSON>umberField
from ..logging_utils import log_error
from ..models import CustomUser, ServiceProviderProfile
from ..validators import normalize_phone
from .common import AccessibleFormMixin



# --- Service Provider Signup Form ---

class ServiceProviderSignupForm(AccessibleFormMixin, UserCreationForm):
    """
    Sign up a new service provider with business details.

    Features:
    - Email uniqueness validation
    - Password confirmation
    - Business profile defaults
    - Accessible form styling
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
            }
        ),
        help_text=_('This will be your login email. We\'ll send a verification email.')
    )

    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Create a strong password'),
                'autocomplete': 'new-password',
            }
        ),
        help_text=_(
            'Your password must contain at least 8 characters and '
            'cannot be entirely numeric.'
        )
    )

    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Confirm your password'),
                'autocomplete': 'new-password',
            }
        ),
        help_text=_('Enter the same password as before, for verification.')
    )

    business_name = forms.CharField(
        label=_('Business Name'),
        max_length=200,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your official business name'),
            }
        ),
        help_text=_('Official registered business name')
    )

    business_phone_number = PhoneNumberField(
        label=_('Business Phone Number'),
        max_length=20,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': '+****************',
                'type': 'tel',
            }
        ),
        help_text=_('Primary business contact number')
    )

    contact_person_name = forms.CharField(
        label=_('Contact Person Name'),
        max_length=100,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter the primary contact person name'),
            }
        ),
        help_text=_('Name of the primary contact person for the business')
    )

    business_address = forms.CharField(
        label=_('Business Address'),
        max_length=255,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your business street address'),
            }
        )
    )

    city = forms.CharField(
        label=_('City'),
        max_length=100,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter city'),
            }
        )
    )

    state = forms.ChoiceField(
        label=_('State'),
        choices=[('', _('Select State'))] + list(ServiceProviderProfile.STATE_CHOICES),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    zip_code = forms.CharField(
        label=_('ZIP Code'),
        max_length=10,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter ZIP code'),
            }
        )
    )

    ein = forms.CharField(
        label=_('EIN (Tax ID)'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('XX-XXXXXXX (optional)'),
            }
        ),
        help_text=_('Employer Identification Number (optional)')
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy form helper."""
        super().__init__(*args, **kwargs)
        self.helper = SignupFormHelper()

    def clean_email(self) -> str:
        """
        Ensure the email address is unique.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError(
                _('A user with this email already exists.'),
                code='email_exists'
            )
        return email

    def clean_password1(self):
        """
        Validate password1 using Django's password validators.

        This ensures password strength errors are displayed on the password1 field
        instead of password2 field.

        Returns:
            str: The cleaned password.

        Raises:
            ValidationError: If password doesn't meet requirements.
        """
        password1 = self.cleaned_data.get('password1')
        if password1:
            # Import here to avoid circular imports
            from django.contrib.auth.password_validation import validate_password
            try:
                # Create a temporary user instance for validation if none exists
                user = self.instance
                if user is None:
                    # Create a temporary user with the email for validation
                    email = self.cleaned_data.get('email')
                    user = CustomUser(email=email)
                validate_password(password1, user)
            except ValidationError as error:
                raise ValidationError(error.messages)
        return password1

    def clean_password2(self):
        """
        Override the default clean_password2 to avoid duplicate validation errors.

        Only check if passwords match, not password strength (already done in clean_password1).

        Returns:
            str: The cleaned password2.

        Raises:
            ValidationError: If passwords don't match.
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _("The two password fields didn't match."),
                code='password_mismatch',
            )
        return password2

    def validate_password_for_user(self, user):
        """
        Override to prevent password validation errors from being added to password2.

        Since we already validate passwords in clean_password1, we don't need
        to validate them again here. This prevents duplicate validation and
        ensures errors appear on the password1 field.
        """
        # Do nothing - password validation is handled in clean_password1
        # This prevents Django from running validation again and adding errors to password2
        pass

    def clean_business_phone_number(self) -> str:
        """
        Normalize and validate the business phone number.

        Returns:
            str: The cleaned phone number.
        """
        number = self.cleaned_data.get('business_phone_number')
        return normalize_phone(number) if number else number

    def save(self, commit: bool = True) -> CustomUser:
        """
        Create and return a new service provider user.

        Args:
            commit (bool): Whether to save the user immediately.

        Returns:
            CustomUser: The created user instance.
        """
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.role = CustomUser.SERVICE_PROVIDER
        user.is_active = False
        if commit:
            user.save()
        return user



# --- Service Provider Login Form ---

class ServiceProviderLoginForm(AccessibleFormMixin, forms.Form):
    """
    Authenticate a service provider using email and password.

    Features:
    - Role validation
    - Active account check
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
            }
        )
    )

    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your password'),
                'autocomplete': 'current-password',
            }
        )
    )

    def __init__(self, request=None, *args, **kwargs):
        """
        Store HTTP request for authentication context.
        """
        self.request = request
        super().__init__(*args, **kwargs)
        self.helper = LoginFormHelper()

    def clean(self) -> dict:
        """
        Validate the user's email and password combination.

        Raises:
            ValidationError: On authentication failure.

        Returns:
            dict: The cleaned data.
        """
        data = super().clean()
        email = data.get('email')
        password = data.get('password')

        if email and password:
            from django.contrib.auth import authenticate

            try:
                user = CustomUser.objects.get(email=email)
                if not user.is_service_provider:
                    raise ValidationError(
                        _('Invalid email or password.'), code='invalid_login'
                    )
                if not user.is_active and user.check_password(password):
                    raise ValidationError(
                        _('Please verify your email to activate your account.'),
                        code='inactive'
                    )
                authenticated = authenticate(
                    self.request, username=email, password=password
                )
                if not authenticated:
                    raise ValidationError(
                        _('Invalid email or password.'), code='invalid_login'
                    )
                self.user_cache = authenticated
            except CustomUser.DoesNotExist:
                raise ValidationError(
                    _('Invalid email or password.'), code='invalid_login'
                )
        return data

    def get_user(self):
        """
        Retrieve the authenticated user after form validation.

        Returns:
            CustomUser or None: The authenticated user.
        """
        return getattr(self, 'user_cache', None)



# --- Service Provider Password Change Form ---

class ServiceProviderPasswordChangeForm(AccessibleFormMixin, PasswordChangeForm):
    """
    Allow service providers to change their password.

    Inherits built-in PasswordChangeForm with custom styling.
    """
    old_password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your current password'),
                'autocomplete': 'current-password',
            }
        )
    )

    new_password1 = forms.CharField(
        label=_('New Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your new password'),
                'autocomplete': 'new-password',
            }
        ),
        help_text=_(
            'Your password must contain at least 8 characters and '
            'cannot be entirely numeric.'
        )
    )

    new_password2 = forms.CharField(
        label=_('Confirm New Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Confirm your new password'),
                'autocomplete': 'new-password',
            }
        )
    )

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy form helper."""
        super().__init__(*args, **kwargs)
        self.helper = PasswordChangeFormHelper()



# --- Service Provider Profile Form ---

class ServiceProviderProfileForm(AccessibleFormMixin, forms.ModelForm):
    """
    Edit service provider business profile details.

    Features:
    - Business info fields
    - Logo upload and processing
    - Visibility toggle
    """
    display_name = forms.CharField(
        label=_('DBA Name'),
        max_length=200,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Doing Business As (optional)'),
            }
        ),
        help_text=_('If different from business name')
    )

    description = forms.CharField(
        label=_('Business Description'),
        max_length=500,
        required=False,
        widget=forms.Textarea(
            attrs={
                'class': 'form-control',
                'placeholder': _('Describe your business and services'),
                'rows': 3,
            }
        ),
        help_text=_('Brief description of your business (max 500 characters)')
    )

    website = forms.URLField(
        label=_('Website URL'),
        required=False,
        widget=forms.URLInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('https://www.yourbusiness.com'),
            }
        )
    )

    instagram = forms.URLField(
        label=_('Instagram URL'),
        required=False,
        widget=forms.URLInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('https://www.instagram.com/yourbusiness'),
            }
        )
    )

    facebook = forms.URLField(
        label=_('Facebook URL'),
        required=False,
        widget=forms.URLInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('https://www.facebook.com/yourbusiness'),
            }
        )
    )

    logo = forms.ImageField(
        label=_('Business Logo'),
        required=False,
        widget=forms.FileInput(
            attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/jpg,image/png,image/webp,image/gif,image/svg+xml,image/heic,image/heif',
                'data-max-size': '10485760',  # 10MB in bytes
                'data-allowed-types': 'jpeg,jpg,png,webp,gif,svg,heic,heif',
                'data-preview': 'true',
                'data-logo-mode': 'true',
            }
        ),
        help_text=_('Upload your business logo (JPEG, PNG, WebP, GIF, SVG, HEIC/HEIF supported, max 10MB). Logo will be automatically optimized and resized.')
    )

    is_public = forms.BooleanField(
        label=_('Make business visible to customers'),
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text=_('Uncheck to temporarily hide your business from customers')
    )

    class Meta:
        model = ServiceProviderProfile
        fields = [
            'legal_name', 'display_name', 'description', 'phone',
            'contact_name', 'address', 'city', 'state', 'county', 'zip_code',
            'ein', 'website', 'instagram', 'facebook', 'logo', 'is_public',
        ]

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy form helper."""
        super().__init__(*args, **kwargs)
        self.helper = ProfileFormHelper()

    def clean_logo(self):
        """
        Validate and process the business logo upload with comprehensive checks.

        Returns:
            UploadedFile or None: The validated and processed image or None.

        Raises:
            ValidationError: On invalid, oversized, or unsupported image.
        """
        logo = self.cleaned_data.get('logo')
        if not logo:
            return None

        # Skip validation for existing images (when editing without new upload)
        if hasattr(logo, 'url') and not hasattr(logo, 'content_type'):
            return logo

        # Use enhanced image validation service with logo-specific settings
        try:
            from ..services.profile_image_validation import ProfileImageValidator

            # Create validator with logo-specific configuration
            validator = ProfileImageValidator(user=self.instance.user if self.instance else None)

            # Override settings for logos (allow smaller minimum dimensions)
            validator.MIN_DIMENSION = 50  # Logos can be smaller than profile pictures
            validator.MAX_DIMENSION = 2000  # Reasonable max for logos

            # Add SVG and GIF support for logos
            validator.ALLOWED_FORMATS.update({
                'GIF': ['image/gif'],
                'SVG': ['image/svg+xml'],
            })
            validator.ALLOWED_EXTENSIONS.extend(['.gif', '.svg'])

            processed_logo, validation_info = validator.validate_and_process(logo)

            # Store validation warnings for display
            if validation_info.get('warnings'):
                if not hasattr(self, '_validation_warnings'):
                    self._validation_warnings = []
                self._validation_warnings.extend(validation_info['warnings'])

            # Log successful validation
            log_info(
                'Business logo validated and processed successfully',
                user=self.instance.user if self.instance else None,
                extra_data={
                    'original_format': validation_info.get('original_format'),
                    'dimensions': validation_info.get('dimensions'),
                    'file_size': validation_info.get('file_size'),
                    'image_hash': validation_info.get('image_hash'),
                }
            )

            return processed_logo

        except ImportError:
            # Fallback to basic validation if service not available
            return self._basic_logo_validation(logo)
        except ValidationError:
            raise
        except Exception as e:
            log_error(
                error_type='logo_validation',
                error_message='Unexpected error during enhanced logo validation',
                user=self.instance.user if self.instance else None,
                exception=e
            )
            # Fallback to basic validation
            return self._basic_logo_validation(logo)

    def _basic_logo_validation(self, logo):
        """Fallback basic logo validation."""
        # Enhanced file size validation (10MB limit)
        if hasattr(logo, 'size'):
            max_size = 10 * 1024 * 1024  # 10MB
            if logo.size > max_size:
                raise ValidationError(
                    _('Logo file is too large. Maximum file size is 10MB.'),
                    code='file_too_large'
                )

            # Minimum file size validation
            min_size = 1024  # 1KB
            if logo.size < min_size:
                raise ValidationError(
                    _('Logo file is too small. Minimum file size is 1KB.'),
                    code='file_too_small'
                )

        # Enhanced file type validation
        allowed_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
            'image/gif', 'image/svg+xml', 'image/heic', 'image/heif'
        ]
        if hasattr(logo, 'content_type'):
            if logo.content_type.lower() not in allowed_types:
                raise ValidationError(
                    _('Invalid logo format. Supported formats: JPEG, PNG, WebP, GIF, SVG, HEIC/HEIF.'),
                    code='invalid_format'
                )

        return logo

    def clean_phone(self):
        """
        Validate and normalize phone number format.
        """
        phone = self.cleaned_data.get('phone')
        if not phone:
            return phone
            
        # Remove common formatting characters
        cleaned_phone = ''.join(filter(str.isdigit, phone))
        
        # Check if it starts with country code
        if phone.startswith('+'):
            # International format
            if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
                raise ValidationError(
                    _('Phone number must be between 10-15 digits.'),
                    code='invalid_phone_length'
                )
        else:
            # US format validation
            if len(cleaned_phone) == 10:
                # Add US country code
                cleaned_phone = '1' + cleaned_phone
            elif len(cleaned_phone) == 11 and cleaned_phone.startswith('1'):
                # Already has US country code
                pass
            else:
                raise ValidationError(
                    _('Please enter a valid US phone number (10 digits) or international number with country code.'),
                    code='invalid_phone_format'
                )
        
        return phone

    def clean_website(self):
        """
        Validate website URL format.
        """
        website = self.cleaned_data.get('website')
        if not website:
            return website
            
        import re
        
        # Add protocol if missing
        if not website.startswith(('http://', 'https://')):
            website = 'https://' + website
            
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
            
        if not url_pattern.match(website):
            raise ValidationError(
                _('Please enter a valid website URL.'),
                code='invalid_website_url'
            )
            
        return website

    def clean_instagram(self):
        """
        Validate Instagram URL format.
        """
        instagram = self.cleaned_data.get('instagram')
        if not instagram:
            return instagram
            
        import re
        
        # Allow various Instagram URL formats
        if not instagram.startswith('http'):
            if instagram.startswith('@'):
                instagram = 'https://www.instagram.com/' + instagram[1:]
            elif '/' not in instagram:
                instagram = 'https://www.instagram.com/' + instagram
            else:
                instagram = 'https://' + instagram
                
        # Validate Instagram URL pattern
        instagram_pattern = re.compile(
            r'^https?://(www\.)?instagram\.com/[a-zA-Z0-9._]+/?$',
            re.IGNORECASE
        )
        
        if not instagram_pattern.match(instagram):
            raise ValidationError(
                _('Please enter a valid Instagram profile URL.'),
                code='invalid_instagram_url'
            )
            
        return instagram

    def clean_facebook(self):
        """
        Validate Facebook URL format.
        """
        facebook = self.cleaned_data.get('facebook')
        if not facebook:
            return facebook
            
        import re
        
        # Allow various Facebook URL formats
        if not facebook.startswith('http'):
            if '/' not in facebook:
                facebook = 'https://www.facebook.com/' + facebook
            else:
                facebook = 'https://' + facebook
                
        # Validate Facebook URL pattern
        facebook_pattern = re.compile(
            r'^https?://(www\.)?facebook\.com/[a-zA-Z0-9.]+/?$',
            re.IGNORECASE
        )
        
        if not facebook_pattern.match(facebook):
            raise ValidationError(
                _('Please enter a valid Facebook page URL.'),
                code='invalid_facebook_url'
            )
            
        return facebook

    def clean_ein(self):
        """
        Validate EIN (Employer Identification Number) format.
        """
        ein = self.cleaned_data.get('ein')
        if not ein:
            return ein
            
        import re
        
        # Remove any formatting
        ein_digits = ''.join(filter(str.isdigit, ein))
        
        # EIN should be exactly 9 digits
        if len(ein_digits) != 9:
            raise ValidationError(
                _('EIN must be exactly 9 digits (format: XX-XXXXXXX).'),
                code='invalid_ein_length'
            )
            
        # Format as XX-XXXXXXX
        formatted_ein = f"{ein_digits[:2]}-{ein_digits[2:]}"
        
        return formatted_ein

    def clean_zip_code(self):
        """
        Validate ZIP code format (US).
        """
        zip_code = self.cleaned_data.get('zip_code')
        if not zip_code:
            return zip_code
            
        import re
        
        # Remove any spaces or dashes
        clean_zip = ''.join(filter(str.isdigit, zip_code))
        
        # US ZIP code validation (5 digits or 5+4 digits)
        if len(clean_zip) == 5:
            return clean_zip
        elif len(clean_zip) == 9:
            # Format as XXXXX-XXXX
            return f"{clean_zip[:5]}-{clean_zip[5:]}"
        else:
            raise ValidationError(
                _('Please enter a valid ZIP code (5 digits or 5+4 format).'),
                code='invalid_zip_format'
            )

    def clean_legal_name(self):
        """
        Validate legal business name.
        """
        legal_name = self.cleaned_data.get('legal_name')
        if not legal_name:
            return legal_name
            
        # Trim whitespace
        legal_name = legal_name.strip()
        
        # Check minimum length
        if len(legal_name) < 2:
            raise ValidationError(
                _('Business name must be at least 2 characters long.'),
                code='name_too_short'
            )
            
        # Check for basic business name patterns
        import re
        if not re.match(r'^[a-zA-Z0-9\s\-\.\,\&\'\"]+$', legal_name):
            raise ValidationError(
                _('Business name contains invalid characters.'),
                code='invalid_name_chars'
            )
            
        return legal_name

    def clean_display_name(self):
        """
        Validate display name (DBA name).
        """
        display_name = self.cleaned_data.get('display_name')
        if not display_name:
            return display_name
            
        # Trim whitespace
        display_name = display_name.strip()
        
        # Check minimum length if provided
        if len(display_name) < 2:
            raise ValidationError(
                _('Display name must be at least 2 characters long.'),
                code='name_too_short'
            )
            
        # Check for valid characters
        import re
        if not re.match(r'^[a-zA-Z0-9\s\-\.\,\&\'\"]+$', display_name):
            raise ValidationError(
                _('Display name contains invalid characters.'),
                code='invalid_name_chars'
            )
            
        return display_name

    def clean_contact_name(self):
        """
        Validate contact person name.
        """
        contact_name = self.cleaned_data.get('contact_name')
        if not contact_name:
            return contact_name
            
        # Trim whitespace
        contact_name = contact_name.strip()
        
        # Check minimum length
        if len(contact_name) < 2:
            raise ValidationError(
                _('Contact name must be at least 2 characters long.'),
                code='name_too_short'
            )
        
        # Check maximum length
        if len(contact_name) > 100:
            raise ValidationError(
                _('Contact name must be less than 100 characters.'),
                code='name_too_long'
            )
            
        # Check for valid name characters (letters, spaces, hyphens, apostrophes)
        import re
        if not re.match(r'^[a-zA-Z\s\-\.\'\,]+$', contact_name):
            raise ValidationError(
                _('Contact name contains invalid characters. Use only letters, spaces, hyphens, and apostrophes.'),
                code='invalid_name_chars'
            )
            
        return contact_name

    def clean_county(self):
        """
        Validate county name.
        """
        county = self.cleaned_data.get('county')
        if not county:
            return county
            
        # Trim whitespace
        county = county.strip()
        
        # Check minimum length
        if len(county) < 2:
            raise ValidationError(
                _('County name must be at least 2 characters long.'),
                code='name_too_short'
            )
            
        # Check for valid county name characters
        import re
        if not re.match(r'^[a-zA-Z\s\-\.\']+$', county):
            raise ValidationError(
                _('County name contains invalid characters.'),
                code='invalid_county_chars'
            )
            
        return county

    def clean_address(self):
        """
        Validate street address.
        """
        address = self.cleaned_data.get('address')
        if not address:
            return address
            
        # Trim whitespace
        address = address.strip()
        
        # Check minimum length
        if len(address) < 5:
            raise ValidationError(
                _('Address must be at least 5 characters long.'),
                code='address_too_short'
            )
            
        # Check for valid address characters
        import re
        if not re.match(r'^[a-zA-Z0-9\s\-\.\,\#\/]+$', address):
            raise ValidationError(
                _('Address contains invalid characters.'),
                code='invalid_address_chars'
            )
            
        return address

    def clean_city(self):
        """
        Validate city name.
        """
        city = self.cleaned_data.get('city')
        if not city:
            return city
            
        # Trim whitespace
        city = city.strip()
        
        # Check minimum length
        if len(city) < 2:
            raise ValidationError(
                _('City name must be at least 2 characters long.'),
                code='city_too_short'
            )
            
        # Check for valid city name characters
        import re
        if not re.match(r'^[a-zA-Z\s\-\.\']+$', city):
            raise ValidationError(
                _('City name contains invalid characters.'),
                code='invalid_city_chars'
            )
            
        return city

    def clean_description(self):
        """
        Validate business description.
        """
        description = self.cleaned_data.get('description')
        if not description:
            return description
            
        # Trim whitespace
        description = description.strip()
        
        # Check length constraints
        if len(description) > 500:
            raise ValidationError(
                _('Description must be less than 500 characters.'),
                code='description_too_long'
            )
            
        if len(description) < 10:
            raise ValidationError(
                _('Description should be at least 10 characters for meaningful content.'),
                code='description_too_short'
            )
            
        return description

    def save(self, commit: bool = True) -> ServiceProviderProfile:
        """
        Persist profile updates, processing business logo when needed.

        Returns:
            ServiceProviderProfile: The saved profile instance.
        """
        profile = super().save(commit=False)
        logo = self.cleaned_data.get('logo')

        # Handle logo upload - save directly without complex processing to avoid transaction issues
        if logo and hasattr(logo, 'content_type'):
            # For now, save the image directly to avoid transaction conflicts
            # Image processing can be added later as a separate background task
            profile.logo = logo

        if commit:
            profile.save()

        return profile




