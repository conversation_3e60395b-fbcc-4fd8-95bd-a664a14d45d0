# --- Django Imports ---
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.sessions.models import Session
from datetime import timedelta
import logging

# --- Local App Imports ---
from accounts_app.models import UserSession, SessionSecurityEvent

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Management command to clean up expired sessions and security events.
    
    This command should be run regularly (e.g., daily via cron) to:
    - Remove expired UserSession records
    - Clean up old Django sessions
    - Archive old security events
    - Maintain database performance
    """
    
    help = 'Clean up expired sessions and old security events'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to keep security events (default: 30)',
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days_to_keep = options['days']
        verbose = options['verbose']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will be deleted')
            )
        
        # Clean up expired UserSessions
        expired_sessions_count = self.cleanup_expired_user_sessions(dry_run, verbose)
        
        # Clean up expired Django sessions
        django_sessions_count = self.cleanup_expired_django_sessions(dry_run, verbose)
        
        # Clean up old security events
        old_events_count = self.cleanup_old_security_events(
            days_to_keep, dry_run, verbose
        )
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCleanup Summary:\n'
                f'  - Expired UserSessions: {expired_sessions_count}\n'
                f'  - Expired Django Sessions: {django_sessions_count}\n'
                f'  - Old Security Events: {old_events_count}\n'
            )
        )
        
        if not dry_run:
            logger.info(
                "Session cleanup completed",
                extra={
                    'expired_user_sessions': expired_sessions_count,
                    'expired_django_sessions': django_sessions_count,
                    'old_security_events': old_events_count,
                    'event': 'session_cleanup_completed'
                }
            )
    
    def cleanup_expired_user_sessions(self, dry_run=False, verbose=False):
        """Clean up expired UserSession records."""
        if verbose:
            self.stdout.write('Cleaning up expired UserSessions...')
        
        # Find expired sessions
        now = timezone.now()
        expired_sessions = UserSession.objects.filter(
            is_active=True,
            expires_at__lt=now
        )
        
        count = expired_sessions.count()
        
        if verbose and count > 0:
            self.stdout.write(f'Found {count} expired UserSessions')
            
            # Show some examples
            for session in expired_sessions[:5]:
                self.stdout.write(
                    f'  - User: {session.user.email}, '
                    f'Expired: {session.expires_at}, '
                    f'Device: {session.device_name}'
                )
            
            if count > 5:
                self.stdout.write(f'  ... and {count - 5} more')
        
        if not dry_run and count > 0:
            # Mark sessions as terminated instead of deleting
            expired_sessions.update(
                is_active=False,
                terminated_at=now,
                termination_reason='expired'
            )
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS(f'Marked {count} sessions as expired')
                )
        
        return count
    
    def cleanup_expired_django_sessions(self, dry_run=False, verbose=False):
        """Clean up expired Django session records."""
        if verbose:
            self.stdout.write('Cleaning up expired Django sessions...')
        
        # Find expired Django sessions
        now = timezone.now()
        expired_sessions = Session.objects.filter(expire_date__lt=now)
        
        count = expired_sessions.count()
        
        if verbose and count > 0:
            self.stdout.write(f'Found {count} expired Django sessions')
        
        if not dry_run and count > 0:
            expired_sessions.delete()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {count} expired Django sessions')
                )
        
        return count
    
    def cleanup_old_security_events(self, days_to_keep=30, dry_run=False, verbose=False):
        """Clean up old security events."""
        if verbose:
            self.stdout.write(f'Cleaning up security events older than {days_to_keep} days...')
        
        # Find old security events
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        old_events = SessionSecurityEvent.objects.filter(
            created_at__lt=cutoff_date
        )
        
        count = old_events.count()
        
        if verbose and count > 0:
            self.stdout.write(f'Found {count} old security events')
            
            # Show event type breakdown
            event_types = old_events.values('event_type').distinct()
            for event_type in event_types[:10]:
                type_count = old_events.filter(
                    event_type=event_type['event_type']
                ).count()
                self.stdout.write(
                    f'  - {event_type["event_type"]}: {type_count}'
                )
        
        if not dry_run and count > 0:
            old_events.delete()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {count} old security events')
                )
        
        return count
    
    def cleanup_inactive_user_sessions(self, days_inactive=90, dry_run=False, verbose=False):
        """Clean up very old inactive sessions."""
        if verbose:
            self.stdout.write(f'Cleaning up sessions inactive for {days_inactive} days...')
        
        # Find very old inactive sessions
        cutoff_date = timezone.now() - timedelta(days=days_inactive)
        old_sessions = UserSession.objects.filter(
            is_active=False,
            terminated_at__lt=cutoff_date
        )
        
        count = old_sessions.count()
        
        if verbose and count > 0:
            self.stdout.write(f'Found {count} very old inactive sessions')
        
        if not dry_run and count > 0:
            old_sessions.delete()
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {count} old inactive sessions')
                )
        
        return count
    
    def get_cleanup_stats(self):
        """Get statistics about what needs cleanup."""
        now = timezone.now()
        
        stats = {
            'expired_user_sessions': UserSession.objects.filter(
                is_active=True,
                expires_at__lt=now
            ).count(),
            'expired_django_sessions': Session.objects.filter(
                expire_date__lt=now
            ).count(),
            'total_user_sessions': UserSession.objects.count(),
            'active_user_sessions': UserSession.objects.filter(
                is_active=True
            ).count(),
            'total_security_events': SessionSecurityEvent.objects.count(),
        }
        
        # Add security event breakdown
        event_types = SessionSecurityEvent.objects.values(
            'event_type'
        ).distinct()[:10]
        
        for event_type in event_types:
            type_count = SessionSecurityEvent.objects.filter(
                event_type=event_type['event_type']
            ).count()
            stats[f'events_{event_type["event_type"]}'] = type_count
        
        return stats
