# --- Standard Library Imports ---
import re
import logging
from typing import Any, Dict, List, Optional

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings

# --- Local App Imports ---
from utils.sanitization import (
    sanitize_text,
    sanitize_html,
    sanitize_email,
    sanitize_phone,
    detect_sql_injection,
    detect_xss_attempt,
    sanitize_search_query,
)

logger = logging.getLogger(__name__)


class SecurityValidationMixin:
    """
    Mixin for forms to add comprehensive security validation.
    
    Features:
    - XSS protection
    - SQL injection detection
    - Input sanitization
    - Length validation
    - Pattern validation
    """
    
    # Security configuration
    security_config = {
        'enable_xss_protection': True,
        'enable_sql_injection_detection': True,
        'enable_input_sanitization': True,
        'log_security_violations': True,
        'strict_mode': False,  # If True, rejects suspicious input instead of sanitizing
    }
    
    # Fields that should be sanitized as HTML
    html_fields = []
    
    # Fields that should be sanitized as plain text
    text_fields = []
    
    # Fields that should be validated for email format
    email_fields = ['email']
    
    # Fields that should be validated for phone format
    phone_fields = ['phone', 'phone_number']
    
    # Fields that should be checked for search query patterns
    search_fields = ['search', 'query', 'q']
    
    def clean(self):
        """Enhanced clean method with security validation."""
        cleaned_data = super().clean()
        
        if not cleaned_data:
            return cleaned_data
        
        # Apply security validation to all fields
        for field_name, value in cleaned_data.items():
            if value is None or value == '':
                continue
            
            try:
                # Apply appropriate sanitization based on field type
                cleaned_value = self._apply_security_validation(field_name, value)
                cleaned_data[field_name] = cleaned_value
                
            except ValidationError as e:
                self.add_error(field_name, e)
            except Exception as e:
                logger.error(f"Security validation error for field {field_name}: {str(e)}")
                if self.security_config.get('strict_mode', False):
                    self.add_error(field_name, _('Invalid input detected'))
        
        return cleaned_data
    
    def _apply_security_validation(self, field_name: str, value: Any) -> Any:
        """Apply security validation to a field value."""
        if not isinstance(value, str):
            return value
        
        # Log original value for security monitoring
        if self.security_config.get('log_security_violations', True):
            self._log_security_check(field_name, value)
        
        # Check for SQL injection
        if self.security_config.get('enable_sql_injection_detection', True):
            if detect_sql_injection(value):
                logger.warning(f"SQL injection attempt detected in field {field_name}: {value}")
                if self.security_config.get('strict_mode', False):
                    raise ValidationError(_('Suspicious input detected'))
        
        # Check for XSS
        if self.security_config.get('enable_xss_protection', True):
            if detect_xss_attempt(value):
                logger.warning(f"XSS attempt detected in field {field_name}: {value}")
                if self.security_config.get('strict_mode', False):
                    raise ValidationError(_('Suspicious input detected'))
        
        # Apply appropriate sanitization
        if self.security_config.get('enable_input_sanitization', True):
            return self._sanitize_field_value(field_name, value)
        
        return value
    
    def _sanitize_field_value(self, field_name: str, value: str) -> str:
        """Sanitize field value based on field type."""
        # HTML fields
        if field_name in self.html_fields:
            return sanitize_html(value, strict=self.security_config.get('strict_mode', False))
        
        # Email fields
        elif field_name in self.email_fields:
            try:
                return sanitize_email(value)
            except ValueError as e:
                raise ValidationError(str(e))
        
        # Phone fields
        elif field_name in self.phone_fields:
            return sanitize_phone(value)
        
        # Search fields
        elif field_name in self.search_fields:
            return sanitize_search_query(value)
        
        # Text fields or default
        else:
            # Get max length from field if available
            max_length = None
            if hasattr(self, 'fields') and field_name in self.fields:
                field = self.fields[field_name]
                if hasattr(field, 'max_length'):
                    max_length = field.max_length
            
            return sanitize_text(value, max_length=max_length)
    
    def _log_security_check(self, field_name: str, value: str):
        """Log security check for monitoring."""
        # Only log if value contains suspicious patterns
        if detect_sql_injection(value) or detect_xss_attempt(value):
            logger.warning(
                f"Security validation triggered for field {field_name} "
                f"in form {self.__class__.__name__}: {value[:100]}..."
            )


class LengthValidationMixin:
    """
    Mixin for forms to add comprehensive length validation.
    """
    
    # Field length limits
    field_length_limits = {
        'email': 254,
        'username': 150,
        'first_name': 30,
        'last_name': 30,
        'phone': 20,
        'address': 255,
        'city': 100,
        'state': 100,
        'country': 100,
        'postal_code': 20,
        'company_name': 100,
        'title': 200,
        'description': 2000,
        'bio': 1000,
        'website': 200,
    }
    
    def clean(self):
        """Enhanced clean method with length validation."""
        cleaned_data = super().clean()
        
        if not cleaned_data:
            return cleaned_data
        
        # Apply length validation
        for field_name, value in cleaned_data.items():
            if isinstance(value, str) and value:
                max_length = self.field_length_limits.get(field_name)
                if max_length and len(value) > max_length:
                    self.add_error(
                        field_name,
                        _(f'This field cannot be longer than {max_length} characters.')
                    )
        
        return cleaned_data


class PatternValidationMixin:
    """
    Mixin for forms to add pattern-based validation.
    """
    
    # Field validation patterns
    field_patterns = {
        'username': {
            'pattern': r'^[a-zA-Z0-9_.-]+$',
            'message': _('Username can only contain letters, numbers, dots, hyphens, and underscores.')
        },
        'phone': {
            'pattern': r'^\+?[1-9]\d{1,14}$',
            'message': _('Please enter a valid phone number.')
        },
        'postal_code': {
            'pattern': r'^[A-Za-z0-9\s-]{3,10}$',
            'message': _('Please enter a valid postal code.')
        },
        'website': {
            'pattern': r'^https?://[^\s/$.?#].[^\s]*$',
            'message': _('Please enter a valid website URL starting with http:// or https://.')
        },
    }
    
    def clean(self):
        """Enhanced clean method with pattern validation."""
        cleaned_data = super().clean()
        
        if not cleaned_data:
            return cleaned_data
        
        # Apply pattern validation
        for field_name, value in cleaned_data.items():
            if isinstance(value, str) and value:
                pattern_config = self.field_patterns.get(field_name)
                if pattern_config:
                    pattern = pattern_config['pattern']
                    message = pattern_config['message']
                    
                    if not re.match(pattern, value):
                        self.add_error(field_name, message)
        
        return cleaned_data


class ComprehensiveValidationMixin(SecurityValidationMixin, LengthValidationMixin, PatternValidationMixin):
    """
    Comprehensive validation mixin that combines all validation types.
    
    Usage:
        class MyForm(ComprehensiveValidationMixin, forms.Form):
            # Your form fields here
            pass
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Apply security attributes to form fields
        self._apply_security_attributes()
    
    def _apply_security_attributes(self):
        """Apply security-related attributes to form fields."""
        if not hasattr(self, 'fields'):
            return
        
        for field_name, field in self.fields.items():
            # Add security-related HTML attributes
            if hasattr(field.widget, 'attrs'):
                # Prevent autocomplete for sensitive fields
                if field_name in ['password', 'password1', 'password2', 'current_password']:
                    field.widget.attrs['autocomplete'] = 'new-password'
                
                # Add input validation attributes
                if field_name in self.email_fields:
                    field.widget.attrs['type'] = 'email'
                    field.widget.attrs['pattern'] = r'[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$'
                
                if field_name in self.phone_fields:
                    field.widget.attrs['type'] = 'tel'
                
                # Add maxlength attribute
                max_length = self.field_length_limits.get(field_name)
                if max_length and not field.widget.attrs.get('maxlength'):
                    field.widget.attrs['maxlength'] = max_length
                
                # Add security classes
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f'{current_classes} security-validated'.strip()


class FileUploadValidationMixin:
    """
    Mixin for forms with file upload validation.
    """
    
    # File upload configuration
    file_upload_config = {
        'max_file_size': 10 * 1024 * 1024,  # 10MB default
        'allowed_types': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'scan_for_malware': True,
        'validate_image_content': True,
    }
    
    def clean(self):
        """Enhanced clean method with file upload validation."""
        cleaned_data = super().clean()
        
        if not cleaned_data:
            return cleaned_data
        
        # Validate file uploads
        for field_name, value in cleaned_data.items():
            if hasattr(value, 'read'):  # File-like object
                try:
                    self._validate_file_upload(field_name, value)
                except ValidationError as e:
                    self.add_error(field_name, e)
        
        return cleaned_data
    
    def _validate_file_upload(self, field_name: str, file_obj):
        """Validate uploaded file."""
        from utils.sanitization import validate_file_upload
        
        config = self.file_upload_config
        
        # Perform comprehensive file validation
        validation_result = validate_file_upload(
            file_obj,
            allowed_types=config.get('allowed_types'),
            max_size=config.get('max_file_size')
        )
        
        if not validation_result['valid']:
            error_messages = validation_result['errors']
            raise ValidationError('; '.join(error_messages))
        
        # Log warnings if any
        if validation_result['warnings']:
            logger.warning(
                f"File upload warnings for {field_name}: "
                f"{'; '.join(validation_result['warnings'])}"
            )
