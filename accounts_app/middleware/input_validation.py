# --- Standard Library Imports ---
import logging
import json
from typing import Any, Dict

# --- Django Imports ---
from django.http import JsonResponse, HttpResponseBadRequest
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.core.exceptions import SuspiciousOperation

# --- Local App Imports ---
from utils.sanitization import (
    detect_sql_injection,
    detect_xss_attempt,
    sanitize_text,
)
from ..utils.security_utils import get_client_ip, log_security_event

logger = logging.getLogger(__name__)


class InputValidationMiddleware(MiddlewareMixin):
    """
    Middleware for comprehensive input validation and sanitization.
    
    Features:
    - SQL injection detection
    - XSS attempt detection
    - Input sanitization
    - Request size validation
    - Content type validation
    - Suspicious pattern detection
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Configuration
        self.max_request_size = getattr(settings, 'MAX_REQUEST_SIZE', 10 * 1024 * 1024)  # 10MB
        self.enable_sql_detection = getattr(settings, 'ENABLE_SQL_INJECTION_DETECTION', True)
        self.enable_xss_detection = getattr(settings, 'ENABLE_XSS_DETECTION', True)
        self.enable_input_sanitization = getattr(settings, 'ENABLE_INPUT_SANITIZATION', True)
        self.strict_mode = getattr(settings, 'INPUT_VALIDATION_STRICT_MODE', False)
        
        # Paths to skip validation
        self.skip_paths = getattr(settings, 'INPUT_VALIDATION_SKIP_PATHS', [
            '/admin/',
            '/static/',
            '/media/',
            '/favicon.ico',
        ])
        
        # Suspicious patterns
        self.suspicious_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'vbscript:',
            r'data:text/html',
            r'<iframe[^>]*>.*?</iframe>',
            r'<object[^>]*>.*?</object>',
            r'<embed[^>]*>.*?</embed>',
            r'eval\s*\(',
            r'document\.cookie',
            r'document\.write',
            r'window\.location',
        ]
        
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming request for input validation."""
        # Skip validation for certain paths
        if self._should_skip_validation(request):
            return None
        
        # Validate request size
        if self._is_request_too_large(request):
            return self._handle_large_request(request)
        
        # Validate content type
        if not self._is_valid_content_type(request):
            return self._handle_invalid_content_type(request)
        
        # Validate request data
        validation_result = self._validate_request_data(request)
        if validation_result:
            return validation_result
        
        return None
    
    def _should_skip_validation(self, request):
        """Check if validation should be skipped for this request."""
        path = request.path
        
        # Skip for configured paths
        for skip_path in self.skip_paths:
            if path.startswith(skip_path):
                return True
        
        # Skip for safe HTTP methods
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        return False
    
    def _is_request_too_large(self, request):
        """Check if request size exceeds limits."""
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length:
            try:
                content_length = int(content_length)
                return content_length > self.max_request_size
            except (ValueError, TypeError):
                return False
        return False
    
    def _is_valid_content_type(self, request):
        """Validate request content type."""
        content_type = request.content_type
        
        # Allow common content types
        allowed_types = [
            'application/x-www-form-urlencoded',
            'multipart/form-data',
            'application/json',
            'text/plain',
        ]
        
        # Check if content type is allowed
        for allowed_type in allowed_types:
            if content_type.startswith(allowed_type):
                return True
        
        # Log suspicious content type
        if content_type:
            logger.warning(f"Suspicious content type: {content_type} from {get_client_ip(request)}")
        
        return False
    
    def _validate_request_data(self, request):
        """Validate request data for security threats."""
        violations = []
        
        # Validate POST data
        if hasattr(request, 'POST') and request.POST:
            post_violations = self._validate_data_dict(request.POST, 'POST')
            violations.extend(post_violations)
        
        # Validate GET parameters
        if hasattr(request, 'GET') and request.GET:
            get_violations = self._validate_data_dict(request.GET, 'GET')
            violations.extend(get_violations)
        
        # Validate JSON data
        if request.content_type == 'application/json':
            json_violations = self._validate_json_data(request)
            violations.extend(json_violations)
        
        # Handle violations
        if violations:
            return self._handle_security_violations(request, violations)
        
        return None
    
    def _validate_data_dict(self, data_dict, data_type):
        """Validate a dictionary of data."""
        violations = []
        
        for key, values in data_dict.items():
            # Handle multiple values
            if isinstance(values, list):
                for value in values:
                    violation = self._validate_single_value(key, value, data_type)
                    if violation:
                        violations.append(violation)
            else:
                violation = self._validate_single_value(key, values, data_type)
                if violation:
                    violations.append(violation)
        
        return violations
    
    def _validate_single_value(self, key, value, data_type):
        """Validate a single value."""
        if not isinstance(value, str) or not value:
            return None
        
        violation = {
            'key': key,
            'value': value[:100],  # Truncate for logging
            'data_type': data_type,
            'violations': []
        }
        
        # Check for SQL injection
        if self.enable_sql_detection and detect_sql_injection(value):
            violation['violations'].append('sql_injection')
        
        # Check for XSS
        if self.enable_xss_detection and detect_xss_attempt(value):
            violation['violations'].append('xss_attempt')
        
        # Check for suspicious patterns
        for pattern in self.suspicious_patterns:
            import re
            if re.search(pattern, value, re.IGNORECASE):
                violation['violations'].append('suspicious_pattern')
                break
        
        # Return violation if any found
        if violation['violations']:
            return violation
        
        return None
    
    def _validate_json_data(self, request):
        """Validate JSON request data."""
        violations = []
        
        try:
            if hasattr(request, 'body') and request.body:
                json_data = json.loads(request.body)
                json_violations = self._validate_json_object(json_data)
                violations.extend(json_violations)
        except json.JSONDecodeError:
            # Invalid JSON - let Django handle it
            pass
        except Exception as e:
            logger.error(f"Error validating JSON data: {str(e)}")
        
        return violations
    
    def _validate_json_object(self, obj, path=""):
        """Recursively validate JSON object."""
        violations = []
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # Validate key
                if isinstance(key, str):
                    violation = self._validate_single_value(current_path, key, 'JSON_KEY')
                    if violation:
                        violations.append(violation)
                
                # Validate value recursively
                if isinstance(value, (dict, list)):
                    violations.extend(self._validate_json_object(value, current_path))
                elif isinstance(value, str):
                    violation = self._validate_single_value(current_path, value, 'JSON_VALUE')
                    if violation:
                        violations.append(violation)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                violations.extend(self._validate_json_object(item, current_path))
        
        return violations
    
    def _handle_large_request(self, request):
        """Handle requests that are too large."""
        log_security_event(
            'request_too_large',
            request=request,
            details={
                'content_length': request.META.get('CONTENT_LENGTH'),
                'max_allowed': self.max_request_size,
            }
        )
        
        return HttpResponseBadRequest("Request too large")
    
    def _handle_invalid_content_type(self, request):
        """Handle requests with invalid content types."""
        log_security_event(
            'invalid_content_type',
            request=request,
            details={
                'content_type': request.content_type,
            }
        )
        
        if self.strict_mode:
            return HttpResponseBadRequest("Invalid content type")
        
        return None
    
    def _handle_security_violations(self, request, violations):
        """Handle security violations."""
        # Log all violations
        for violation in violations:
            log_security_event(
                'input_validation_violation',
                request=request,
                details=violation
            )
        
        # In strict mode, reject the request
        if self.strict_mode:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'security_violation',
                    'message': 'Request contains suspicious content',
                    'violations': len(violations),
                }, status=400)
            else:
                raise SuspiciousOperation("Request contains suspicious content")
        
        # In non-strict mode, sanitize the input
        if self.enable_input_sanitization:
            self._sanitize_request_data(request, violations)
        
        return None
    
    def _sanitize_request_data(self, request, violations):
        """Sanitize request data based on violations."""
        # This is a simplified sanitization - in practice, you'd need
        # to modify the request data structures carefully
        logger.info(f"Sanitizing request data for {len(violations)} violations")
        
        # Note: Modifying request.POST and request.GET is complex
        # This is mainly for logging purposes
        for violation in violations:
            logger.info(f"Sanitized field {violation['key']} due to {violation['violations']}")
