# --- Django Imports ---
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core import mail
from django.core.cache import cache
from django.utils import timezone
from unittest.mock import patch, Mock

# --- Third-Party Imports ---
from allauth.account.models import EmailAddress, EmailConfirmation
from allauth.socialaccount.models import SocialAccount, SocialApp

# --- Local App Imports ---
from ..models import UserSession, SessionSecurityEvent
from ..utils.security_utils import check_password_strength, validate_email_security

User = get_user_model()


class EnhancedEmailVerificationTestCase(TestCase):
    """Test enhanced email verification functionality."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
    def test_email_verification_status_view(self):
        """Test email verification status page."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.get(reverse('accounts_app:email_verification_status'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Email Verification')
        
    def test_resend_verification_email(self):
        """Test resending verification email."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Clear any existing emails
        mail.outbox = []
        
        response = self.client.post(reverse('accounts_app:resend_verification_email'))
        
        # Should redirect or return success
        self.assertIn(response.status_code, [200, 302])
        
        # Should send email
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn('verification', mail.outbox[0].subject.lower())
        
    def test_resend_verification_rate_limiting(self):
        """Test rate limiting for verification email resend."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # First request should succeed
        response1 = self.client.post(reverse('accounts_app:resend_verification_email'))
        self.assertIn(response1.status_code, [200, 302])
        
        # Immediate second request should be rate limited
        response2 = self.client.post(reverse('accounts_app:resend_verification_email'))
        
        # Should contain rate limit message
        if response2.status_code == 200:
            content = response2.content.decode()
            self.assertIn('wait', content.lower())
        
    def test_email_verification_success_page(self):
        """Test email verification success page."""
        response = self.client.get(reverse('accounts_app:email_verification_success'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Email Verified Successfully')
        
    def test_email_verification_error_page(self):
        """Test email verification error page."""
        response = self.client.get(
            reverse('accounts_app:email_verification_error') + '?error=expired'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Verification Link Expired')


class AccountRecoveryTestCase(TestCase):
    """Test account recovery functionality."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
    def test_account_recovery_form(self):
        """Test account recovery form display."""
        response = self.client.get(reverse('accounts_app:account_recovery'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Account Recovery')
        
    def test_account_recovery_submission(self):
        """Test account recovery form submission."""
        mail.outbox = []
        
        response = self.client.post(reverse('accounts_app:account_recovery'), {
            'email': '<EMAIL>',
            'recovery_reason': 'forgot_password',
            'additional_info': 'I forgot my password and cannot access my email.',
            'agree_to_verification': True,
        })
        
        # Should redirect to success page
        self.assertEqual(response.status_code, 302)
        
        # Should send emails (to user and support)
        self.assertGreaterEqual(len(mail.outbox), 1)
        
    def test_enhanced_password_reset(self):
        """Test enhanced password reset functionality."""
        mail.outbox = []
        
        response = self.client.post(reverse('account_reset_password'), {
            'email': '<EMAIL>'
        })
        
        # Should redirect to done page
        self.assertEqual(response.status_code, 302)
        
        # Should send password reset email
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn('password', mail.outbox[0].subject.lower())


class SessionManagementTestCase(TestCase):
    """Test session management functionality."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
    def test_session_management_view(self):
        """Test session management page."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.get(reverse('accounts_app:session_management'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Session Management')
        
    def test_session_creation(self):
        """Test that sessions are created properly."""
        # Login should create a session
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Check that UserSession was created
        sessions = UserSession.objects.filter(user=self.user)
        self.assertGreater(sessions.count(), 0)
        
        session = sessions.first()
        self.assertTrue(session.is_active)
        self.assertIsNotNone(session.expires_at)
        
    def test_session_extension(self):
        """Test session extension functionality."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.post(reverse('accounts_app:extend_session'), {
            'hours': '2'
        })
        
        # Should return success JSON
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success', False))
        
    def test_session_termination(self):
        """Test session termination."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Get the session
        session = UserSession.objects.filter(user=self.user, is_active=True).first()
        self.assertIsNotNone(session)
        
        # Terminate it
        response = self.client.post(reverse('accounts_app:terminate_session'), {
            'session_id': str(session.session_id)
        })
        
        # Should return success
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success', False))
        
        # Session should be terminated
        session.refresh_from_db()
        self.assertFalse(session.is_active)


class SocialAccountManagementTestCase(TestCase):
    """Test social account management functionality."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        # Create a social app for testing
        self.social_app = SocialApp.objects.create(
            provider='google',
            name='Google',
            client_id='test_client_id',
            secret='test_secret'
        )
        
    def test_social_account_management_view(self):
        """Test social account management page."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.get(reverse('accounts_app:social_account_management'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Social Account Management')
        
    def test_social_account_unlinking(self):
        """Test unlinking social accounts."""
        # Create a social account
        social_account = SocialAccount.objects.create(
            user=self.user,
            provider='google',
            uid='*********',
            extra_data={'email': '<EMAIL>', 'name': 'Test User'}
        )
        
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.post(reverse('accounts_app:unlink_social_account'), {
            'account_id': social_account.id
        })
        
        # Should return success
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success', False))
        
        # Social account should be deleted
        self.assertFalse(
            SocialAccount.objects.filter(id=social_account.id).exists()
        )
        
    def test_social_account_data_sync(self):
        """Test syncing data from social accounts."""
        # Create a social account with data
        social_account = SocialAccount.objects.create(
            user=self.user,
            provider='google',
            uid='*********',
            extra_data={
                'email': '<EMAIL>',
                'given_name': 'John',
                'family_name': 'Doe',
                'picture': 'https://example.com/avatar.jpg'
            }
        )
        
        self.client.login(email='<EMAIL>', password='testpass123')
        
        response = self.client.post(reverse('accounts_app:sync_social_account_data'), {
            'account_id': social_account.id,
            'sync_fields': ['first_name', 'last_name']
        })
        
        # Should return success
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success', False))
        
        # User data should be updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'John')
        self.assertEqual(self.user.last_name, 'Doe')


class SecurityUtilsTestCase(TestCase):
    """Test security utility functions."""
    
    def test_password_strength_checker(self):
        """Test password strength checking."""
        # Weak password
        result = check_password_strength('123')
        self.assertFalse(result['is_strong'])
        self.assertGreater(len(result['feedback']), 0)
        
        # Strong password
        result = check_password_strength('MyStr0ng!Password123')
        self.assertTrue(result['is_strong'])
        self.assertGreaterEqual(result['score'], 70)
        
    def test_email_security_validation(self):
        """Test email security validation."""
        # Valid email
        result = validate_email_security('<EMAIL>')
        self.assertTrue(result['is_valid'])
        self.assertFalse(result['is_disposable'])
        
        # Disposable email
        result = validate_email_security('<EMAIL>')
        self.assertTrue(result['is_valid'])
        self.assertTrue(result['is_disposable'])
        
        # Invalid email
        result = validate_email_security('invalid-email')
        self.assertFalse(result['is_valid'])


class SecurityMiddlewareTestCase(TestCase):
    """Test security middleware functionality."""
    
    def setUp(self):
        self.client = Client()
        
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        # Clear cache
        cache.clear()
        
        # Make multiple requests to trigger rate limiting
        login_url = reverse('account_login')
        
        for i in range(6):  # Exceed the limit of 5
            response = self.client.post(login_url, {
                'email': '<EMAIL>',
                'password': 'wrongpassword'
            })
        
        # Last request should be rate limited
        # Note: This test might need adjustment based on actual rate limiting implementation
        
    def test_security_headers(self):
        """Test that security headers are added."""
        response = self.client.get(reverse('account_login'))
        
        # Check for security headers
        self.assertIn('X-Content-Type-Options', response)
        self.assertIn('X-Frame-Options', response)
        self.assertIn('X-XSS-Protection', response)
        
    def test_csrf_enhancement(self):
        """Test enhanced CSRF protection."""
        # This would test the enhanced CSRF middleware
        # Implementation depends on specific requirements
        pass


class IntegrationTestCase(TestCase):
    """Integration tests for enhanced authentication flows."""
    
    def setUp(self):
        self.client = Client()
        
    def test_complete_signup_flow(self):
        """Test complete signup and verification flow."""
        mail.outbox = []
        
        # 1. Sign up
        response = self.client.post(reverse('accounts_app:customer_signup'), {
            'email': '<EMAIL>',
            'password1': 'MyStr0ng!Password123',
            'password2': 'MyStr0ng!Password123',
            'first_name': 'John',
            'last_name': 'Doe',
        })
        
        # Should redirect after successful signup
        self.assertEqual(response.status_code, 302)
        
        # User should be created
        user = User.objects.get(email='<EMAIL>')
        self.assertIsNotNone(user)
        
        # Verification email should be sent
        self.assertGreater(len(mail.outbox), 0)
        
        # 2. Check verification status
        self.client.login(email='<EMAIL>', password='MyStr0ng!Password123')
        response = self.client.get(reverse('accounts_app:email_verification_status'))
        self.assertEqual(response.status_code, 200)
        
        # 3. Resend verification email
        mail.outbox = []
        response = self.client.post(reverse('accounts_app:resend_verification_email'))
        self.assertIn(response.status_code, [200, 302])
        self.assertGreater(len(mail.outbox), 0)
        
    def test_complete_password_reset_flow(self):
        """Test complete password reset flow."""
        # Create user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            role='customer'
        )
        
        mail.outbox = []
        
        # 1. Request password reset
        response = self.client.post(reverse('account_reset_password'), {
            'email': '<EMAIL>'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertGreater(len(mail.outbox), 0)
        
        # 2. Check that security notification was sent
        # (This would require checking for multiple emails)
        
    def test_session_security_flow(self):
        """Test session security features."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        # 1. Login and create session
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # 2. Check session was created
        sessions = UserSession.objects.filter(user=user, is_active=True)
        self.assertGreater(sessions.count(), 0)
        
        # 3. Access session management
        response = self.client.get(reverse('accounts_app:session_management'))
        self.assertEqual(response.status_code, 200)
        
        # 4. Extend session
        response = self.client.post(reverse('accounts_app:extend_session'))
        self.assertEqual(response.status_code, 200)
        
        # 5. Check security events were logged
        events = SessionSecurityEvent.objects.filter(user=user)
        self.assertGreater(events.count(), 0)
