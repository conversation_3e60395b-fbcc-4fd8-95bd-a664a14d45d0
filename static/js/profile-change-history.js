/**
 * Profile Change History Component
 * 
 * Provides comprehensive profile change tracking with detailed audit logs,
 * change comparison views, rollback functionality, and change notifications.
 */

class ProfileChangeHistory {
    constructor(options = {}) {
        this.options = {
            containerSelector: '#change-history',
            historyListSelector: '#change-history-list',
            statisticsSelector: '#change-statistics',
            comparisonModalSelector: '#change-comparison-modal',
            rollbackModalSelector: '#rollback-modal',
            pageSize: 20,
            autoRefresh: false,
            refreshInterval: 60000, // 1 minute
            ...options
        };
        
        this.container = document.querySelector(this.options.containerSelector);
        this.historyList = document.querySelector(this.options.historyListSelector);
        this.statisticsContainer = document.querySelector(this.options.statisticsSelector);
        
        this.currentPage = 0;
        this.totalChanges = 0;
        this.hasMore = true;
        this.filters = {};
        this.selectedChanges = new Set();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadChangeHistory();
        this.loadStatistics();
        
        if (this.options.autoRefresh) {
            this.startAutoRefresh();
        }
    }
    
    setupEventListeners() {
        // Filter controls
        const filterForm = document.getElementById('change-history-filters');
        if (filterForm) {
            filterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.applyFilters();
            });
        }
        
        // Load more button
        const loadMoreBtn = document.getElementById('load-more-changes');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => this.loadMoreChanges());
        }
        
        // Bulk actions
        const bulkActionsBtn = document.getElementById('bulk-actions-btn');
        if (bulkActionsBtn) {
            bulkActionsBtn.addEventListener('click', () => this.showBulkActions());
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-history');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshHistory());
        }
    }
    
    async loadChangeHistory(reset = true) {
        try {
            if (reset) {
                this.currentPage = 0;
                this.hasMore = true;
                if (this.historyList) {
                    this.historyList.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
                }
            }
            
            const params = new URLSearchParams({
                limit: this.options.pageSize,
                offset: this.currentPage * this.options.pageSize,
                ...this.filters
            });
            
            const response = await fetch(`/accounts/profile/change-history/?${params}`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderChangeHistory(data.history, reset);
                this.totalChanges = data.history.total;
                this.hasMore = data.history.has_more;
                this.updatePaginationControls();
            } else {
                this.showError('Failed to load change history: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading change history:', error);
            this.showError('Failed to load change history');
        }
    }
    
    renderChangeHistory(historyData, reset = true) {
        if (!this.historyList) return;
        
        if (reset) {
            this.historyList.innerHTML = '';
        }
        
        if (historyData.changes.length === 0) {
            if (reset) {
                this.historyList.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <h5>No Change History</h5>
                        <p>No profile changes have been recorded yet.</p>
                    </div>
                `;
            }
            return;
        }
        
        const changesHtml = historyData.changes.map(change => this.renderChangeItem(change)).join('');
        
        if (reset) {
            this.historyList.innerHTML = changesHtml;
        } else {
            this.historyList.insertAdjacentHTML('beforeend', changesHtml);
        }
    }
    
    renderChangeItem(change) {
        const changeDate = new Date(change.changed_at);
        const relativeTime = this.getRelativeTime(changeDate);
        
        return `
            <div class="change-item card mb-3" data-change-id="${change.id}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-1">
                            <div class="form-check">
                                <input class="form-check-input change-checkbox" type="checkbox" 
                                       value="${change.id}" onchange="profileChangeHistory.toggleChangeSelection(${change.id}, this.checked)">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="change-icon text-center">
                                <i class="${this.getChangeIcon(change.change_type)} fa-2x text-${this.getChangeColor(change.change_type)}"></i>
                                <div class="small text-muted mt-1">${change.change_type_display}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">${change.change_summary}</h6>
                            <div class="change-details small text-muted">
                                <div class="mb-1">
                                    <i class="fas fa-user me-1"></i>
                                    Changed by: ${change.changed_by.name}
                                </div>
                                <div class="mb-1">
                                    <i class="fas fa-clock me-1"></i>
                                    ${relativeTime} (${changeDate.toLocaleString()})
                                </div>
                                ${change.field_count > 0 ? `
                                    <div class="mb-1">
                                        <i class="fas fa-edit me-1"></i>
                                        ${change.field_count} field${change.field_count > 1 ? 's' : ''} changed
                                        ${change.categories.length > 0 ? `(${change.categories.join(', ')})` : ''}
                                    </div>
                                ` : ''}
                                ${change.ip_address ? `
                                    <div class="mb-1">
                                        <i class="fas fa-globe me-1"></i>
                                        IP: ${change.ip_address}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="profileChangeHistory.viewChangeDetails(${change.id})">
                                    <i class="fas fa-eye"></i>
                                    View
                                </button>
                                ${change.can_rollback ? `
                                    <button type="button" class="btn btn-outline-warning btn-sm" 
                                            onclick="profileChangeHistory.showRollbackModal(${change.id})">
                                        <i class="fas fa-undo"></i>
                                        Rollback
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Expandable details -->
                    <div class="change-details-expanded collapse mt-3" id="details-${change.id}">
                        <div class="border-top pt-3">
                            ${this.renderChangeFields(change.changed_fields)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderChangeFields(changedFields) {
        if (!changedFields || Object.keys(changedFields).length === 0) {
            return '<p class="text-muted">No field changes recorded.</p>';
        }
        
        const fieldsHtml = Object.entries(changedFields).map(([fieldName, fieldInfo]) => `
            <div class="field-change mb-2">
                <strong>${this.formatFieldName(fieldName)}:</strong>
                <div class="row">
                    <div class="col-md-6">
                        <div class="old-value">
                            <small class="text-muted">Old:</small>
                            <div class="value-display bg-light p-2 rounded">
                                ${fieldInfo.old || '<em>Empty</em>'}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="new-value">
                            <small class="text-muted">New:</small>
                            <div class="value-display bg-success bg-opacity-10 p-2 rounded">
                                ${fieldInfo.new || '<em>Empty</em>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        return fieldsHtml;
    }
    
    async loadStatistics() {
        try {
            const days = document.getElementById('statistics-days')?.value || 30;
            
            const response = await fetch(`/accounts/profile/change-statistics/?days=${days}`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderStatistics(data.statistics);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }
    
    renderStatistics(statistics) {
        if (!this.statisticsContainer) return;
        
        const statisticsHtml = `
            <div class="statistics-overview">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${statistics.total_changes}</div>
                            <div class="stat-label">Total Changes</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${statistics.days_analyzed}</div>
                            <div class="stat-label">Days Analyzed</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${statistics.average_changes_per_day.toFixed(1)}</div>
                            <div class="stat-label">Avg/Day</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${Object.keys(statistics.change_type_counts).length}</div>
                            <div class="stat-label">Change Types</div>
                        </div>
                    </div>
                </div>
                
                ${Object.keys(statistics.change_type_counts).length > 0 ? `
                    <div class="mt-4">
                        <h6>Change Types</h6>
                        <div class="change-type-breakdown">
                            ${Object.entries(statistics.change_type_counts).map(([type, count]) => `
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-${this.getChangeColor(type)}">${type}</span>
                                    <span>${count} changes</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                ${Object.keys(statistics.category_counts).length > 0 ? `
                    <div class="mt-4">
                        <h6>Categories</h6>
                        <div class="category-breakdown">
                            ${Object.entries(statistics.category_counts).map(([category, count]) => `
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>${category}</span>
                                    <span class="badge bg-secondary">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        this.statisticsContainer.innerHTML = statisticsHtml;
    }
    
    async viewChangeDetails(changeId) {
        // Toggle expanded details
        const detailsElement = document.getElementById(`details-${changeId}`);
        if (detailsElement) {
            const collapse = new bootstrap.Collapse(detailsElement);
            collapse.toggle();
        }
    }
    
    async compareChanges(changeId1, changeId2) {
        try {
            const response = await fetch(`/accounts/profile/compare-changes/?change_id1=${changeId1}&change_id2=${changeId2}`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showComparisonModal(data.comparison);
            } else {
                this.showError('Failed to compare changes: ' + data.error);
            }
        } catch (error) {
            console.error('Error comparing changes:', error);
            this.showError('Failed to compare changes');
        }
    }
    
    showComparisonModal(comparison) {
        // This would show a modal with detailed comparison
        console.log('Comparison data:', comparison);
        alert('Change comparison feature would be implemented here');
    }
    
    showRollbackModal(changeId) {
        const modal = document.getElementById('rollback-modal');
        if (modal) {
            const changeIdInput = modal.querySelector('#rollback-change-id');
            if (changeIdInput) {
                changeIdInput.value = changeId;
            }
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
    
    async rollbackChange(changeId, reason) {
        try {
            const response = await fetch('/accounts/profile/rollback-change/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({
                    change_id: changeId,
                    reason: reason
                }),
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Change rolled back successfully! ${data.fields_rolled_back.length} fields restored.`);
                this.refreshHistory();
            } else {
                this.showError('Failed to rollback change: ' + data.error);
            }
        } catch (error) {
            console.error('Error rolling back change:', error);
            this.showError('Failed to rollback change');
        }
    }
    
    toggleChangeSelection(changeId, selected) {
        if (selected) {
            this.selectedChanges.add(changeId);
        } else {
            this.selectedChanges.delete(changeId);
        }
        
        this.updateBulkActionsButton();
    }
    
    updateBulkActionsButton() {
        const bulkActionsBtn = document.getElementById('bulk-actions-btn');
        if (bulkActionsBtn) {
            bulkActionsBtn.disabled = this.selectedChanges.size === 0;
            bulkActionsBtn.textContent = `Bulk Actions (${this.selectedChanges.size})`;
        }
    }
    
    showBulkActions() {
        if (this.selectedChanges.size === 0) return;
        
        // Show bulk actions menu
        alert(`Bulk actions for ${this.selectedChanges.size} selected changes would be shown here`);
    }
    
    applyFilters() {
        const filterForm = document.getElementById('change-history-filters');
        if (!filterForm) return;
        
        const formData = new FormData(filterForm);
        this.filters = {};
        
        for (const [key, value] of formData.entries()) {
            if (value) {
                this.filters[key] = value;
            }
        }
        
        this.loadChangeHistory(true);
    }
    
    loadMoreChanges() {
        if (!this.hasMore) return;
        
        this.currentPage++;
        this.loadChangeHistory(false);
    }
    
    refreshHistory() {
        this.selectedChanges.clear();
        this.loadChangeHistory(true);
        this.loadStatistics();
    }
    
    updatePaginationControls() {
        const loadMoreBtn = document.getElementById('load-more-changes');
        if (loadMoreBtn) {
            loadMoreBtn.style.display = this.hasMore ? 'block' : 'none';
        }
        
        const totalElement = document.getElementById('total-changes');
        if (totalElement) {
            totalElement.textContent = this.totalChanges;
        }
    }
    
    getChangeIcon(changeType) {
        const icons = {
            'create': 'fas fa-plus-circle',
            'update': 'fas fa-edit',
            'verify': 'fas fa-check-circle',
            'unverify': 'fas fa-times-circle',
            'privacy_change': 'fas fa-shield-alt',
            'picture_upload': 'fas fa-camera',
            'picture_remove': 'fas fa-trash',
            'data_export': 'fas fa-download',
            'deletion_request': 'fas fa-user-times',
            'admin_update': 'fas fa-user-cog',
            'rollback': 'fas fa-undo',
        };
        return icons[changeType] || 'fas fa-edit';
    }
    
    getChangeColor(changeType) {
        const colors = {
            'create': 'success',
            'update': 'primary',
            'verify': 'success',
            'unverify': 'warning',
            'privacy_change': 'info',
            'picture_upload': 'primary',
            'picture_remove': 'danger',
            'data_export': 'secondary',
            'deletion_request': 'danger',
            'admin_update': 'warning',
            'rollback': 'warning',
        };
        return colors[changeType] || 'secondary';
    }
    
    formatFieldName(fieldName) {
        return fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    
    getRelativeTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        
        return date.toLocaleDateString();
    }
    
    startAutoRefresh() {
        setInterval(() => {
            this.refreshHistory();
        }, this.options.refreshInterval);
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#change-history')) {
        window.profileChangeHistory = new ProfileChangeHistory();
    }
});
