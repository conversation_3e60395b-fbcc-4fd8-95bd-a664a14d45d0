/**
 * CSRF Protection Utilities
 * 
 * Provides comprehensive CSRF protection for AJAX requests and forms.
 * Handles token refresh, failure recovery, and progressive enhancement.
 */

class CSRFProtection {
    constructor() {
        this.token = null;
        this.tokenRefreshUrl = '/accounts/csrf-token/';
        this.failureCount = 0;
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
        
        this.init();
    }
    
    init() {
        this.loadToken();
        this.setupAjaxInterceptors();
        this.setupFormHandlers();
        this.setupTokenRefresh();
        this.handleCSRFFailures();
    }
    
    /**
     * Load CSRF token from meta tag or cookie
     */
    loadToken() {
        // Try to get token from meta tag first
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            this.token = metaToken.getAttribute('content');
            return;
        }
        
        // Fallback to cookie
        this.token = this.getCookie('csrftoken');
        
        if (!this.token) {
            console.warn('CSRF token not found. AJAX requests may fail.');
            this.refreshToken();
        }
    }
    
    /**
     * Get cookie value by name
     */
    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    /**
     * Setup AJAX interceptors for different libraries
     */
    setupAjaxInterceptors() {
        // jQuery AJAX setup
        if (window.jQuery) {
            const self = this;
            jQuery.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (self.needsCSRFToken(settings.type, settings.url)) {
                        xhr.setRequestHeader("X-CSRFToken", self.token);
                    }
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 403 && xhr.responseJSON && xhr.responseJSON.error === 'csrf_failed') {
                        self.handleCSRFFailure(xhr, this);
                    }
                }
            });
        }
        
        // Fetch API interceptor
        this.setupFetchInterceptor();
        
        // XMLHttpRequest interceptor
        this.setupXHRInterceptor();
    }
    
    /**
     * Setup fetch API interceptor
     */
    setupFetchInterceptor() {
        const self = this;
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options = {}) {
            if (self.needsCSRFToken(options.method, url)) {
                options.headers = options.headers || {};
                options.headers['X-CSRFToken'] = self.token;
            }
            
            return originalFetch(url, options).then(response => {
                if (response.status === 403) {
                    return response.json().then(data => {
                        if (data.error === 'csrf_failed') {
                            self.handleCSRFFailure(response, { url, options });
                        }
                        throw new Error('CSRF validation failed');
                    });
                }
                return response;
            });
        };
    }
    
    /**
     * Setup XMLHttpRequest interceptor
     */
    setupXHRInterceptor() {
        const self = this;
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            this._method = method;
            this._url = url;
            return originalOpen.call(this, method, url, async, user, password);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            if (self.needsCSRFToken(this._method, this._url)) {
                this.setRequestHeader('X-CSRFToken', self.token);
            }
            
            this.addEventListener('load', function() {
                if (this.status === 403) {
                    try {
                        const response = JSON.parse(this.responseText);
                        if (response.error === 'csrf_failed') {
                            self.handleCSRFFailure(this, { method: this._method, url: this._url });
                        }
                    } catch (e) {
                        // Not JSON response, ignore
                    }
                }
            });
            
            return originalSend.call(this, data);
        };
    }
    
    /**
     * Setup form handlers for CSRF protection
     */
    setupFormHandlers() {
        const self = this;
        
        // Add CSRF token to forms that don't have it
        document.addEventListener('submit', function(event) {
            const form = event.target;
            if (form.tagName === 'FORM' && self.needsCSRFToken(form.method)) {
                self.ensureFormHasCSRFToken(form);
            }
        });
        
        // Handle dynamic form creation
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const forms = node.tagName === 'FORM' ? [node] : node.querySelectorAll('form');
                        forms.forEach(form => self.ensureFormHasCSRFToken(form));
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
    /**
     * Ensure form has CSRF token
     */
    ensureFormHasCSRFToken(form) {
        if (!this.needsCSRFToken(form.method, form.action)) {
            return;
        }
        
        // Check if form already has CSRF token
        const existingToken = form.querySelector('input[name="csrfmiddlewaretoken"]');
        if (existingToken) {
            // Update token value if it's different
            if (existingToken.value !== this.token) {
                existingToken.value = this.token;
            }
            return;
        }
        
        // Add CSRF token to form
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = this.token;
        form.appendChild(csrfInput);
    }
    
    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        // Refresh token every 30 minutes
        setInterval(() => {
            this.refreshToken();
        }, 30 * 60 * 1000);
        
        // Refresh token when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshToken();
            }
        });
    }
    
    /**
     * Check if request needs CSRF token
     */
    needsCSRFToken(method, url = '') {
        if (!method) return false;
        
        const safeMethod = /^(GET|HEAD|OPTIONS|TRACE)$/i.test(method);
        if (safeMethod) return false;
        
        // Skip for external URLs
        if (url && (url.startsWith('http://') || url.startsWith('https://')) && !url.includes(window.location.hostname)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Handle CSRF failures with retry logic
     */
    handleCSRFFailure(response, requestInfo) {
        this.failureCount++;
        
        console.warn(`CSRF validation failed (attempt ${this.failureCount})`);
        
        if (this.failureCount <= this.maxRetries) {
            // Try to refresh token and retry
            this.refreshToken().then(() => {
                setTimeout(() => {
                    this.retryRequest(requestInfo);
                }, this.retryDelay * this.failureCount);
            });
        } else {
            // Max retries exceeded, show error to user
            this.showCSRFError();
        }
    }
    
    /**
     * Refresh CSRF token
     */
    async refreshToken() {
        try {
            const response = await fetch(this.tokenRefreshUrl, {
                method: 'GET',
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                const data = await response.json();
                this.token = data.token;
                this.updateMetaTag();
                this.failureCount = 0; // Reset failure count on successful refresh
                console.log('CSRF token refreshed successfully');
            }
        } catch (error) {
            console.error('Failed to refresh CSRF token:', error);
        }
    }
    
    /**
     * Update meta tag with new token
     */
    updateMetaTag() {
        let metaTag = document.querySelector('meta[name="csrf-token"]');
        if (!metaTag) {
            metaTag = document.createElement('meta');
            metaTag.name = 'csrf-token';
            document.head.appendChild(metaTag);
        }
        metaTag.content = this.token;
    }
    
    /**
     * Retry failed request
     */
    retryRequest(requestInfo) {
        // This is a simplified retry - in practice, you'd need to store
        // the original request details and replay them
        console.log('Retrying request:', requestInfo);
        
        // For now, just reload the page as a fallback
        if (this.failureCount >= this.maxRetries) {
            location.reload();
        }
    }
    
    /**
     * Show CSRF error to user
     */
    showCSRFError() {
        const message = 'Security validation failed. The page will be refreshed to resolve this issue.';
        
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert(message);
        }
        
        // Reload page after a short delay
        setTimeout(() => {
            location.reload();
        }, 2000);
    }
    
    /**
     * Handle general CSRF failures from server
     */
    handleCSRFFailures() {
        // Listen for CSRF failure events
        document.addEventListener('csrf:failure', (event) => {
            this.handleCSRFFailure(null, event.detail);
        });
        
        // Check for CSRF failure in URL parameters (redirect from server)
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('csrf_failed')) {
            this.showCSRFError();
        }
    }
    
    /**
     * Get current token
     */
    getToken() {
        return this.token;
    }
    
    /**
     * Manually set token
     */
    setToken(token) {
        this.token = token;
        this.updateMetaTag();
    }
}

// Initialize CSRF protection when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.csrfProtection = new CSRFProtection();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSRFProtection;
}
