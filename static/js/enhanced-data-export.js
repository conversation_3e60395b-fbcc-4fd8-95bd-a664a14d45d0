/**
 * Enhanced Data Export Component
 * 
 * Provides comprehensive data export functionality with multiple formats,
 * scheduled exports, export history tracking, and GDPR-compliant data portability.
 */

class EnhancedDataExport {
    constructor(options = {}) {
        this.options = {
            containerSelector: '#data-export-container',
            exportFormSelector: '#export-form',
            historySelector: '#export-history',
            statisticsSelector: '#export-statistics',
            scheduleFormSelector: '#schedule-form',
            autoRefreshHistory: true,
            refreshInterval: 30000, // 30 seconds
            ...options
        };
        
        this.container = document.querySelector(this.options.containerSelector);
        this.exportForm = document.querySelector(this.options.exportFormSelector);
        this.historyContainer = document.querySelector(this.options.historySelector);
        this.statisticsContainer = document.querySelector(this.options.statisticsSelector);
        this.scheduleForm = document.querySelector(this.options.scheduleFormSelector);
        
        this.exportHistory = [];
        this.statistics = {};
        this.activeExports = new Set();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadExportHistory();
        
        if (this.options.autoRefreshHistory) {
            this.startAutoRefresh();
        }
    }
    
    setupEventListeners() {
        // Export form submission
        if (this.exportForm) {
            this.exportForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createExport();
            });
        }
        
        // Schedule form submission
        if (this.scheduleForm) {
            this.scheduleForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.scheduleExport();
            });
        }
        
        // Format change handler
        const formatSelect = document.getElementById('export-format');
        if (formatSelect) {
            formatSelect.addEventListener('change', (e) => {
                this.updateFormatOptions(e.target.value);
            });
        }
        
        // Cleanup button
        const cleanupBtn = document.getElementById('cleanup-exports');
        if (cleanupBtn) {
            cleanupBtn.addEventListener('click', () => this.cleanupExpiredExports());
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-history');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadExportHistory());
        }
    }
    
    async createExport() {
        try {
            const formData = new FormData(this.exportForm);
            
            const exportData = {
                export_format: formData.get('export_format'),
                data_types: formData.getAll('data_types'),
                options: {
                    compression: formData.get('compression'),
                    template: formData.get('template'),
                    include_metadata: formData.get('include_metadata') === 'on',
                }
            };
            
            this.showLoading('Creating export...');
            
            const response = await fetch('/accounts/data-export/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify(exportData),
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Export created successfully! File: ${data.file_name}`);
                this.activeExports.add(data.export_id);
                this.loadExportHistory(); // Refresh history
                
                // Show download option
                this.showDownloadOption(data.export_id, data.file_name);
            } else {
                this.showError('Failed to create export: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating export:', error);
            this.showError('Failed to create export');
        } finally {
            this.hideLoading();
        }
    }
    
    async scheduleExport() {
        try {
            const formData = new FormData(this.scheduleForm);
            
            const scheduleData = {
                export_format: formData.get('schedule_export_format'),
                data_types: formData.getAll('schedule_data_types'),
                schedule_type: formData.get('schedule_type'),
                schedule_options: {
                    time_of_day: formData.get('time_of_day'),
                    day_of_week: formData.get('day_of_week'),
                    day_of_month: formData.get('day_of_month'),
                }
            };
            
            this.showLoading('Scheduling export...');
            
            const response = await fetch('/accounts/data-export/schedule/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify(scheduleData),
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Export scheduled successfully! Next run: ${new Date(data.schedule.next_run_at).toLocaleString()}`);
                this.scheduleForm.reset();
            } else {
                this.showError('Failed to schedule export: ' + data.error);
            }
        } catch (error) {
            console.error('Error scheduling export:', error);
            this.showError('Failed to schedule export');
        } finally {
            this.hideLoading();
        }
    }
    
    async loadExportHistory() {
        try {
            const response = await fetch('/accounts/data-export/history/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.exportHistory = data.history;
                this.statistics = data.statistics;
                this.renderExportHistory();
                this.renderStatistics();
            } else {
                this.showError('Failed to load export history: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading export history:', error);
            this.showError('Failed to load export history');
        }
    }
    
    renderExportHistory() {
        if (!this.historyContainer) return;
        
        if (this.exportHistory.length === 0) {
            this.historyContainer.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-download fa-3x mb-3"></i>
                    <h5>No Export History</h5>
                    <p>You haven't created any data exports yet.</p>
                </div>
            `;
            return;
        }
        
        const historyHtml = this.exportHistory.map(export => this.renderExportItem(export)).join('');
        
        this.historyContainer.innerHTML = `
            <div class="export-history-list">
                ${historyHtml}
            </div>
        `;
    }
    
    renderExportItem(exportItem) {
        const requestedDate = new Date(exportItem.requested_at);
        const completedDate = exportItem.completed_at ? new Date(exportItem.completed_at) : null;
        const expiresDate = exportItem.expires_at ? new Date(exportItem.expires_at) : null;
        
        return `
            <div class="export-item card mb-3" data-export-id="${exportItem.id}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <div class="export-status text-center">
                                <i class="${this.getStatusIcon(exportItem.status)} fa-2x text-${this.getStatusColor(exportItem.status)}"></i>
                                <div class="small text-muted mt-1">${exportItem.status.toUpperCase()}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">${exportItem.file_name || 'Export #' + exportItem.id}</h6>
                            <div class="export-details small text-muted">
                                <div class="mb-1">
                                    <i class="fas fa-file-alt me-1"></i>
                                    Format: ${exportItem.export_format.toUpperCase()}
                                </div>
                                <div class="mb-1">
                                    <i class="fas fa-database me-1"></i>
                                    Data: ${exportItem.data_types.join(', ')}
                                </div>
                                <div class="mb-1">
                                    <i class="fas fa-clock me-1"></i>
                                    Requested: ${requestedDate.toLocaleString()}
                                </div>
                                ${completedDate ? `
                                    <div class="mb-1">
                                        <i class="fas fa-check me-1"></i>
                                        Completed: ${completedDate.toLocaleString()}
                                    </div>
                                ` : ''}
                                ${exportItem.file_size ? `
                                    <div class="mb-1">
                                        <i class="fas fa-weight me-1"></i>
                                        Size: ${this.formatFileSize(exportItem.file_size)}
                                    </div>
                                ` : ''}
                                ${exportItem.download_count > 0 ? `
                                    <div class="mb-1">
                                        <i class="fas fa-download me-1"></i>
                                        Downloads: ${exportItem.download_count}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            ${this.renderExportActions(exportItem)}
                            
                            ${expiresDate ? `
                                <div class="small text-muted mt-2">
                                    <i class="fas fa-hourglass-end me-1"></i>
                                    Expires: ${expiresDate.toLocaleDateString()}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    ${exportItem.error_message ? `
                        <div class="alert alert-danger mt-3 mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${exportItem.error_message}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    renderExportActions(exportItem) {
        const actions = [];
        
        if (exportItem.status === 'completed' && !this.isExpired(exportItem.expires_at)) {
            actions.push(`
                <button type="button" class="btn btn-primary btn-sm" 
                        onclick="enhancedDataExport.downloadExport(${exportItem.id})">
                    <i class="fas fa-download me-1"></i>
                    Download
                </button>
            `);
        }
        
        if (exportItem.status === 'pending' || exportItem.status === 'processing') {
            actions.push(`
                <button type="button" class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Processing...
                </button>
            `);
        }
        
        if (exportItem.status === 'failed') {
            actions.push(`
                <button type="button" class="btn btn-outline-warning btn-sm" 
                        onclick="enhancedDataExport.retryExport(${exportItem.id})">
                    <i class="fas fa-redo me-1"></i>
                    Retry
                </button>
            `);
        }
        
        return `<div class="btn-group" role="group">${actions.join('')}</div>`;
    }
    
    renderStatistics() {
        if (!this.statisticsContainer || !this.statistics) return;
        
        const statisticsHtml = `
            <div class="statistics-overview">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${this.statistics.total_exports}</div>
                            <div class="stat-label">Total Exports</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${this.statistics.completed_exports}</div>
                            <div class="stat-label">Completed</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${this.statistics.success_rate.toFixed(1)}%</div>
                            <div class="stat-label">Success Rate</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <div class="stat-value">${this.formatFileSize(this.statistics.total_data_size)}</div>
                            <div class="stat-label">Total Data</div>
                        </div>
                    </div>
                </div>
                
                ${Object.keys(this.statistics.format_breakdown).length > 0 ? `
                    <div class="mt-4">
                        <h6>Format Breakdown</h6>
                        <div class="format-breakdown">
                            ${Object.entries(this.statistics.format_breakdown).map(([format, count]) => `
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">${format.toUpperCase()}</span>
                                    <span>${count} exports</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        this.statisticsContainer.innerHTML = statisticsHtml;
    }
    
    async downloadExport(exportId) {
        try {
            const response = await fetch(`/accounts/data-export/download/${exportId}/`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            if (response.ok) {
                // Create download link
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || `export_${exportId}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showSuccess('Export downloaded successfully!');
                this.loadExportHistory(); // Refresh to update download count
            } else {
                const errorData = await response.json();
                this.showError('Failed to download export: ' + errorData.error);
            }
        } catch (error) {
            console.error('Error downloading export:', error);
            this.showError('Failed to download export');
        }
    }
    
    async cleanupExpiredExports() {
        try {
            this.showLoading('Cleaning up expired exports...');
            
            const response = await fetch('/accounts/data-export/cleanup/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Cleaned up ${data.cleaned_count} expired exports`);
                this.loadExportHistory(); // Refresh history
            } else {
                this.showError('Failed to cleanup exports: ' + data.error);
            }
        } catch (error) {
            console.error('Error cleaning up exports:', error);
            this.showError('Failed to cleanup exports');
        } finally {
            this.hideLoading();
        }
    }
    
    updateFormatOptions(format) {
        // Update available options based on selected format
        const compressionSelect = document.getElementById('compression');
        const templateSelect = document.getElementById('template');
        
        if (compressionSelect) {
            // Enable/disable compression options based on format
            const compressionOptions = compressionSelect.querySelectorAll('option');
            compressionOptions.forEach(option => {
                if (option.value === 'pdf' && format === 'pdf') {
                    option.disabled = true;
                } else {
                    option.disabled = false;
                }
            });
        }
    }
    
    showDownloadOption(exportId, fileName) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px;';
        notification.innerHTML = `
            <h6 class="alert-heading">Export Ready!</h6>
            <p class="mb-2">Your export "${fileName}" is ready for download.</p>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-success btn-sm" onclick="enhancedDataExport.downloadExport(${exportId})">
                    <i class="fas fa-download me-1"></i>
                    Download Now
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }
    
    getStatusIcon(status) {
        const icons = {
            'pending': 'fas fa-clock',
            'processing': 'fas fa-spinner fa-spin',
            'completed': 'fas fa-check-circle',
            'failed': 'fas fa-times-circle',
            'expired': 'fas fa-hourglass-end',
            'cancelled': 'fas fa-ban',
        };
        return icons[status] || 'fas fa-question-circle';
    }
    
    getStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'processing': 'info',
            'completed': 'success',
            'failed': 'danger',
            'expired': 'secondary',
            'cancelled': 'secondary',
        };
        return colors[status] || 'secondary';
    }
    
    formatFileSize(bytes) {
        if (!bytes) return 'Unknown';
        
        const sizes = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < sizes.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${sizes[unitIndex]}`;
    }
    
    isExpired(expiresAt) {
        if (!expiresAt) return false;
        return new Date(expiresAt) < new Date();
    }
    
    startAutoRefresh() {
        setInterval(() => {
            this.loadExportHistory();
        }, this.options.refreshInterval);
    }
    
    showLoading(message = 'Loading...') {
        // Show loading indicator
        const loadingElement = document.getElementById('loading-indicator');
        if (loadingElement) {
            loadingElement.textContent = message;
            loadingElement.style.display = 'block';
        }
    }
    
    hideLoading() {
        const loadingElement = document.getElementById('loading-indicator');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#data-export-container')) {
        window.enhancedDataExport = new EnhancedDataExport();
    }
});
