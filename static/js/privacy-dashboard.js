/**
 * Privacy Dashboard Component
 * 
 * Provides comprehensive privacy settings management with granular controls,
 * privacy impact analysis, and user-friendly privacy dashboard.
 */

class PrivacyDashboard {
    constructor(options = {}) {
        this.options = {
            dashboardSelector: '.privacy-dashboard',
            scoreSelector: '#privacy-score-section',
            presetsSelector: '#privacy-presets',
            settingsSelector: '#privacy-settings-form',
            impactSelector: '#privacy-impact',
            recommendationsSelector: '#privacy-recommendations',
            dataUsageSelector: '#data-usage-summary',
            autoRefresh: true,
            refreshInterval: 30000, // 30 seconds
            ...options
        };
        
        this.dashboard = document.querySelector(this.options.dashboardSelector);
        this.scoreSection = document.querySelector(this.options.scoreSelector);
        this.presetsContainer = document.querySelector(this.options.presetsSelector);
        this.settingsContainer = document.querySelector(this.options.settingsSelector);
        this.impactContainer = document.querySelector(this.options.impactSelector);
        this.recommendationsContainer = document.querySelector(this.options.recommendationsSelector);
        this.dataUsageContainer = document.querySelector(this.options.dataUsageSelector);
        
        this.dashboardData = null;
        this.currentSettings = {};
        this.hasUnsavedChanges = false;
        
        this.init();
    }
    
    init() {
        this.loadDashboardData();
        this.setupEventListeners();
        
        if (this.options.autoRefresh) {
            this.startAutoRefresh();
        }
    }
    
    async loadDashboardData() {
        try {
            this.showLoading();
            
            const response = await fetch('/accounts/privacy/dashboard/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.dashboardData = data.dashboard;
                this.currentSettings = { ...data.dashboard.current_settings };
                this.renderDashboard();
            } else {
                this.showError('Failed to load privacy dashboard: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading privacy dashboard:', error);
            this.showError('Failed to load privacy dashboard');
        } finally {
            this.hideLoading();
        }
    }
    
    renderDashboard() {
        if (!this.dashboardData) return;
        
        this.renderPrivacyScore();
        this.renderPrivacyPresets();
        this.renderPrivacySettings();
        this.renderPrivacyImpact();
        this.renderDataUsage();
        this.renderRecommendations();
    }
    
    renderPrivacyScore() {
        const score = this.dashboardData.privacy_score;
        
        // Update score display
        const scoreElement = document.getElementById('privacy-score');
        const titleElement = document.getElementById('privacy-level-title');
        const descriptionElement = document.getElementById('privacy-level-description');
        
        if (scoreElement) {
            scoreElement.textContent = score.score;
        }
        
        if (titleElement) {
            titleElement.textContent = `${score.level_info.description}`;
            titleElement.className = `text-${score.level_info.color}`;
        }
        
        if (descriptionElement) {
            descriptionElement.textContent = score.level_info.description;
        }
        
        // Update score circle color based on level
        const scoreCircle = document.querySelector('.privacy-score-circle');
        if (scoreCircle) {
            scoreCircle.style.background = this.getScoreColor(score.score);
        }
    }
    
    renderPrivacyPresets() {
        if (!this.presetsContainer) return;
        
        const presets = this.dashboardData.presets;
        
        const presetsHtml = Object.entries(presets).map(([key, preset]) => `
            <div class="privacy-preset-card card mb-3" data-preset="${key}" onclick="privacyDashboard.applyPreset('${key}')">
                <div class="card-body text-center">
                    <i class="${preset.icon} fa-2x mb-2 text-primary"></i>
                    <h6 class="card-title">${preset.name}</h6>
                    <p class="card-text small text-muted">${preset.description}</p>
                </div>
            </div>
        `).join('');
        
        this.presetsContainer.innerHTML = presetsHtml;
    }
    
    renderPrivacySettings() {
        if (!this.settingsContainer) return;
        
        const settings = this.dashboardData.current_settings;
        
        const settingsHtml = `
            <div class="privacy-settings">
                <!-- Profile Visibility -->
                <div class="mb-4">
                    <h6 class="mb-3">Profile Visibility</h6>
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Profile Visibility</strong>
                            <div class="small text-muted">Who can see your profile</div>
                        </div>
                        <select class="form-select form-select-sm" style="width: auto;" name="profile_visibility" onchange="privacyDashboard.updateSetting('profile_visibility', this.value)">
                            <option value="public" ${settings.profile_visibility === 'public' ? 'selected' : ''}>Public</option>
                            <option value="limited" ${settings.profile_visibility === 'limited' ? 'selected' : ''}>Limited</option>
                            <option value="private" ${settings.profile_visibility === 'private' ? 'selected' : ''}>Private</option>
                            <option value="custom" ${settings.profile_visibility === 'custom' ? 'selected' : ''}>Custom</option>
                        </select>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Show Name</strong>
                            <div class="small text-muted">Display your name on your profile</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="show_name" ${settings.show_name ? 'checked' : ''} onchange="privacyDashboard.updateSetting('show_name', this.checked)">
                        </div>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Show Profile Picture</strong>
                            <div class="small text-muted">Display your profile picture</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="show_profile_picture" ${settings.show_profile_picture ? 'checked' : ''} onchange="privacyDashboard.updateSetting('show_profile_picture', this.checked)">
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="mb-4">
                    <h6 class="mb-3">Contact Information</h6>
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Show Email</strong>
                            <div class="small text-muted">Allow others to see your email</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="show_email" ${settings.show_email ? 'checked' : ''} onchange="privacyDashboard.updateSetting('show_email', this.checked)">
                        </div>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Show Phone</strong>
                            <div class="small text-muted">Allow others to see your phone number</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="show_phone" ${settings.show_phone ? 'checked' : ''} onchange="privacyDashboard.updateSetting('show_phone', this.checked)">
                        </div>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Contact Permission</strong>
                            <div class="small text-muted">Who can contact you directly</div>
                        </div>
                        <select class="form-select form-select-sm" style="width: auto;" name="contact_permission" onchange="privacyDashboard.updateSetting('contact_permission', this.value)">
                            <option value="anyone" ${settings.contact_permission === 'anyone' ? 'selected' : ''}>Anyone</option>
                            <option value="verified" ${settings.contact_permission === 'verified' ? 'selected' : ''}>Verified Users</option>
                            <option value="bookings" ${settings.contact_permission === 'bookings' ? 'selected' : ''}>Booking Contacts</option>
                            <option value="none" ${settings.contact_permission === 'none' ? 'selected' : ''}>No One</option>
                        </select>
                    </div>
                </div>
                
                <!-- Data Sharing -->
                <div class="mb-4">
                    <h6 class="mb-3">Data Sharing</h6>
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Data Sharing Level</strong>
                            <div class="small text-muted">How much data to share for recommendations</div>
                        </div>
                        <select class="form-select form-select-sm" style="width: auto;" name="data_sharing_level" onchange="privacyDashboard.updateSetting('data_sharing_level', this.value)">
                            <option value="full" ${settings.data_sharing_level === 'full' ? 'selected' : ''}>Full</option>
                            <option value="limited" ${settings.data_sharing_level === 'limited' ? 'selected' : ''}>Limited</option>
                            <option value="minimal" ${settings.data_sharing_level === 'minimal' ? 'selected' : ''}>Minimal</option>
                            <option value="none" ${settings.data_sharing_level === 'none' ? 'selected' : ''}>None</option>
                        </select>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Usage Analytics</strong>
                            <div class="small text-muted">Help improve the platform</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="allow_usage_analytics" ${settings.allow_usage_analytics ? 'checked' : ''} onchange="privacyDashboard.updateSetting('allow_usage_analytics', this.checked)">
                        </div>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Marketing Contact</strong>
                            <div class="small text-muted">Receive marketing communications</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="allow_marketing_contact" ${settings.allow_marketing_contact ? 'checked' : ''} onchange="privacyDashboard.updateSetting('allow_marketing_contact', this.checked)">
                        </div>
                    </div>
                </div>
                
                <!-- Search and Discovery -->
                <div class="mb-4">
                    <h6 class="mb-3">Search and Discovery</h6>
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Searchable Profile</strong>
                            <div class="small text-muted">Allow your profile to appear in searches</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="searchable_profile" ${settings.searchable_profile ? 'checked' : ''} onchange="privacyDashboard.updateSetting('searchable_profile', this.checked)">
                        </div>
                    </div>
                    
                    <div class="privacy-setting-toggle">
                        <div>
                            <strong>Show in Recommendations</strong>
                            <div class="small text-muted">Appear in recommended profiles</div>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="show_in_recommendations" ${settings.show_in_recommendations ? 'checked' : ''} onchange="privacyDashboard.updateSetting('show_in_recommendations', this.checked)">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.settingsContainer.innerHTML = settingsHtml;
    }
    
    renderPrivacyImpact() {
        if (!this.impactContainer) return;
        
        const impact = this.dashboardData.privacy_impact;
        
        if (!impact.impacts || impact.impacts.length === 0) {
            this.impactContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <p>Your privacy settings look good!</p>
                </div>
            `;
            return;
        }
        
        const impactHtml = impact.impacts.map(item => `
            <div class="privacy-impact-item privacy-impact-${item.level}">
                <h6 class="mb-2">${item.title}</h6>
                <p class="mb-2 small">${item.description}</p>
                <div class="small text-muted">
                    <strong>Trade-off:</strong> ${item.trade_off}
                </div>
            </div>
        `).join('');
        
        this.impactContainer.innerHTML = impactHtml;
    }
    
    renderDataUsage() {
        if (!this.dataUsageContainer) return;
        
        const dataUsage = this.dashboardData.data_usage_summary;
        
        const usageHtml = Object.entries(dataUsage.usage).map(([key, enabled]) => `
            <div class="data-usage-item">
                <div>
                    <strong>${this.formatDataUsageKey(key)}</strong>
                </div>
                <div>
                    <span class="badge bg-${enabled ? 'success' : 'secondary'}">
                        ${enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </div>
            </div>
        `).join('');
        
        this.dataUsageContainer.innerHTML = `
            <div class="data-usage-list">
                ${usageHtml}
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="privacyDashboard.showDataDetails()">
                    <i class="fas fa-info-circle me-1"></i>
                    View Details
                </button>
            </div>
        `;
    }
    
    renderRecommendations() {
        if (!this.recommendationsContainer) return;
        
        const recommendations = this.dashboardData.recommendations;
        
        if (!recommendations || recommendations.length === 0) {
            this.recommendationsContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-thumbs-up fa-2x mb-2"></i>
                    <p>No recommendations at this time. Your privacy settings look good!</p>
                </div>
            `;
            return;
        }
        
        const recommendationsHtml = recommendations.map(rec => `
            <div class="privacy-recommendation ${rec.type}">
                <h6 class="mb-2">${rec.title}</h6>
                <p class="mb-2">${rec.description}</p>
                <p class="mb-3 small"><strong>Suggestion:</strong> ${rec.suggestion}</p>
                <button type="button" class="btn btn-primary btn-sm" onclick="privacyDashboard.applyRecommendation('${rec.action}')">
                    Apply Suggestion
                </button>
            </div>
        `).join('');
        
        this.recommendationsContainer.innerHTML = recommendationsHtml;
    }
    
    updateSetting(key, value) {
        this.currentSettings[key] = value;
        this.hasUnsavedChanges = true;
        this.showUnsavedChangesIndicator();
        
        // Debounced auto-save
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = setTimeout(() => {
            this.saveSettings();
        }, 2000);
    }
    
    async applyPreset(presetName) {
        try {
            this.showLoading();
            
            const response = await fetch('/accounts/privacy/apply-preset/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({ preset_name: presetName }),
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Privacy preset "${presetName}" applied successfully!`);
                await this.loadDashboardData(); // Reload to show updated settings
            } else {
                this.showError('Failed to apply preset: ' + data.error);
            }
        } catch (error) {
            console.error('Error applying preset:', error);
            this.showError('Failed to apply privacy preset');
        } finally {
            this.hideLoading();
        }
    }
    
    async saveSettings() {
        if (!this.hasUnsavedChanges) return;
        
        try {
            this.showLoading('settings-loading');
            
            const response = await fetch('/accounts/privacy/bulk-update/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({ settings: this.currentSettings }),
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.hasUnsavedChanges = false;
                this.hideUnsavedChangesIndicator();
                this.showSuccess('Privacy settings saved successfully!');
                
                // Update privacy score
                if (data.new_privacy_score) {
                    this.dashboardData.privacy_score = data.new_privacy_score;
                    this.renderPrivacyScore();
                }
            } else {
                this.showError('Failed to save settings: ' + data.error);
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showError('Failed to save privacy settings');
        } finally {
            this.hideLoading('settings-loading');
        }
    }
    
    async showImpactAnalysis() {
        try {
            const response = await fetch('/accounts/privacy/impact-analysis/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Show detailed impact analysis in modal
                const modalContent = document.getElementById('detailed-impact-analysis');
                if (modalContent) {
                    modalContent.innerHTML = this.renderDetailedImpactAnalysis(data.impact_analysis, data.privacy_score);
                    const modal = new bootstrap.Modal(document.getElementById('privacyImpactModal'));
                    modal.show();
                }
            }
        } catch (error) {
            console.error('Error loading impact analysis:', error);
        }
    }
    
    renderDetailedImpactAnalysis(impact, score) {
        return `
            <div class="impact-analysis">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="privacy-score-circle mx-auto mb-2" style="width: 80px; height: 80px; background: ${this.getScoreColor(score.score)};">
                                <div class="privacy-score-number" style="font-size: 1.5rem;">${score.score}</div>
                            </div>
                            <h6>${score.level_info.description}</h6>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Impact Summary</h6>
                        <p class="text-muted">Overall Impact Level: <strong class="text-${this.getImpactColor(impact.overall_impact)}">${impact.overall_impact.toUpperCase()}</strong></p>
                    </div>
                </div>
                
                <h6>Detailed Impact Analysis</h6>
                ${impact.impacts.map(item => `
                    <div class="privacy-impact-item privacy-impact-${item.level} mb-3">
                        <h6 class="mb-2">${item.title}</h6>
                        <p class="mb-2">${item.description}</p>
                        <div class="small text-muted">
                            <strong>Trade-off:</strong> ${item.trade_off}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    showRecommendations() {
        // Scroll to recommendations section
        if (this.recommendationsContainer) {
            this.recommendationsContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    applyRecommendation(action) {
        // Apply specific recommendation actions
        switch (action) {
            case 'adjust_visibility':
                this.updateSetting('profile_visibility', 'limited');
                break;
            case 'hide_contact_info':
                this.updateSetting('show_email', false);
                this.updateSetting('show_phone', false);
                break;
            case 'fix_inconsistency':
                this.updateSetting('searchable_profile', false);
                break;
        }
        
        this.saveSettings();
    }
    
    showDataDetails() {
        // Show detailed data usage information
        alert('Detailed data usage information would be shown here.');
    }
    
    formatDataUsageKey(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    
    getScoreColor(score) {
        if (score >= 80) return 'rgba(40, 167, 69, 0.2)'; // Green
        if (score >= 50) return 'rgba(255, 193, 7, 0.2)'; // Yellow
        return 'rgba(220, 53, 69, 0.2)'; // Red
    }
    
    getImpactColor(level) {
        const colors = { high: 'danger', medium: 'warning', low: 'info' };
        return colors[level] || 'secondary';
    }
    
    showUnsavedChangesIndicator() {
        // Show indicator that there are unsaved changes
        const saveButton = document.querySelector('[onclick="privacyDashboard.saveSettings()"]');
        if (saveButton) {
            saveButton.classList.add('btn-warning');
            saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Save Changes';
        }
    }
    
    hideUnsavedChangesIndicator() {
        const saveButton = document.querySelector('[onclick="privacyDashboard.saveSettings()"]');
        if (saveButton) {
            saveButton.classList.remove('btn-warning');
            saveButton.classList.add('btn-primary');
            saveButton.innerHTML = '<i class="fas fa-save me-1"></i>Save Changes';
        }
    }
    
    setupEventListeners() {
        // Listen for beforeunload to warn about unsaved changes
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved privacy settings. Are you sure you want to leave?';
            }
        });
    }
    
    startAutoRefresh() {
        setInterval(() => {
            if (!this.hasUnsavedChanges) {
                this.loadDashboardData();
            }
        }, this.options.refreshInterval);
    }
    
    showLoading(elementId = null) {
        if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.remove('d-none');
            }
        }
    }
    
    hideLoading(elementId = null) {
        if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('d-none');
            }
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.privacy-dashboard')) {
        window.privacyDashboard = new PrivacyDashboard();
    }
});
