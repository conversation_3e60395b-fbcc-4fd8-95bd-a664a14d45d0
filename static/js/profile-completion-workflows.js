/**
 * Profile Completion Workflows Component
 * 
 * Provides guided profile completion workflows with smart suggestions,
 * milestone tracking, and completion incentives.
 */

class ProfileCompletionWorkflows {
    constructor(options = {}) {
        this.options = {
            containerSelector: '#completion-workflows',
            suggestionsSelector: '#completion-suggestions',
            milestonesSelector: '#completion-milestones',
            tourSelector: '#guided-tour',
            enableGuidedTour: true,
            enableMilestones: true,
            enableSuggestions: true,
            autoCheckCompletion: true,
            checkInterval: 30000, // 30 seconds
            ...options
        };
        
        this.container = document.querySelector(this.options.containerSelector);
        this.suggestionsContainer = document.querySelector(this.options.suggestionsSelector);
        this.milestonesContainer = document.querySelector(this.options.milestonesSelector);
        
        this.workflowData = null;
        this.checkTimer = null;
        this.tourInstance = null;
        
        this.init();
    }
    
    init() {
        this.loadWorkflowData();
        this.setupEventListeners();
        
        if (this.options.autoCheckCompletion) {
            this.startAutoCheck();
        }
    }
    
    async loadWorkflowData() {
        try {
            const response = await fetch('/accounts/profile/completion-workflow/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.workflowData = data.workflow;
                this.renderWorkflow();
                
                // Auto-start guided tour if applicable
                if (this.options.enableGuidedTour && 
                    this.workflowData.guided_tour && 
                    this.workflowData.guided_tour.auto_start) {
                    this.startGuidedTour();
                }
            } else {
                console.error('Failed to load workflow data:', data.error);
            }
        } catch (error) {
            console.error('Error loading workflow data:', error);
        }
    }
    
    renderWorkflow() {
        if (!this.workflowData) return;
        
        if (this.options.enableSuggestions) {
            this.renderSuggestions();
        }
        
        if (this.options.enableMilestones) {
            this.renderMilestones();
        }
        
        this.renderProgressIndicator();
    }
    
    renderSuggestions() {
        if (!this.suggestionsContainer || !this.workflowData.smart_suggestions) return;
        
        const suggestions = this.workflowData.smart_suggestions;
        
        if (suggestions.length === 0) {
            this.suggestionsContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Great job! You've completed all suggested profile improvements.
                </div>
            `;
            return;
        }
        
        const suggestionsHtml = suggestions.map(suggestion => `
            <div class="suggestion-card card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-2">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                ${suggestion.title}
                            </h6>
                            <p class="card-text text-muted mb-2">${suggestion.description}</p>
                            
                            <div class="suggestion-meta d-flex flex-wrap gap-2 mb-3">
                                <span class="badge bg-${this.getPriorityColor(suggestion.priority)}">
                                    ${suggestion.priority.toUpperCase()} Priority
                                </span>
                                <span class="badge bg-light text-dark">
                                    +${suggestion.completion_boost}% completion
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="fas fa-clock me-1"></i>
                                    ${suggestion.estimated_time}
                                </span>
                            </div>
                            
                            ${suggestion.tips ? this.renderTips(suggestion.tips) : ''}
                        </div>
                    </div>
                    
                    <div class="suggestion-actions">
                        <a href="${suggestion.action_url}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>
                            Complete This Step
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-sm ms-2" 
                                onclick="profileWorkflows.dismissSuggestion('${suggestion.field}')">
                            <i class="fas fa-times me-1"></i>
                            Dismiss
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
        
        this.suggestionsContainer.innerHTML = `
            <div class="completion-suggestions">
                <h6 class="mb-3">
                    <i class="fas fa-rocket me-2"></i>
                    Complete Your Profile
                </h6>
                ${suggestionsHtml}
            </div>
        `;
    }
    
    renderMilestones() {
        if (!this.milestonesContainer || !this.workflowData.completion_incentives) return;
        
        const incentives = this.workflowData.completion_incentives;
        const nextMilestone = this.workflowData.next_milestone;
        
        const milestonesHtml = incentives.map(incentive => `
            <div class="milestone-item d-flex align-items-center mb-3 ${incentive.is_achieved ? 'achieved' : ''}">
                <div class="milestone-icon me-3">
                    <i class="${incentive.icon} ${incentive.is_achieved ? 'text-' + incentive.color : 'text-muted'}"></i>
                </div>
                <div class="milestone-content flex-grow-1">
                    <div class="milestone-title fw-bold ${incentive.is_achieved ? 'text-' + incentive.color : ''}">
                        ${incentive.title}
                    </div>
                    <div class="milestone-description text-muted small">
                        ${incentive.description}
                    </div>
                    <div class="milestone-reward text-primary small">
                        <i class="fas fa-gift me-1"></i>
                        ${incentive.reward}
                    </div>
                </div>
                <div class="milestone-percentage">
                    <span class="badge ${incentive.is_achieved ? 'bg-' + incentive.color : 'bg-light text-dark'}">
                        ${incentive.target_percentage}%
                    </span>
                </div>
            </div>
        `).join('');
        
        this.milestonesContainer.innerHTML = `
            <div class="completion-milestones">
                <h6 class="mb-3">
                    <i class="fas fa-trophy me-2"></i>
                    Completion Rewards
                </h6>
                ${milestonesHtml}
                
                ${nextMilestone ? `
                    <div class="next-milestone-cta mt-4 p-3 bg-light rounded">
                        <div class="text-center">
                            <i class="${nextMilestone.icon} text-${nextMilestone.color} fa-2x mb-2"></i>
                            <h6 class="mb-2">${nextMilestone.title}</h6>
                            <p class="text-muted mb-3">${nextMilestone.description}</p>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-${nextMilestone.color}" 
                                     style="width: ${Math.max(10, (this.workflowData.current_completion / nextMilestone.target_percentage) * 100)}%">
                                </div>
                            </div>
                            <small class="text-muted">
                                ${nextMilestone.progress_needed}% more to unlock: ${nextMilestone.reward}
                            </small>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    renderProgressIndicator() {
        const currentCompletion = this.workflowData.current_completion || 0;
        
        // Update any existing progress indicators
        const progressBars = document.querySelectorAll('.profile-completion-progress');
        progressBars.forEach(bar => {
            const progressElement = bar.querySelector('.progress-bar');
            const percentageElement = bar.querySelector('.completion-percentage');
            
            if (progressElement) {
                progressElement.style.width = `${currentCompletion}%`;
                progressElement.setAttribute('aria-valuenow', currentCompletion);
            }
            
            if (percentageElement) {
                percentageElement.textContent = `${currentCompletion}%`;
            }
        });
    }
    
    renderTips(tips) {
        if (!tips || tips.length === 0) return '';
        
        const tipsHtml = tips.map(tip => `
            <li class="small text-muted">${tip}</li>
        `).join('');
        
        return `
            <div class="suggestion-tips">
                <details class="mb-2">
                    <summary class="small text-primary" style="cursor: pointer;">
                        <i class="fas fa-info-circle me-1"></i>
                        Tips for success
                    </summary>
                    <ul class="mt-2 mb-0">
                        ${tipsHtml}
                    </ul>
                </details>
            </div>
        `;
    }
    
    getPriorityColor(priority) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info'
        };
        return colors[priority] || 'secondary';
    }
    
    async startGuidedTour() {
        if (!this.options.enableGuidedTour) return;
        
        try {
            const response = await fetch('/accounts/profile/start-guided-tour/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success && data.tour) {
                this.initializeTour(data.tour);
            }
        } catch (error) {
            console.error('Error starting guided tour:', error);
        }
    }
    
    initializeTour(tourData) {
        // This would integrate with a tour library like Shepherd.js or Intro.js
        // For now, we'll create a simple custom tour
        this.showTourStep(0, tourData.steps);
    }
    
    showTourStep(stepIndex, steps) {
        if (stepIndex >= steps.length) return;
        
        const step = steps[stepIndex];
        const target = document.querySelector(step.target);
        
        if (!target) {
            // Skip to next step if target not found
            this.showTourStep(stepIndex + 1, steps);
            return;
        }
        
        // Create tour overlay
        const overlay = document.createElement('div');
        overlay.className = 'tour-overlay';
        overlay.innerHTML = `
            <div class="tour-popup" style="position: absolute; z-index: 9999; background: white; border-radius: 8px; padding: 1rem; box-shadow: 0 4px 12px rgba(0,0,0,0.15); max-width: 300px;">
                <h6 class="mb-2">${step.title}</h6>
                <p class="mb-3 small">${step.content}</p>
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="profileWorkflows.skipTour()">
                        Skip Tour
                    </button>
                    <div>
                        ${stepIndex > 0 ? '<button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="profileWorkflows.previousTourStep()">Previous</button>' : ''}
                        <button type="button" class="btn btn-sm btn-primary" onclick="profileWorkflows.nextTourStep()">
                            ${stepIndex === steps.length - 1 ? 'Finish' : 'Next'}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Position the popup
        const rect = target.getBoundingClientRect();
        const popup = overlay.querySelector('.tour-popup');
        
        document.body.appendChild(overlay);
        
        // Store tour state
        this.currentTourStep = stepIndex;
        this.tourSteps = steps;
        this.tourOverlay = overlay;
        
        // Position popup based on placement
        this.positionTourPopup(popup, rect, step.placement);
    }
    
    positionTourPopup(popup, targetRect, placement) {
        const popupRect = popup.getBoundingClientRect();
        let top, left;
        
        switch (placement) {
            case 'right':
                top = targetRect.top + (targetRect.height / 2) - (popupRect.height / 2);
                left = targetRect.right + 10;
                break;
            case 'left':
                top = targetRect.top + (targetRect.height / 2) - (popupRect.height / 2);
                left = targetRect.left - popupRect.width - 10;
                break;
            case 'bottom':
                top = targetRect.bottom + 10;
                left = targetRect.left + (targetRect.width / 2) - (popupRect.width / 2);
                break;
            case 'top':
            default:
                top = targetRect.top - popupRect.height - 10;
                left = targetRect.left + (targetRect.width / 2) - (popupRect.width / 2);
                break;
        }
        
        popup.style.top = `${Math.max(10, top)}px`;
        popup.style.left = `${Math.max(10, Math.min(window.innerWidth - popupRect.width - 10, left))}px`;
    }
    
    nextTourStep() {
        if (this.tourOverlay) {
            this.tourOverlay.remove();
        }
        
        if (this.currentTourStep < this.tourSteps.length - 1) {
            this.showTourStep(this.currentTourStep + 1, this.tourSteps);
        } else {
            this.finishTour();
        }
    }
    
    previousTourStep() {
        if (this.tourOverlay) {
            this.tourOverlay.remove();
        }
        
        if (this.currentTourStep > 0) {
            this.showTourStep(this.currentTourStep - 1, this.tourSteps);
        }
    }
    
    skipTour() {
        if (this.tourOverlay) {
            this.tourOverlay.remove();
        }
        this.finishTour();
    }
    
    finishTour() {
        this.currentTourStep = null;
        this.tourSteps = null;
        this.tourOverlay = null;
    }
    
    async triggerCompletionCheck() {
        try {
            const response = await fetch('/accounts/profile/trigger-completion-check/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
            });
            
            const data = await response.json();
            
            if (data.success) {
                const results = data.results;
                
                // Show milestone notifications if any new milestones
                if (results.new_milestones && results.new_milestones.length > 0) {
                    this.showMilestoneNotifications(results.new_milestones);
                }
                
                // Update progress if changed
                if (results.percentage_change > 0) {
                    this.showProgressUpdate(results.old_percentage, results.new_percentage);
                    
                    // Reload workflow data to get updated suggestions
                    await this.loadWorkflowData();
                }
            }
        } catch (error) {
            console.error('Error triggering completion check:', error);
        }
    }
    
    showMilestoneNotifications(milestones) {
        milestones.forEach(milestone => {
            this.showNotification({
                title: 'Milestone Achieved!',
                message: `You've achieved: ${milestone.title}`,
                type: 'success',
                icon: 'fas fa-trophy',
                duration: 5000
            });
        });
    }
    
    showProgressUpdate(oldPercentage, newPercentage) {
        this.showNotification({
            title: 'Profile Updated!',
            message: `Your profile is now ${newPercentage}% complete (+${newPercentage - oldPercentage}%)`,
            type: 'info',
            icon: 'fas fa-chart-line',
            duration: 3000
        });
    }
    
    showNotification(options) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${options.type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="${options.icon} me-2"></i>
            <strong>${options.title}</strong> ${options.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, options.duration || 3000);
    }
    
    dismissSuggestion(field) {
        // This would typically save the dismissal to the backend
        console.log('Dismissing suggestion for field:', field);
        
        // For now, just hide the suggestion
        const suggestionCard = document.querySelector(`[data-field="${field}"]`);
        if (suggestionCard) {
            suggestionCard.style.display = 'none';
        }
    }
    
    setupEventListeners() {
        // Listen for form changes to trigger completion checks
        document.addEventListener('change', (e) => {
            if (e.target.closest('#profile-form')) {
                // Debounced completion check
                clearTimeout(this.checkTimer);
                this.checkTimer = setTimeout(() => {
                    this.triggerCompletionCheck();
                }, 2000);
            }
        });
    }
    
    startAutoCheck() {
        setInterval(() => {
            this.triggerCompletionCheck();
        }, this.options.checkInterval);
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Global methods for tour navigation
window.profileWorkflows = null;

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#completion-workflows') || 
        document.querySelector('#completion-suggestions')) {
        window.profileWorkflows = new ProfileCompletionWorkflows();
    }
});
