/**
 * Real-time Profile Preview Component
 * 
 * Provides live preview functionality for profile editing forms.
 * Features:
 * - Real-time form validation
 * - Live profile preview updates
 * - Image preview with drag & drop
 * - Completion percentage tracking
 */

class ProfilePreview {
    constructor(options = {}) {
        this.options = {
            formSelector: '#profile-form',
            previewSelector: '#profile-preview',
            completionSelector: '#completion-progress',
            imagePreviewSelector: '#image-preview',
            debounceDelay: 500,
            enableImagePreview: true,
            enableValidation: true,
            enableCompletion: true,
            ...options
        };
        
        this.form = document.querySelector(this.options.formSelector);
        this.previewContainer = document.querySelector(this.options.previewSelector);
        this.completionContainer = document.querySelector(this.options.completionSelector);
        this.imagePreviewContainer = document.querySelector(this.options.imagePreviewSelector);
        
        this.debounceTimer = null;
        this.isUpdating = false;
        this.validationErrors = {};
        
        this.init();
    }
    
    init() {
        if (!this.form) {
            console.warn('Profile form not found');
            return;
        }
        
        this.setupEventListeners();
        this.setupImagePreview();
        this.initialPreview();
    }
    
    setupEventListeners() {
        // Form field change listeners
        const formFields = this.form.querySelectorAll('input, select, textarea');
        formFields.forEach(field => {
            field.addEventListener('input', (e) => this.handleFieldChange(e));
            field.addEventListener('change', (e) => this.handleFieldChange(e));
            field.addEventListener('blur', (e) => this.handleFieldBlur(e));
        });
        
        // Image upload listeners
        const imageFields = this.form.querySelectorAll('input[type="file"]');
        imageFields.forEach(field => {
            field.addEventListener('change', (e) => this.handleImageChange(e));
        });
        
        // Form submission listener
        this.form.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }
    
    setupImagePreview() {
        if (!this.options.enableImagePreview || !this.imagePreviewContainer) {
            return;
        }
        
        // Setup drag and drop for image preview
        this.imagePreviewContainer.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.imagePreviewContainer.classList.add('drag-over');
        });
        
        this.imagePreviewContainer.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.imagePreviewContainer.classList.remove('drag-over');
        });
        
        this.imagePreviewContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            this.imagePreviewContainer.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageFile(files[0]);
            }
        });
    }
    
    handleFieldChange(event) {
        const field = event.target;
        
        // Clear previous validation error
        this.clearFieldError(field);
        
        // Debounced preview update
        this.debouncedPreviewUpdate();
        
        // Immediate validation for certain fields
        if (this.options.enableValidation) {
            this.validateField(field);
        }
    }
    
    handleFieldBlur(event) {
        const field = event.target;
        
        // Validate field on blur
        if (this.options.enableValidation) {
            this.validateField(field);
        }
    }
    
    handleImageChange(event) {
        const file = event.target.files[0];
        if (file) {
            this.handleImageFile(file);
        }
    }
    
    handleImageFile(file) {
        // Validate image file
        if (!this.validateImageFile(file)) {
            return;
        }
        
        // Show image preview
        this.showImagePreview(file);
        
        // Update profile preview
        this.debouncedPreviewUpdate();
    }
    
    validateImageFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'];
        
        if (file.size > maxSize) {
            this.showError('Image file too large. Maximum size is 10MB.');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            this.showError('Unsupported image format. Please use JPEG, PNG, WebP, or HEIC/HEIF.');
            return false;
        }
        
        return true;
    }
    
    showImagePreview(file) {
        if (!this.imagePreviewContainer) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = this.imagePreviewContainer.querySelector('img') || document.createElement('img');
            img.src = e.target.result;
            img.className = 'profile-image-preview';
            img.style.maxWidth = '200px';
            img.style.maxHeight = '200px';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '8px';
            
            if (!this.imagePreviewContainer.querySelector('img')) {
                this.imagePreviewContainer.appendChild(img);
            }
        };
        reader.readAsDataURL(file);
    }
    
    debouncedPreviewUpdate() {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.updatePreview();
        }, this.options.debounceDelay);
    }
    
    async updatePreview() {
        if (this.isUpdating) return;
        
        this.isUpdating = true;
        this.showLoadingState();
        
        try {
            const formData = this.getFormData();
            const response = await this.sendPreviewRequest(formData);
            
            if (response.success) {
                this.updatePreviewDisplay(response);
                this.updateCompletionDisplay(response);
                this.updateValidationDisplay(response);
            } else {
                this.showError(response.error || 'Failed to update preview');
            }
        } catch (error) {
            console.error('Preview update error:', error);
            this.showError('Failed to update preview');
        } finally {
            this.isUpdating = false;
            this.hideLoadingState();
        }
    }
    
    getFormData() {
        const formData = {};
        const formFields = this.form.querySelectorAll('input, select, textarea');
        
        formFields.forEach(field => {
            if (field.type === 'file') {
                // Skip file fields for now
                return;
            }
            
            if (field.type === 'checkbox' || field.type === 'radio') {
                if (field.checked) {
                    formData[field.name] = field.value;
                }
            } else {
                formData[field.name] = field.value;
            }
        });
        
        return formData;
    }
    
    async sendPreviewRequest(formData) {
        const response = await fetch('/accounts/profile/preview/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken(),
            },
            body: JSON.stringify(formData),
        });
        
        return await response.json();
    }
    
    updatePreviewDisplay(response) {
        if (this.previewContainer && response.preview_html) {
            this.previewContainer.innerHTML = response.preview_html;
        }
    }
    
    updateCompletionDisplay(response) {
        if (!this.options.enableCompletion || !this.completionContainer) return;
        
        const percentage = response.completion_percentage || 0;
        
        // Update progress bar
        const progressBar = this.completionContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        // Update percentage text
        const percentageText = this.completionContainer.querySelector('.completion-percentage');
        if (percentageText) {
            percentageText.textContent = `${percentage}%`;
        }
        
        // Update completion suggestions
        if (response.completion_details && response.completion_details.suggestions) {
            this.updateCompletionSuggestions(response.completion_details.suggestions);
        }
    }
    
    updateCompletionSuggestions(suggestions) {
        const suggestionsContainer = document.querySelector('#completion-suggestions');
        if (!suggestionsContainer || !suggestions.length) return;
        
        const suggestionsList = suggestions.map(suggestion => 
            `<li class="list-group-item d-flex justify-content-between align-items-center">
                <span>${suggestion.title}</span>
                <span class="badge bg-primary rounded-pill">+${suggestion.boost}%</span>
            </li>`
        ).join('');
        
        suggestionsContainer.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Complete Your Profile</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        ${suggestionsList}
                    </ul>
                </div>
            </div>
        `;
    }
    
    updateValidationDisplay(response) {
        if (!this.options.enableValidation) return;
        
        // Clear previous errors
        this.clearAllFieldErrors();
        
        if (response.validation_results && response.validation_results.field_errors) {
            Object.entries(response.validation_results.field_errors).forEach(([fieldName, error]) => {
                this.showFieldError(fieldName, error);
            });
        }
    }
    
    validateField(field) {
        // Basic client-side validation
        const value = field.value.trim();
        const fieldName = field.name;
        
        // Clear previous error
        this.clearFieldError(field);
        
        // Required field validation
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(fieldName, 'This field is required');
            return false;
        }
        
        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(fieldName, 'Please enter a valid email address');
                return false;
            }
        }
        
        // Phone validation
        if (fieldName === 'phone_number' || fieldName === 'phone') {
            const phoneRegex = /^[\+]?[\d\s\-\(\)]+$/;
            if (value && !phoneRegex.test(value)) {
                this.showFieldError(fieldName, 'Please enter a valid phone number');
                return false;
            }
        }
        
        return true;
    }
    
    showFieldError(fieldName, message) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        field.classList.add('is-invalid');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
        
        this.validationErrors[fieldName] = message;
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
        delete this.validationErrors[field.name];
    }
    
    clearAllFieldErrors() {
        const invalidFields = this.form.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => this.clearFieldError(field));
    }
    
    showError(message) {
        // Show error message to user
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of form
        this.form.insertBefore(alertDiv, this.form.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    showLoadingState() {
        if (this.previewContainer) {
            this.previewContainer.classList.add('loading');
        }
    }
    
    hideLoadingState() {
        if (this.previewContainer) {
            this.previewContainer.classList.remove('loading');
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    initialPreview() {
        // Generate initial preview
        setTimeout(() => {
            this.updatePreview();
        }, 100);
    }
    
    handleFormSubmit(event) {
        // Validate all fields before submission
        if (!this.options.enableValidation) return;
        
        let isValid = true;
        const formFields = this.form.querySelectorAll('input, select, textarea');
        
        formFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            event.preventDefault();
            this.showError('Please correct the errors below before submitting.');
        }
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize profile preview if form exists
    if (document.querySelector('#profile-form')) {
        window.profilePreview = new ProfilePreview();
    }
});
