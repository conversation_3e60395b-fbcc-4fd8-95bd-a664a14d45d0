{% load static %}
{% load i18n %}

<!-- Service Provider Profile Preview Component -->
<div class="profile-preview-card">
    <div class="card">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-eye me-2"></i>
                {% trans "Business Profile Preview" %}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Business Logo Section -->
                <div class="col-md-3 text-center">
                    <div class="business-logo-preview mb-3">
                        {% if profile.logo %}
                            <img src="{{ profile.logo.url }}" 
                                 alt="{% trans 'Business Logo' %}" 
                                 class="business-logo-image">
                        {% else %}
                            <div class="logo-placeholder">
                                <i class="fas fa-building fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if profile.is_verified %}
                        <div class="verification-badge">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                {% trans "Verified Business" %}
                            </span>
                        </div>
                    {% endif %}
                    
                    {% if not profile.is_public %}
                        <div class="visibility-badge mt-2">
                            <span class="badge bg-warning">
                                <i class="fas fa-eye-slash me-1"></i>
                                {% trans "Hidden" %}
                            </span>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Business Information Section -->
                <div class="col-md-9">
                    <div class="business-info">
                        <!-- Business Name Section -->
                        <div class="name-section mb-3">
                            <h5 class="business-name mb-1">
                                {% if profile.legal_name %}
                                    {{ profile.legal_name }}
                                {% else %}
                                    <span class="text-muted">{% trans "Your Business Name" %}</span>
                                {% endif %}
                            </h5>
                            
                            {% if profile.display_name and profile.display_name != profile.legal_name %}
                                <p class="dba-name text-muted mb-1">
                                    {% trans "DBA:" %} {{ profile.display_name }}
                                </p>
                            {% endif %}
                            
                            <p class="business-email text-muted mb-0">{{ user.email }}</p>
                        </div>
                        
                        <!-- Business Description -->
                        {% if profile.description %}
                            <div class="description-section mb-3">
                                <p class="business-description">{{ profile.description }}</p>
                            </div>
                        {% endif %}
                        
                        <!-- Contact Information -->
                        <div class="contact-info mb-3">
                            <div class="row">
                                {% if profile.phone %}
                                    <div class="col-sm-6">
                                        <div class="info-item">
                                            <i class="fas fa-phone text-primary me-2"></i>
                                            <span>{{ profile.phone }}</span>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                {% if profile.contact_name %}
                                    <div class="col-sm-6">
                                        <div class="info-item">
                                            <i class="fas fa-user text-primary me-2"></i>
                                            <span>{{ profile.contact_name }}</span>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Address Information -->
                        {% if profile.address or profile.city or profile.state or profile.zip_code %}
                            <div class="address-info mb-3">
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <span>
                                        {% if profile.address %}{{ profile.address }}{% endif %}
                                        {% if profile.city %}
                                            {% if profile.address %}, {% endif %}{{ profile.city }}
                                        {% endif %}
                                        {% if profile.state %}
                                            {% if profile.address or profile.city %}, {% endif %}{{ profile.get_state_display }}
                                        {% endif %}
                                        {% if profile.zip_code %}
                                            {% if profile.address or profile.city or profile.state %} {% endif %}{{ profile.zip_code }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Business Details -->
                        <div class="business-details mb-3">
                            {% if profile.ein %}
                                <div class="info-item">
                                    <i class="fas fa-id-card text-primary me-2"></i>
                                    <span>{% trans "EIN:" %} {{ profile.ein }}</span>
                                </div>
                            {% endif %}
                            
                            {% if profile.county %}
                                <div class="info-item">
                                    <i class="fas fa-map text-primary me-2"></i>
                                    <span>{% trans "County:" %} {{ profile.county }}</span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Social Media Links -->
                        {% if profile.website or profile.instagram or profile.facebook %}
                            <div class="social-links mb-3">
                                <div class="d-flex flex-wrap gap-2">
                                    {% if profile.website %}
                                        <a href="{{ profile.website }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-globe me-1"></i>
                                            {% trans "Website" %}
                                        </a>
                                    {% endif %}
                                    
                                    {% if profile.instagram %}
                                        <a href="{{ profile.instagram }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="fab fa-instagram me-1"></i>
                                            {% trans "Instagram" %}
                                        </a>
                                    {% endif %}
                                    
                                    {% if profile.facebook %}
                                        <a href="{{ profile.facebook }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="fab fa-facebook me-1"></i>
                                            {% trans "Facebook" %}
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Profile Completion Progress -->
            <div class="completion-progress mt-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-muted">{% trans "Profile Completion" %}</small>
                    <small class="completion-percentage fw-bold">{{ profile.profile_completion_percentage|default:0 }}%</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-success" 
                         role="progressbar" 
                         style="width: {{ profile.profile_completion_percentage|default:0 }}%"
                         aria-valuenow="{{ profile.profile_completion_percentage|default:0 }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="quick-stats mt-3">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="stat-item">
                            <div class="stat-value">{{ user.date_joined|date:"M Y" }}</div>
                            <div class="stat-label text-muted small">{% trans "Member Since" %}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-item">
                            <div class="stat-value">
                                {% if profile.is_verified %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% else %}
                                    <i class="fas fa-clock text-warning"></i>
                                {% endif %}
                            </div>
                            <div class="stat-label text-muted small">{% trans "Verification" %}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-item">
                            <div class="stat-value">
                                {% if profile.is_public %}
                                    <i class="fas fa-eye text-success"></i>
                                {% else %}
                                    <i class="fas fa-eye-slash text-warning"></i>
                                {% endif %}
                            </div>
                            <div class="stat-label text-muted small">{% trans "Visibility" %}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-item">
                            <div class="stat-value">{{ profile.profile_completion_percentage|default:0 }}%</div>
                            <div class="stat-label text-muted small">{% trans "Complete" %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {% if is_preview %}
            <div class="card-footer bg-light">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "This is a preview of how your business profile will appear to customers." %}
                </small>
            </div>
        {% endif %}
    </div>
</div>

<style>
.profile-preview-card .business-logo-image {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border: 2px solid #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background-color: #fff;
}

.profile-preview-card .logo-placeholder {
    width: 100px;
    height: 100px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.profile-preview-card .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.profile-preview-card .info-item i {
    width: 20px;
    text-align: center;
}

.profile-preview-card .business-description {
    font-style: italic;
    color: #6c757d;
    margin-bottom: 0;
}

.profile-preview-card .verification-badge,
.profile-preview-card .visibility-badge {
    margin-top: 0.5rem;
}

.profile-preview-card .social-links .btn {
    font-size: 0.875rem;
}

.profile-preview-card .stat-item {
    padding: 0.5rem;
}

.profile-preview-card .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.profile-preview-card .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-preview-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.profile-preview-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Drag and drop styles */
.drag-over {
    border: 2px dashed #007bff !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
}
</style>
