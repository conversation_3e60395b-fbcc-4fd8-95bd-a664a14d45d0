{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Privacy Dashboard - CozyWish{% endblock %}

{% block extra_css %}
<style>
.privacy-dashboard {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 2rem 0;
}

.privacy-score-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.privacy-score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.privacy-score-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.privacy-preset-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.privacy-preset-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.privacy-preset-card.active {
    border-color: #28a745;
    background-color: #f8fff9;
}

.privacy-impact-item {
    padding: 1rem;
    border-left: 4px solid;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.privacy-impact-high {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}

.privacy-impact-medium {
    border-left-color: #ffc107;
    background-color: #fffbf0;
}

.privacy-impact-low {
    border-left-color: #17a2b8;
    background-color: #f0f9ff;
}

.privacy-setting-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: background-color 0.3s ease;
}

.privacy-setting-toggle:hover {
    background-color: #f8f9fa;
}

.privacy-recommendation {
    border-left: 4px solid;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.privacy-recommendation.warning {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}

.privacy-recommendation.caution {
    border-left-color: #ffc107;
    background-color: #fffbf0;
}

.privacy-recommendation.inconsistency {
    border-left-color: #6f42c1;
    background-color: #f8f6ff;
}

.data-usage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.data-usage-item:last-child {
    border-bottom: none;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}
</style>
{% endblock %}

{% block content %}
<div class="privacy-dashboard">
    <div class="container">
        <!-- Privacy Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2 mb-2">
                    <i class="fas fa-shield-alt me-2"></i>
                    {% trans "Privacy Dashboard" %}
                </h1>
                <p class="text-muted">
                    {% trans "Manage your privacy settings and control how your data is used." %}
                </p>
            </div>
        </div>
        
        <!-- Privacy Score Card -->
        <div id="privacy-score-section">
            <div class="privacy-score-card">
                <div class="row align-items-center">
                    <div class="col-md-4 text-center">
                        <div class="privacy-score-circle">
                            <div class="privacy-score-number" id="privacy-score">--</div>
                        </div>
                        <h5 class="mb-0">{% trans "Privacy Score" %}</h5>
                    </div>
                    <div class="col-md-8">
                        <h4 id="privacy-level-title">{% trans "Loading..." %}</h4>
                        <p id="privacy-level-description" class="mb-3">
                            {% trans "Analyzing your privacy settings..." %}
                        </p>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-light btn-sm" onclick="privacyDashboard.showImpactAnalysis()">
                                <i class="fas fa-chart-line me-1"></i>
                                {% trans "View Impact" %}
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" onclick="privacyDashboard.showRecommendations()">
                                <i class="fas fa-lightbulb me-1"></i>
                                {% trans "Get Recommendations" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Privacy Presets -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>
                            {% trans "Quick Privacy Presets" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-3">
                            {% trans "Choose a preset to quickly configure your privacy settings." %}
                        </p>
                        <div id="privacy-presets">
                            <!-- Presets will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Privacy Settings -->
            <div class="col-lg-8 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            {% trans "Privacy Settings" %}
                        </h5>
                        <button type="button" class="btn btn-primary btn-sm" onclick="privacyDashboard.saveSettings()">
                            <i class="fas fa-save me-1"></i>
                            {% trans "Save Changes" %}
                        </button>
                    </div>
                    <div class="card-body position-relative">
                        <div class="loading-overlay d-none" id="settings-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">{% trans "Loading..." %}</span>
                            </div>
                        </div>
                        
                        <div id="privacy-settings-form">
                            <!-- Settings form will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Privacy Impact Analysis -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            {% trans "Privacy Impact" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="privacy-impact">
                            <!-- Impact analysis will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Data Usage Summary -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            {% trans "Data Usage" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="data-usage-summary">
                            <!-- Data usage summary will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Privacy Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            {% trans "Privacy Recommendations" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="privacy-recommendations">
                            <!-- Recommendations will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Impact Modal -->
<div class="modal fade" id="privacyImpactModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "Privacy Impact Analysis" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="detailed-impact-analysis">
                    <!-- Detailed impact analysis will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Privacy Dashboard JavaScript -->
<script src="{% static 'js/privacy-dashboard.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize privacy dashboard
    window.privacyDashboard = new PrivacyDashboard({
        dashboardSelector: '.privacy-dashboard',
        scoreSelector: '#privacy-score-section',
        presetsSelector: '#privacy-presets',
        settingsSelector: '#privacy-settings-form',
        impactSelector: '#privacy-impact',
        recommendationsSelector: '#privacy-recommendations',
        dataUsageSelector: '#data-usage-summary'
    });
});
</script>
{% endblock %}
