{% load static %}
{% load i18n %}

<!-- Customer Profile Preview Component -->
<div class="profile-preview-card">
    <div class="card">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-eye me-2"></i>
                {% trans "Profile Preview" %}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Profile Picture Section -->
                <div class="col-md-3 text-center">
                    <div class="profile-picture-preview mb-3">
                        {% if profile.profile_picture %}
                            <img src="{{ profile.profile_picture.url }}" 
                                 alt="{% trans 'Profile Picture' %}" 
                                 class="rounded-circle profile-preview-image">
                        {% else %}
                            <div class="profile-placeholder rounded-circle">
                                <i class="fas fa-user fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if profile.is_verified %}
                        <div class="verification-badge">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                {% trans "Verified" %}
                            </span>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Profile Information Section -->
                <div class="col-md-9">
                    <div class="profile-info">
                        <!-- Name Section -->
                        <div class="name-section mb-3">
                            <h5 class="profile-name mb-1">
                                {% if profile.first_name or profile.last_name %}
                                    {{ profile.first_name }} {{ profile.last_name }}
                                {% else %}
                                    <span class="text-muted">{% trans "Your Name" %}</span>
                                {% endif %}
                            </h5>
                            <p class="profile-email text-muted mb-0">{{ user.email }}</p>
                        </div>
                        
                        <!-- Contact Information -->
                        <div class="contact-info mb-3">
                            <div class="row">
                                {% if profile.phone_number %}
                                    <div class="col-sm-6">
                                        <div class="info-item">
                                            <i class="fas fa-phone text-primary me-2"></i>
                                            <span>{{ profile.phone_number }}</span>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                {% if profile.gender %}
                                    <div class="col-sm-6">
                                        <div class="info-item">
                                            <i class="fas fa-user text-primary me-2"></i>
                                            <span>{{ profile.get_gender_display }}</span>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Address Information -->
                        {% if profile.address or profile.city or profile.zip_code %}
                            <div class="address-info mb-3">
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <span>
                                        {% if profile.address %}{{ profile.address }}{% endif %}
                                        {% if profile.city %}
                                            {% if profile.address %}, {% endif %}{{ profile.city }}
                                        {% endif %}
                                        {% if profile.zip_code %}
                                            {% if profile.address or profile.city %} {% endif %}{{ profile.zip_code }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Birth Information -->
                        {% if profile.birth_month or profile.birth_year %}
                            <div class="birth-info mb-3">
                                <div class="info-item">
                                    <i class="fas fa-birthday-cake text-primary me-2"></i>
                                    <span>
                                        {% if profile.birth_month %}{{ profile.birth_month_name }}{% endif %}
                                        {% if profile.birth_year %}
                                            {% if profile.birth_month %} {% endif %}{{ profile.birth_year }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Privacy Settings Preview -->
                        <div class="privacy-info">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                {% trans "Profile Visibility:" %} 
                                <span class="fw-bold">{{ profile.get_profile_visibility_display|default:"Public" }}</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Profile Completion Progress -->
            <div class="completion-progress mt-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-muted">{% trans "Profile Completion" %}</small>
                    <small class="completion-percentage fw-bold">{{ profile.profile_completion_percentage|default:0 }}%</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-success" 
                         role="progressbar" 
                         style="width: {{ profile.profile_completion_percentage|default:0 }}%"
                         aria-valuenow="{{ profile.profile_completion_percentage|default:0 }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="quick-stats mt-3">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-value">{{ user.date_joined|date:"M Y" }}</div>
                            <div class="stat-label text-muted small">{% trans "Member Since" %}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-value">
                                {% if profile.is_verified %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% else %}
                                    <i class="fas fa-clock text-warning"></i>
                                {% endif %}
                            </div>
                            <div class="stat-label text-muted small">{% trans "Verification" %}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-value">{{ profile.profile_completion_percentage|default:0 }}%</div>
                            <div class="stat-label text-muted small">{% trans "Complete" %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {% if is_preview %}
            <div class="card-footer bg-light">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "This is a preview of how your profile will appear to others." %}
                </small>
            </div>
        {% endif %}
    </div>
</div>

<style>
.profile-preview-card .profile-preview-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.profile-preview-card .profile-placeholder {
    width: 80px;
    height: 80px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.profile-preview-card .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.profile-preview-card .info-item i {
    width: 20px;
    text-align: center;
}

.profile-preview-card .verification-badge {
    margin-top: 0.5rem;
}

.profile-preview-card .stat-item {
    padding: 0.5rem;
}

.profile-preview-card .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.profile-preview-card .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-preview-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.profile-preview-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Drag and drop styles */
.drag-over {
    border: 2px dashed #007bff !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
}
</style>
