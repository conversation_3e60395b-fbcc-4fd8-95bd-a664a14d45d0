{% extends "base.html" %}
{% load static %}

{% block title %}Security Validation Failed - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .csrf-failure-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 2rem;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .csrf-failure-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        background: #dc3545;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
    }
    
    .csrf-failure-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .csrf-failure-message {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }
    
    .csrf-failure-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 2rem;
        text-align: left;
    }
    
    .csrf-failure-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary {
        background: #42241A;
        border: 1px solid #42241A;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: white;
        color: #42241A;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: white;
        border: 1px solid #42241A;
        color: #42241A;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #42241A;
        color: white;
        text-decoration: none;
    }
    
    .security-tips {
        margin-top: 2rem;
        padding: 1rem;
        background: #e8f4fd;
        border-radius: 8px;
        text-align: left;
    }
    
    .security-tips h4 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }
    
    .security-tips ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #525252;
    }
    
    .security-tips li {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="csrf-failure-container">
    <div class="csrf-failure-icon">
        <i class="fas fa-shield-alt"></i>
    </div>
    
    <h1 class="csrf-failure-title">Security Validation Failed</h1>
    
    <div class="csrf-failure-message">
        <p>We detected a security issue with your request. This could be due to:</p>
        <ul style="text-align: left; display: inline-block;">
            <li>An expired security token</li>
            <li>Multiple browser tabs or windows</li>
            <li>A browser extension interfering with the page</li>
            <li>Network connectivity issues</li>
        </ul>
    </div>
    
    {% if failure_count > 1 %}
    <div class="csrf-failure-details">
        <strong>Multiple Security Failures Detected:</strong>
        <p>This is the {{ failure_count|ordinal }} security validation failure from your location in the past hour. 
        If you continue to experience issues, please contact support.</p>
    </div>
    {% endif %}
    
    <div class="csrf-failure-actions">
        <a href="javascript:history.back()" class="btn-secondary">
            <i class="fas fa-arrow-left"></i> Go Back
        </a>
        <a href="javascript:location.reload()" class="btn-primary">
            <i class="fas fa-sync-alt"></i> Refresh Page
        </a>
        <a href="{% url 'dashboard_app:dashboard' %}" class="btn-secondary">
            <i class="fas fa-home"></i> Dashboard
        </a>
    </div>
    
    <div class="security-tips">
        <h4><i class="fas fa-lightbulb"></i> Security Tips</h4>
        <ul>
            <li>Always use the latest version of your browser</li>
            <li>Avoid using multiple tabs for the same account</li>
            <li>Disable browser extensions that modify web pages</li>
            <li>Ensure you have a stable internet connection</li>
            <li>Clear your browser cache if problems persist</li>
        </ul>
    </div>
</div>

<script>
// Auto-refresh after 30 seconds if user doesn't take action
setTimeout(function() {
    if (confirm('Would you like to refresh the page automatically?')) {
        location.reload();
    }
}, 30000);

// Add CSRF token to all future AJAX requests
document.addEventListener('DOMContentLoaded', function() {
    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        // Set up CSRF token for jQuery AJAX (if jQuery is available)
        if (window.jQuery) {
            jQuery.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", csrfToken.getAttribute('content'));
                    }
                }
            });
        }
        
        // Set up CSRF token for fetch API
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method)) {
                options.headers = options.headers || {};
                options.headers['X-CSRFToken'] = csrfToken.getAttribute('content');
            }
            return originalFetch(url, options);
        };
    }
});
</script>
{% endblock %}
