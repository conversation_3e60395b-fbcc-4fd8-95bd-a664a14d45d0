import bleach
import html
import re
import json
import urllib.parse
from typing import Any, Dict, List, Optional, Union

# Allowed HTML tags for rich text content
ALLOWED_TAGS = [
    'b', 'i', 'u', 'em', 'strong', 'br', 'ul', 'ol', 'li', 'p', 'a', 'span'
]

# Allowed attributes for HTML tags
ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    'span': ['class'],
}

# Tags that should be completely removed (including their content)
DANGEROUS_TAGS = [
    'script', 'style', 'iframe', 'object', 'embed', 'form', 'input',
    'button', 'textarea', 'select', 'option', 'meta', 'link', 'base'
]

# Dangerous protocols in URLs
DANGEROUS_PROTOCOLS = [
    'javascript:', 'data:', 'vbscript:', 'file:', 'ftp:', 'jar:', 'chrome:'
]

# SQL injection patterns
SQL_INJECTION_PATTERNS = [
    r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
    r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
    r'(\b(OR|AND)\s+[\'"]?\w+[\'"]?\s*=\s*[\'"]?\w+[\'"]?)',
    r'(--|\#|\/\*|\*\/)',
    r'(\bxp_cmdshell\b)',
    r'(\bsp_executesql\b)',
]

# XSS patterns
XSS_PATTERNS = [
    r'<script[^>]*>.*?</script>',
    r'javascript:',
    r'on\w+\s*=',
    r'<iframe[^>]*>.*?</iframe>',
    r'<object[^>]*>.*?</object>',
    r'<embed[^>]*>.*?</embed>',
]


def sanitize_html(value: str, strict: bool = False) -> str:
    """
    Sanitize user provided HTML input with comprehensive XSS protection.

    Args:
        value: HTML string to sanitize
        strict: If True, applies stricter sanitization

    Returns:
        Sanitized HTML string
    """
    if not value:
        return value

    # Convert to string if not already
    value = str(value)

    # First, remove dangerous tags and their content completely
    for tag in DANGEROUS_TAGS:
        pattern = f'<{tag}[^>]*>.*?</{tag}>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)
        # Also remove self-closing dangerous tags
        pattern = f'<{tag}[^>]*/?>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE)

    # Remove dangerous event handlers
    value = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', value, flags=re.IGNORECASE)

    # Remove javascript: and data: URLs
    for protocol in DANGEROUS_PROTOCOLS:
        value = re.sub(f'{protocol}[^\\s"\']*', '', value, flags=re.IGNORECASE)

    # Apply strict mode if requested
    if strict:
        # Remove all HTML tags in strict mode
        value = re.sub(r'<[^>]+>', '', value)
        return html.escape(value)

    # Use bleach for comprehensive cleaning
    cleaned = bleach.clean(
        value,
        tags=ALLOWED_TAGS,
        attributes=ALLOWED_ATTRIBUTES,
        strip=True,
        strip_comments=True
    )

    # Additional XSS pattern removal
    for pattern in XSS_PATTERNS:
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)

    return cleaned


def sanitize_text(value: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize plain text input to prevent XSS attacks.

    Args:
        value: Text string to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized text string
    """
    if not value:
        return value

    # Convert to string if not already
    value = str(value)

    # Remove null bytes and other control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)

    # Escape HTML entities to prevent XSS
    sanitized = html.escape(sanitized)

    # Remove potential XSS patterns
    for pattern in XSS_PATTERNS:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)

    # Normalize whitespace
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()

    # Apply length limit if specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length].strip()

    return sanitized


def sanitize_email(value: str) -> str:
    """
    Sanitize email input with comprehensive validation.

    Args:
        value: Email string to sanitize

    Returns:
        Sanitized email string

    Raises:
        ValueError: If email format is invalid
    """
    if not value:
        return value

    # Convert to string and normalize
    value = str(value).strip().lower()

    # Remove dangerous characters
    value = re.sub(r'[<>"\'\x00-\x1F\x7F]', '', value)

    # Basic email format validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, value):
        raise ValueError("Invalid email format")

    # Check for suspicious patterns
    suspicious_patterns = [
        r'javascript:',
        r'<script',
        r'data:',
        r'vbscript:',
    ]

    for pattern in suspicious_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            raise ValueError("Email contains suspicious content")

    return value


def sanitize_phone(value: str) -> str:
    """
    Sanitize phone number input.

    Args:
        value: Phone number string to sanitize

    Returns:
        Sanitized phone number string
    """
    if not value:
        return value

    # Convert to string
    value = str(value)

    # Remove all non-digit characters except + at the beginning
    sanitized = re.sub(r'[^\d+]', '', value)

    # Ensure + is only at the beginning
    if '+' in sanitized:
        parts = sanitized.split('+')
        sanitized = '+' + ''.join(parts[1:])

    # Remove any remaining non-digit characters except leading +
    sanitized = re.sub(r'(?<!^)\+', '', sanitized)

    return sanitized


def detect_sql_injection(value: str) -> bool:
    """
    Detect potential SQL injection attempts.

    Args:
        value: String to check for SQL injection patterns

    Returns:
        True if potential SQL injection detected
    """
    if not value:
        return False

    value = str(value).lower()

    # Check for SQL injection patterns
    for pattern in SQL_INJECTION_PATTERNS:
        if re.search(pattern, value, re.IGNORECASE):
            return True

    return False


def detect_xss_attempt(value: str) -> bool:
    """
    Detect potential XSS attempts.

    Args:
        value: String to check for XSS patterns

    Returns:
        True if potential XSS detected
    """
    if not value:
        return False

    value = str(value).lower()

    # Check for XSS patterns
    for pattern in XSS_PATTERNS:
        if re.search(pattern, value, re.IGNORECASE):
            return True

    return False


def sanitize_json_input(value: Union[str, Dict, List]) -> Union[str, Dict, List]:
    """
    Sanitize JSON input to prevent injection attacks.

    Args:
        value: JSON data to sanitize

    Returns:
        Sanitized JSON data
    """
    if isinstance(value, str):
        try:
            # Try to parse as JSON
            parsed = json.loads(value)
            return sanitize_json_input(parsed)
        except json.JSONDecodeError:
            # Not valid JSON, treat as string
            return sanitize_text(value)

    elif isinstance(value, dict):
        sanitized = {}
        for key, val in value.items():
            # Sanitize both keys and values
            clean_key = sanitize_text(str(key), max_length=100)
            clean_val = sanitize_json_input(val)
            sanitized[clean_key] = clean_val
        return sanitized

    elif isinstance(value, list):
        return [sanitize_json_input(item) for item in value]

    else:
        # For other types (int, float, bool, None), return as-is
        return value


def sanitize_search_query(value: str) -> str:
    """
    Sanitize search query input to prevent injection attacks.

    Args:
        value: Search query string to sanitize

    Returns:
        Sanitized search query string
    """
    if not value:
        return value

    # Convert to string
    value = str(value)

    # Remove SQL injection patterns
    if detect_sql_injection(value):
        # Log the attempt
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"SQL injection attempt detected in search query: {value}")

        # Remove dangerous patterns
        for pattern in SQL_INJECTION_PATTERNS:
            value = re.sub(pattern, '', value, flags=re.IGNORECASE)

    # Remove XSS patterns
    if detect_xss_attempt(value):
        # Log the attempt
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"XSS attempt detected in search query: {value}")

        value = sanitize_html(value, strict=True)

    # Normalize whitespace and limit length
    value = re.sub(r'\s+', ' ', value).strip()
    value = value[:500]  # Limit search queries to 500 characters

    return value


def validate_file_upload(file_obj, allowed_types: List[str] = None, max_size: int = None) -> Dict[str, Any]:
    """
    Comprehensive file upload validation.

    Args:
        file_obj: Uploaded file object
        allowed_types: List of allowed MIME types
        max_size: Maximum file size in bytes

    Returns:
        Dictionary with validation results
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'file_info': {}
    }

    if not file_obj:
        result['valid'] = False
        result['errors'].append('No file provided')
        return result

    # Get file info
    result['file_info'] = {
        'name': file_obj.name,
        'size': file_obj.size,
        'content_type': getattr(file_obj, 'content_type', None),
    }

    # Validate file size
    if max_size and file_obj.size > max_size:
        result['valid'] = False
        result['errors'].append(f'File size ({file_obj.size} bytes) exceeds maximum allowed ({max_size} bytes)')

    # Validate file type
    if allowed_types and result['file_info']['content_type'] not in allowed_types:
        result['valid'] = False
        result['errors'].append(f'File type {result["file_info"]["content_type"]} not allowed')

    # Validate filename
    if file_obj.name:
        sanitized_name = sanitize_filename(file_obj.name)
        if sanitized_name != file_obj.name:
            result['warnings'].append('Filename was sanitized')
            result['file_info']['sanitized_name'] = sanitized_name

    # Check for suspicious file content (basic check)
    try:
        file_obj.seek(0)
        content_sample = file_obj.read(1024)  # Read first 1KB
        file_obj.seek(0)  # Reset file pointer

        # Check for executable signatures
        executable_signatures = [
            b'\x4d\x5a',  # PE executable
            b'\x7f\x45\x4c\x46',  # ELF executable
            b'\xca\xfe\xba\xbe',  # Mach-O executable
            b'#!/bin/',  # Shell script
            b'#!/usr/bin/',  # Shell script
        ]

        for signature in executable_signatures:
            if content_sample.startswith(signature):
                result['valid'] = False
                result['errors'].append('Executable file detected')
                break

        # Check for script content in non-script files
        if result['file_info']['content_type'] and 'image' in result['file_info']['content_type']:
            script_patterns = [b'<script', b'javascript:', b'<?php']
            for pattern in script_patterns:
                if pattern in content_sample.lower():
                    result['valid'] = False
                    result['errors'].append('Script content detected in image file')
                    break

    except Exception as e:
        result['warnings'].append(f'Could not analyze file content: {str(e)}')

    return result


def sanitize_filename(value: str) -> str:
    """Sanitize filename to prevent path traversal attacks."""
    if not value:
        return value
    
    # Remove dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', value)
    
    # Remove path traversal attempts
    sanitized = sanitized.replace('..', '')
    
    # Limit length
    sanitized = sanitized[:255]
    
    return sanitized.strip()


def sanitize_url(value: str) -> str:
    """Sanitize URL input to prevent XSS and malicious redirects."""
    if not value:
        return value
    
    # Basic URL validation
    if not value.startswith(('http://', 'https://', 'ftp://', 'mailto:')):
        # Prepend https:// if no scheme provided
        value = f'https://{value}'
    
    # Remove dangerous schemes
    dangerous_schemes = ['javascript:', 'data:', 'vbscript:', 'file:']
    for scheme in dangerous_schemes:
        if value.lower().startswith(scheme):
            return ''
    
    return html.escape(value)
