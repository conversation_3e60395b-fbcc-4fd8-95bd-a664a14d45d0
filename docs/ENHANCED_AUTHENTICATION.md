# Enhanced Authentication System Documentation

## Overview

The CozyWish platform features a comprehensive enhanced authentication system built on top of Django Allauth, providing advanced security features, improved user experience, and robust session management.

## Features

### 1. Enhanced Email Verification Workflow

#### Features
- **Custom Email Templates**: Beautiful HTML and text email templates with CozyWish branding
- **Resend Functionality**: Users can resend verification emails with rate limiting (5 minutes cooldown)
- **Status Tracking**: Real-time verification status with auto-refresh
- **Custom Success/Error Pages**: Role-based next steps and helpful error resolution

#### Implementation
- **Views**: `EmailVerificationStatusView`, `ResendVerificationEmailView`, `EmailVerificationSuccessView`, `EmailVerificationErrorView`
- **Templates**: `email_verification_status.html`, `email_verification_success.html`, `email_verification_error.html`
- **Rate Limiting**: 3 resend attempts per 5 minutes per user
- **Security**: All verification requests are logged for security monitoring

#### Usage
```python
# Check verification status
GET /accounts/email/verification/status/

# Resend verification email
POST /accounts/email/verification/resend/

# AJAX status check
GET /accounts/email/verification/check/
```

### 2. Advanced Account Recovery Flows

#### Features
- **Enhanced Password Reset**: Security notifications, IP tracking, and enhanced templates
- **Account Recovery**: Manual recovery process for locked or compromised accounts
- **Security Questions**: Framework for additional verification (future enhancement)
- **Account Unlock**: Automated unlock request system

#### Implementation
- **Views**: `EnhancedPasswordResetView`, `AccountRecoveryView`, `AccountUnlockRequestView`
- **Forms**: `EnhancedPasswordResetForm`, `AccountRecoveryForm`, `SecurityQuestionForm`
- **Security**: All recovery attempts are logged and monitored for suspicious patterns

#### Usage
```python
# Enhanced password reset
POST /accounts/password/reset/enhanced/

# Account recovery request
POST /accounts/recovery/

# Account unlock request
POST /accounts/unlock/request/
```

### 3. Enhanced Session Management

#### Features
- **Session Tracking**: Detailed metadata including device, location, and activity
- **Remember Me**: Extended sessions with secure cookie handling
- **Timeout Warnings**: JavaScript-based warnings before session expiry
- **Multiple Device Management**: View and terminate sessions across devices
- **Security Monitoring**: Real-time suspicious activity detection

#### Models
```python
class UserSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_key = models.CharField(max_length=40, unique=True)
    device_name = models.CharField(max_length=200, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    is_remember_me = models.BooleanField(default=False)
    expires_at = models.DateTimeField(null=True, blank=True)
    # ... additional fields

class SessionSecurityEvent(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    is_suspicious = models.BooleanField(default=False)
    # ... additional fields
```

#### Usage
```python
# View active sessions
GET /accounts/sessions/

# Terminate specific session
POST /accounts/sessions/terminate/

# Extend current session
POST /accounts/sessions/extend/

# Check session status (AJAX)
GET /accounts/sessions/status/
```

### 4. Social Account Management System

#### Features
- **Account Linking/Unlinking**: Secure connection and disconnection of social accounts
- **Data Synchronization**: Sync profile information from social accounts
- **Conflict Resolution**: Handle duplicate emails and account conflicts
- **Provider Management**: Support for Google, Facebook, Twitter, LinkedIn

#### Implementation
- **Views**: `SocialAccountManagementView`, `UnlinkSocialAccountView`, `SyncSocialAccountDataView`
- **Forms**: `SocialAccountLinkForm`, `SocialAccountSyncForm`, `SocialAccountConflictResolutionForm`
- **Security**: Prevents account lockout by ensuring alternative authentication methods

#### Usage
```python
# Manage social accounts
GET /accounts/social/

# Unlink social account
POST /accounts/social/unlink/

# Sync account data
POST /accounts/social/sync/

# Resolve conflicts
GET /accounts/social/conflict/<conflict_id>/
```

### 5. Security Enhancements and Rate Limiting

#### Features
- **Comprehensive Rate Limiting**: IP-based and user-based rate limiting
- **Bot Detection**: Automatic detection and handling of bot requests
- **Security Headers**: CSP, XSS protection, and other security headers
- **Request Fingerprinting**: Unique fingerprints for security analysis
- **Suspicious Activity Detection**: Pattern recognition for security threats

#### Middleware
```python
MIDDLEWARE = [
    # ... other middleware
    'accounts_app.middleware.security_enhancements.SecurityEnhancementMiddleware',
    'accounts_app.middleware.security_enhancements.CSRFEnhancementMiddleware',
    'accounts_app.middleware.session_management.EnhancedSessionMiddleware',
    'accounts_app.middleware.session_management.ConcurrentSessionMiddleware',
    'accounts_app.middleware.security_enhancements.LoginAttemptTrackingMiddleware',
]
```

#### Rate Limits
- **Login**: 5 attempts per 5 minutes per IP
- **Signup**: 3 signups per hour per IP
- **Password Reset**: 3 resets per hour per IP
- **Email Verification**: 3 resends per 5 minutes per user

## Configuration

### Settings

```python
# Enhanced Email Verification Settings
EMAIL_VERIFICATION_RESEND_COOLDOWN = 300  # 5 minutes
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 1
ACCOUNT_EMAIL_CONFIRMATION_HMAC = True

# Enhanced Security Settings
MAX_CONCURRENT_SESSIONS = 5
SESSION_TIMEOUT_WARNING_MINUTES = 5
SECURITY_PASSWORD_MIN_LENGTH = 8

# Rate Limiting Settings
RATELIMIT_ENABLE = True
RATELIMIT_USE_CACHE = 'default'

# Session Security
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
```

### Email Templates

The system includes custom email templates for:
- Email verification (`email_confirmation_message.html`)
- Password reset (`password_reset_key_message.html`)
- Security notifications (`security_notification.html`)
- Account recovery confirmations

## Security Features

### 1. Password Security
- Minimum 8 characters
- Requires uppercase, lowercase, numbers, and special characters
- Strength checking with real-time feedback
- Protection against common passwords

### 2. Session Security
- Secure cookie configuration
- Session timeout warnings
- Concurrent session limits
- Device and location tracking
- Automatic cleanup of expired sessions

### 3. Rate Limiting
- Progressive delays for failed attempts
- IP-based and user-based limiting
- Bot detection and handling
- Graceful error pages

### 4. Monitoring and Logging
- Comprehensive security event logging
- Suspicious activity detection
- Failed login attempt tracking
- Security alert notifications

## API Endpoints

### Authentication Status
```javascript
// Check email verification status
fetch('/accounts/email/verification/check/')
  .then(response => response.json())
  .then(data => {
    if (data.verified) {
      // Handle verified state
    }
  });

// Check session status
fetch('/accounts/sessions/status/')
  .then(response => response.json())
  .then(data => {
    console.log('Session expires in:', data.expires_in, 'seconds');
  });
```

### Session Management
```javascript
// Extend session
fetch('/accounts/sessions/extend/', {
  method: 'POST',
  headers: {
    'X-CSRFToken': csrfToken,
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'hours=2'
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Session extended');
  }
});
```

## Testing

### Running Tests
```bash
# Run all enhanced authentication tests
python manage.py test accounts_app.tests.test_enhanced_authentication

# Run specific test cases
python manage.py test accounts_app.tests.test_enhanced_authentication.EnhancedEmailVerificationTestCase
python manage.py test accounts_app.tests.test_enhanced_authentication.SessionManagementTestCase
```

### Test Coverage
- Email verification workflow
- Account recovery flows
- Session management
- Social account management
- Security utilities
- Middleware functionality
- Integration tests

## Migration Guide

### From Basic Authentication

1. **Run Migrations**:
   ```bash
   python manage.py makemigrations accounts_app
   python manage.py migrate
   ```

2. **Update Settings**: Add the new middleware and settings as shown above

3. **Update Templates**: The new templates are backward compatible

4. **Test Functionality**: Run the test suite to ensure everything works

### Backward Compatibility

The enhanced authentication system is fully backward compatible with existing:
- User accounts and data
- Authentication flows
- Template structure
- URL patterns

## Troubleshooting

### Common Issues

1. **Rate Limiting Too Aggressive**:
   - Adjust rate limits in settings
   - Check cache configuration
   - Review IP detection logic

2. **Session Management Issues**:
   - Verify middleware order
   - Check session backend configuration
   - Review cookie settings

3. **Email Verification Problems**:
   - Verify email backend configuration
   - Check template paths
   - Review SMTP settings

### Debug Mode

Enable debug logging for authentication:
```python
LOGGING = {
    'loggers': {
        'accounts_app': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

## Future Enhancements

- Two-factor authentication (2FA)
- Biometric authentication support
- Advanced fraud detection
- Machine learning-based security
- OAuth2 provider functionality
- Single Sign-On (SSO) integration
