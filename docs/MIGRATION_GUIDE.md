# Enhanced Authentication Migration Guide

## Overview

This guide walks you through migrating to the enhanced authentication system for CozyWish. The migration is designed to be backward compatible and non-disruptive to existing users.

## Pre-Migration Checklist

### 1. Backup Database
```bash
# Create a full database backup
python manage.py dumpdata > backup_before_auth_migration.json

# Or use your database-specific backup tools
pg_dump cozy_wish_db > backup_before_auth_migration.sql  # PostgreSQL
mysqldump cozy_wish_db > backup_before_auth_migration.sql  # MySQL
```

### 2. Review Current Configuration
- Check existing authentication settings
- Review middleware configuration
- Verify email backend setup
- Test current authentication flows

### 3. Dependencies Check
```bash
# Ensure all required packages are installed
pip install django-ratelimit  # If not already installed
pip install user-agents       # For device detection
pip install requests          # For IP geolocation
```

## Migration Steps

### Step 1: Update Settings

Add the new settings to your `settings.py`:

```python
# Enhanced Email Verification Settings
EMAIL_VERIFICATION_RESEND_COOLDOWN = 300  # 5 minutes in seconds
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 1  # Email confirmations expire after 1 day
ACCOUNT_EMAIL_CONFIRMATION_HMAC = True  # Use HMAC for email confirmation tokens

# Enhanced Security Settings
MAX_CONCURRENT_SESSIONS = 5  # Maximum concurrent sessions per user
SESSION_TIMEOUT_WARNING_MINUTES = 5  # Minutes before session timeout to show warning
SECURITY_PASSWORD_MIN_LENGTH = 8  # Minimum password length
SECURITY_PASSWORD_REQUIRE_UPPERCASE = True  # Require uppercase letters
SECURITY_PASSWORD_REQUIRE_LOWERCASE = True  # Require lowercase letters
SECURITY_PASSWORD_REQUIRE_NUMBERS = True  # Require numbers
SECURITY_PASSWORD_REQUIRE_SYMBOLS = True  # Require special characters

# Rate Limiting Settings
RATELIMIT_ENABLE = True  # Enable rate limiting
RATELIMIT_USE_CACHE = 'default'  # Cache backend for rate limiting

# Security Logging
SECURITY_LOG_FAILED_LOGINS = True  # Log failed login attempts
SECURITY_LOG_SUSPICIOUS_ACTIVITY = True  # Log suspicious activity
SECURITY_ALERT_EMAIL = '<EMAIL>'  # Email for security alerts

# Session Security
SESSION_COOKIE_SECURE = not DEBUG  # Use secure cookies in production
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookies
SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
CSRF_COOKIE_SECURE = not DEBUG  # Use secure CSRF cookies in production
CSRF_COOKIE_HTTPONLY = True  # Prevent JavaScript access to CSRF cookies
```

### Step 2: Update Middleware

Update your middleware configuration:

```python
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'admin_app.middleware.AdminCSPMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'accounts_app.middleware.security_enhancements.SecurityEnhancementMiddleware',  # NEW
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'accounts_app.middleware.security_enhancements.CSRFEnhancementMiddleware',  # NEW
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'accounts_app.middleware.session_management.EnhancedSessionMiddleware',  # NEW
    'accounts_app.middleware.session_management.ConcurrentSessionMiddleware',  # NEW
    'accounts_app.middleware.session_management.RememberMeMiddleware',  # NEW
    'accounts_app.middleware.security_enhancements.LoginAttemptTrackingMiddleware',  # NEW
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'dashboard_app.middleware.DashboardAccessMiddleware',
]
```

### Step 3: Create and Run Migrations

```bash
# Create migrations for new models
python manage.py makemigrations accounts_app

# Review the migration files
ls accounts_app/migrations/

# Apply migrations
python manage.py migrate accounts_app
```

### Step 4: Update URL Configuration

The new URLs are automatically included in the accounts_app URLs. Verify they're working:

```bash
# Test URL resolution
python manage.py shell
>>> from django.urls import reverse
>>> reverse('accounts_app:email_verification_status')
>>> reverse('accounts_app:session_management')
>>> reverse('accounts_app:social_account_management')
```

### Step 5: Update Templates (Optional)

The new templates are backward compatible, but you may want to customize them:

1. **Email Templates**: Located in `templates/allauth/account/email/`
2. **Authentication Pages**: Located in `templates/allauth/account/`
3. **Management Pages**: Located in `templates/accounts/`

### Step 6: Test Migration

Run the test suite to ensure everything is working:

```bash
# Run enhanced authentication tests
python manage.py test accounts_app.tests.test_enhanced_authentication

# Run all authentication-related tests
python manage.py test accounts_app

# Test specific functionality
python manage.py test accounts_app.tests.test_enhanced_authentication.EnhancedEmailVerificationTestCase
```

## Post-Migration Tasks

### 1. Create Session Records for Existing Users

Run this management command to create session records for currently active sessions:

```python
# Create a management command: accounts_app/management/commands/migrate_sessions.py
from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from accounts_app.models import UserSession
from accounts_app.utils.session_utils import get_device_info

class Command(BaseCommand):
    help = 'Migrate existing sessions to new session management system'
    
    def handle(self, *args, **options):
        migrated = 0
        for session in Session.objects.all():
            session_data = session.get_decoded()
            user_id = session_data.get('_auth_user_id')
            
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    
                    # Create UserSession if it doesn't exist
                    user_session, created = UserSession.objects.get_or_create(
                        session_key=session.session_key,
                        defaults={
                            'user': user,
                            'device_name': 'Migrated Session',
                            'is_active': True,
                            'expires_at': session.expire_date,
                        }
                    )
                    
                    if created:
                        migrated += 1
                        
                except User.DoesNotExist:
                    continue
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully migrated {migrated} sessions')
        )
```

Run the command:
```bash
python manage.py migrate_sessions
```

### 2. Configure Email Templates

Update your email configuration to use the new templates:

```python
# In settings.py, ensure email backend is configured
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'your-smtp-host'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
DEFAULT_FROM_EMAIL = 'CozyWish <<EMAIL>>'
```

### 3. Set Up Security Monitoring

Configure logging for security events:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'security_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/security.log',
        },
    },
    'loggers': {
        'accounts_app.middleware.security_enhancements': {
            'handlers': ['security_file'],
            'level': 'INFO',
            'propagate': True,
        },
        'accounts_app.utils.security_utils': {
            'handlers': ['security_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 4. Configure Cache for Rate Limiting

Ensure your cache is properly configured:

```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## Verification Steps

### 1. Test Email Verification
1. Create a new user account
2. Check that verification email is sent
3. Test resend functionality
4. Verify rate limiting works

### 2. Test Session Management
1. Log in from multiple devices/browsers
2. Check session management page
3. Test session termination
4. Verify timeout warnings

### 3. Test Security Features
1. Attempt multiple failed logins
2. Check rate limiting kicks in
3. Verify security events are logged
4. Test password strength requirements

### 4. Test Social Account Management
1. Link a social account
2. Test data synchronization
3. Test account unlinking
4. Verify conflict resolution

## Rollback Plan

If you need to rollback the migration:

### 1. Disable New Middleware
Comment out the new middleware in settings:

```python
MIDDLEWARE = [
    # ... keep only original middleware
    # Comment out all accounts_app.middleware entries
]
```

### 2. Restore Database
```bash
# Restore from backup
python manage.py loaddata backup_before_auth_migration.json

# Or restore database dump
psql cozy_wish_db < backup_before_auth_migration.sql  # PostgreSQL
mysql cozy_wish_db < backup_before_auth_migration.sql  # MySQL
```

### 3. Remove New Settings
Remove the enhanced authentication settings from `settings.py`.

## Troubleshooting

### Common Issues

1. **Migration Fails**:
   - Check database permissions
   - Ensure all dependencies are installed
   - Review migration files for conflicts

2. **Middleware Errors**:
   - Check middleware order
   - Verify all imports are available
   - Review error logs

3. **Rate Limiting Too Aggressive**:
   - Adjust rate limit settings
   - Check cache configuration
   - Review IP detection logic

4. **Session Issues**:
   - Verify session backend
   - Check cookie settings
   - Review middleware configuration

### Getting Help

If you encounter issues:

1. Check the logs in `logs/security.log`
2. Run the test suite to identify problems
3. Review the documentation in `docs/ENHANCED_AUTHENTICATION.md`
4. Contact the development team

## Performance Considerations

### Database Indexes

The migration creates appropriate indexes, but monitor performance:

```sql
-- Check index usage
EXPLAIN ANALYZE SELECT * FROM accounts_user_sessions WHERE user_id = 1 AND is_active = true;

-- Monitor session cleanup performance
EXPLAIN ANALYZE DELETE FROM accounts_user_sessions WHERE expires_at < NOW() AND is_active = false;
```

### Cache Usage

Monitor cache usage for rate limiting:

```python
# Check cache stats
from django.core.cache import cache
cache.get_stats()  # If using memcached
```

### Session Cleanup

Set up a cron job to clean up expired sessions:

```bash
# Add to crontab
0 2 * * * /path/to/venv/bin/python /path/to/project/manage.py cleanup_expired_sessions
```

## Success Metrics

After migration, monitor these metrics:

1. **Security Events**: Track failed login attempts and suspicious activity
2. **Session Management**: Monitor active sessions and cleanup efficiency
3. **User Experience**: Track email verification completion rates
4. **Performance**: Monitor response times for authentication endpoints
5. **Error Rates**: Track authentication-related errors

The migration is complete when all tests pass and these metrics show healthy values.
